const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  eslint: {
    ignoreDuringBuilds: true,
  },
  // Add output configuration for more stable builds
  output: 'standalone',
  webpack: (config, { isServer }) => {
    // Add support for Relay
    config.module.rules.push({
      test: /\.(graphql|gql)$/,
      exclude: /node_modules/,
      use: [
        {
          loader: 'graphql-tag/loader'
        }
      ]
    });

    // Handle __generated__ directory
    config.resolve.alias = {
      ...config.resolve.alias,
      '@/__generated__': path.resolve(__dirname, '__generated__'),
    };

    return config;
  },
  compiler: {
    relay: {
      src: './app',
      artifactDirectory: './__generated__',
      language: 'typescript',
      eagerEsModules: true
    }
  },
  images: {
    domains: ['images.unsplash.com'],
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        port: '',
        pathname: '/**',
      },
    ],
  },
}

module.exports = nextConfig
