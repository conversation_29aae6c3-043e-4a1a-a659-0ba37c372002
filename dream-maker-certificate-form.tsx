"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage, FormDescription } from "@/app/components/ui/form"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Calendar } from "@/app/components/ui/calendar"
import { Calendar as CalendarIcon } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/app/lib/utils"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover"

const formSchema = z.object({
  date: z.date({
    required_error: "Please select a date.",
  }),
})

type FormData = z.infer<typeof formSchema>

export function DreamMakerCertificateForm() {
  const [status, setStatus] = useState<{ type: "success" | "error"; message: string } | null>(null)

  const form = useForm<FormData>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      date: undefined,
    },
  })

  async function onSubmit(data: FormData) {
    try {
      // For now, just show a mock success message
      setStatus({
        type: "success",
        message: `Successfully processed certificates for ${format(data.date, "MM/dd/yyyy")}`,
      })
      form.reset() // Reset form after successful submission
    } catch (error) {
      setStatus({
        type: "error",
        message: "An error occurred while processing the request",
      })
    }
  }

  return (
    <Card className="w-full rounded-none border-none">
      <CardHeader className="border-b">
        <CardTitle className="text-lg font-medium">Certificate Generation</CardTitle>
        <CardDescription>
          Select a date to generate and send certificates to qualified agents
        </CardDescription>
      </CardHeader>
      <CardContent className="p-6">
        <div className="space-y-6">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
              <div className="max-w-sm">
                <FormField
                  control={form.control}
                  name="date"
                  render={({ field }) => (
                    <FormItem className="flex flex-col">
                      <FormLabel>Completion Date</FormLabel>
                      <Popover>
                        <PopoverTrigger asChild>
                          <FormControl>
                            <Button
                              variant={"outline"}
                              className={cn(
                                "w-full pl-3 text-left font-normal",
                                !field.value && "text-muted-foreground"
                              )}
                            >
                              {field.value ? (
                                format(field.value, "MM/dd/yyyy")
                              ) : (
                                <span>Pick a date</span>
                              )}
                              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                            </Button>
                          </FormControl>
                        </PopoverTrigger>
                        <PopoverContent className="w-auto p-0" align="start">
                          <Calendar
                            mode="single"
                            selected={field.value}
                            onSelect={field.onChange}
                            disabled={(date) => {
                              const oneMonthAgo = new Date()
                              oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
                              return date < oneMonthAgo || date > new Date()
                            }}
                            initialFocus
                          />
                        </PopoverContent>
                      </Popover>
                      <FormDescription className="text-xs text-muted-foreground">
                        Select the date when Dream Maker attendees were uploaded into the system.
                        Only dates within the last month are available.
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
              
              <div className="flex justify-end">
                <Button 
                  type="submit" 
                  disabled={form.formState.isSubmitting}
                  className="min-w-[150px]"
                >
                  {form.formState.isSubmitting ? "Processing..." : "Send Certificates"}
                </Button>
              </div>
            </form>
          </Form>

          {status && (
            <Alert 
              className={cn(
                "mt-4",
                status.type === "error" 
                  ? "border-destructive/50 text-destructive" 
                  : "border-green-500/50 text-green-700"
              )}
            >
              <AlertDescription>{status.message}</AlertDescription>
            </Alert>
          )}
        </div>
      </CardContent>
    </Card>
  )
} 