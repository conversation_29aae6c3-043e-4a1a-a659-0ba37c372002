"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"

export function useTransactionSearch() {
  const searchParams = useSearchParams()
  const [transactions, setTransactions] = useState([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchTransactions() {
      setIsLoading(true)
      try {
        // Replace with your actual API endpoint
        const response = await fetch(`/api/transactions?${searchParams.toString()}`)
        const data = await response.json()
        setTransactions(data)
      } catch (error) {
        console.error("Error fetching transactions:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchTransactions()
  }, [searchParams])

  return { transactions, isLoading }
} 