"use client"

import { useSearchParams, useRouter } from "next/navigation"
import { useCallback } from "react"
import { createQueryString } from "@/app/lib/utils"

export function useTransactionFilters() {
  const searchParams = useSearchParams()
  const router = useRouter()

  const filters = {
    dateRange: {
      from: searchParams.get("from") || "",
      to: searchParams.get("to") || "",
    },
    status: searchParams.get("status") || "all",
    minAmount: searchParams.get("minAmount") || "",
    maxAmount: searchParams.get("maxAmount") || "",
  }

  const setFilter = useCallback(
    (key: string, value: any) => {
      const newQueryString = createQueryString(
        searchParams,
        { [key]: value === null ? null : value }
      )
      router.push(`?${newQueryString}`)
    },
    [router, searchParams]
  )

  return { filters, setFilter }
} 