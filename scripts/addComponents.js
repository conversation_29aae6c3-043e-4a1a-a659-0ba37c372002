const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Function to download component from shadcn-ui GitHub
async function addComponent(componentName) {
  const componentsDir = path.join(process.cwd(), 'components', 'ui');
  
  if (!fs.existsSync(componentsDir)) {
    fs.mkdirSync(componentsDir, { recursive: true });
  }

  const baseUrl = 'https://raw.githubusercontent.com/shadcn/ui/main/apps/www/registry/default/ui';
  const fileName = `${componentName}.tsx`;
  const url = `${baseUrl}/${fileName}`;

  try {
    execSync(`curl -o ${path.join(componentsDir, fileName)} ${url}`);
    console.log(`✅ Added ${componentName} component`);
  } catch (error) {
    console.error(`❌ Failed to add ${componentName}:`, error);
  }
}

// Get component name from command line argument
const componentName = process.argv[2];
if (!componentName) {
  console.error('Please specify a component name');
  process.exit(1);
}

addComponent(componentName);