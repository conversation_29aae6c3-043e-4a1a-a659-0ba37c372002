import { printSchema, getIntrospectionQuery, buildClientSchema } from 'graphql';
import fs from 'fs';
import path from 'path';

async function fetchSchema() {
  try {
    const response = await fetch('http://localhost:4000/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: getIntrospectionQuery(),
      }),
    });

    const { data } = await response.json();
    const schemaPath = path.join(process.cwd(), 'schema.graphql');
    fs.writeFileSync(schemaPath, printSchema(buildClientSchema(data)));
    console.log('Schema updated successfully');
  } catch (error) {
    console.error('Error fetching schema:', error);
  }
}

fetchSchema(); 