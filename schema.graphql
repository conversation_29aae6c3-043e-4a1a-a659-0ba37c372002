schema {
  query: RootQuery
}

"""Exposes a URL that specifies the behavior of this scalar."""
directive @specifiedBy(
  """The URL that specifies the behavior of this scalar."""
  url: String!
) on SCALAR

"""
Indicates exactly one field must be supplied and this field must not be `null`.
"""
directive @oneOf on INPUT_OBJECT

type Booking {
  iBookingID: Int
  vcPIN: String
  vcFreeUserID: String
  nEngineID: Int
  nBookingTypeID: Int
  vcConfirmationID: String
  nBookingStatusID: Int
  vcClientName: String
  vcLiftCity: String
  vcFinalDestination: String
  nNumberOfPsgrs: Int
  vcGDS: String
  vcVendor: String
  vcVendorAddress: String
  vcVendorCityState: String
  vcVendorPostalCode: String
  vcVendorNumber: String
  vcVendorFax: String
  dBooked: String
  dTravelStartDate: String
  dTravelEndDate: String
  nTotalCharges: Float
  nAdminFee: Float
  nCommissionRate: Float
  nTotalCommission: Float
  bIATANQual: Int
  nInteleTravelCommission: Float
  nAgentCommission: Float
  nVendorCheckID: Int
  vcVendorCheckNum: String
  dVendorPaid: String
  bInTheBank: Int
  vcITCheckNumber: String
  dAgentPaid: String
  vcCancelConfirmation: String
  txtComments: String
  nCheckRunID: Int
  dCheckRunDate: String
  bActive: Int
  dModified: String
  dCreated: String
  nUserModifiedID: Int
  nUserCreatedID: Int
  dPending: String
  bNonCommissionable: Int
  dECS: String
  nOrderID: Int
  nSegmentID: Int
  totalChargesCurrency: String
  nSupplierID: Int
  nDepositAmount: Float
  nProcessingFee: Float
  vcconfirmationidnew: String
  nSalesContactID: Int
  vcVendorID: Int
  vcTripID: String
}

type PaginatedBookings {
  items: [Booking]
  total: Int
  page: Int
  pageSize: Int
  totalPages: Int
}

type RootQuery {
  getAllBookings(page: Int = 1, pageSize: Int = 10, iBookingID: Int, vcPIN: String, vcConfirmationID: String, vcClientName: String, nEngineID: Int, vcFreeUserID: String, vcFinalDestination: String, vcTripID: String, vcVendorID: String, nSalesContactID: Int, vcconfirmationidnew: String, dTravelStartDate: String, dTravelEndDate: String, dTravelStartDateFrom: String, dTravelStartDateTo: String, dTravelEndDateFrom: String, dTravelEndDateTo: String): PaginatedBookings
  getBooking(page: Int = 1, pageSize: Int = 10, iBookingID: Int, nSupplierID: Int, nBookingStatusID: Int, vcConfirmationID: String, vcPIN: String, vcTripID: String, vcFreeUserID: String, nBookingTypeID: Int, bNonCommissionable: Int, bInTheBank: Int, vcGDS: String, nEngineID: Int, bActive: Int, nUserCreatedID: Int, nUserModifiedID: Int): PaginatedBookings
  searchBookings(page: Int!, pageSize: Int!, filters: BookingFiltersInput): BookingConnection!
}

input BookingFiltersInput {
  iBookingID: FilterOperator
  vcConfirmationID: FilterOperator
  dTravelStartDate: FilterOperator
  nBookingStatusID: FilterOperator
}

enum ComparisonOperator {
  eq
  gt
  lt
  gte
  lte
  contains
}

input FilterOperator {
  operator: ComparisonOperator!
  value: Any
}

scalar Any

type BookingConnection {
  items: [Booking!]!
  total: Int!
  totalPages: Int!
  page: Int!
  pageSize: Int!
}

