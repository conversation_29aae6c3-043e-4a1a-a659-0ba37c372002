const { createClient } = require('@supabase/supabase-js');
const { SupabaseClient } = require('@supabase/supabase-js');

interface TestContext {
  [key: string]: unknown;
}

interface TestResults {
  numTotalTests: number;
  numPassedTests: number;
  numFailedTests: number;
  startTime?: number;
}

class SupabaseReporter {
  private supabase: typeof SupabaseClient | null;
  private isInitialized: boolean;

  constructor() {
    this.supabase = null;
    this.isInitialized = false;

    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

    if (!supabaseUrl || !supabaseKey) {
      console.warn('Supabase credentials not found. Test reporting will be disabled.');
      return;
    }

    this.supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false
      }
    });
    this.isInitialized = true;
  }

  async onTestResult(_test: TestContext, testResult: { testResults: Array<{
    testFilePath: string;
    fullName?: string;
    title?: string;
    status: string;
    duration?: number;
    failureMessages?: string[];
  }> }) {
    if (!this.isInitialized || !this.supabase) return;

    try {
      const results = testResult.testResults.map(result => ({
        testFilePath: result.testFilePath,
        testName: result.fullName || result.title || '',
        status: result.status,
        duration: result.duration || 0,
        errorMessage: result.failureMessages?.join('\n'),
        timestamp: new Date().toISOString(),
        branch: process.env.GITHUB_REF_NAME || 'local',
        commitHash: process.env.GITHUB_SHA || 'local'
      }));

      // Send results to Supabase in batches
      const batchSize = 10;
      for (let i = 0; i < results.length; i += batchSize) {
        const batch = results.slice(i, i + batchSize);
        const { error } = await this.supabase
          .from('test_results')
          .insert(batch);

        if (error) {
          console.error('Error sending test results to Supabase:', error);
        }
      }
    } catch (error) {
      console.error('Error processing test results:', error);
    }
  }

  onRunComplete(_contexts: TestContext, results: TestResults) {
    if (!this.isInitialized) return;

    console.log('\nTest Run Summary:');
    console.log('================');
    console.log(`Total Tests: ${results.numTotalTests}`);
    console.log(`Passed Tests: ${results.numPassedTests}`);
    console.log(`Failed Tests: ${results.numFailedTests}`);
    console.log(`Test Duration: ${results.startTime ? (Date.now() - results.startTime) / 1000 : 0}s`);
  }
}

module.exports = SupabaseReporter; 