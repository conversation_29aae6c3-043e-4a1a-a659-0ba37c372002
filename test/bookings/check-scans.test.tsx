import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import CheckScansPage from '../../app/bookings/check-scans/page'

// Mock the components used in the page
jest.mock('../../app/components/check-scans/check-scans-search', () => ({
  CheckScansSearch: jest.fn(() => <div data-testid="check-scans-search">Check Scans Search Form</div>),
}))

jest.mock('../../app/components/check-scans/check-scans-table', () => ({
  CheckScansTable: jest.fn(() => <div data-testid="check-scans-table">Check Scans Table</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

jest.mock('../../app/components/check-scans/upload-check-scan-modal', () => ({
  UploadCheckScanModal: jest.fn(({ isOpen }) => 
    isOpen ? <div data-testid="upload-modal">Upload Modal</div> : null
  ),
}))

describe('CheckScansPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the Check Scans page with all components', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    expect(screen.getByText('Check Scans')).toBeInTheDocument()
    
    // Check for "Checks Uploaded Today" button
    expect(screen.getByText('Checks Uploaded Today')).toBeInTheDocument()
    
    // Check for filter section
    expect(screen.getByText('Search Filters')).toBeInTheDocument()
    
    // Check for filter form
    expect(screen.getByTestId('check-scans-search')).toBeInTheDocument()
    
    // Check for table
    expect(screen.getByTestId('check-scans-table')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<CheckScansPage searchParams={{}} />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('h-full')
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('space-y-8')
    expect(mainContainer).toHaveClass('p-8')
    expect(mainContainer).toHaveClass('md:flex')
  })
  
  it('renders the page title with correct styling', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    const pageTitle = screen.getByText('Check Scans')
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-bold')
    expect(pageTitle).toHaveClass('tracking-tight')
  })
  
  it('renders the filter section with action buttons', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    // Check for filter action buttons
    expect(screen.getByText('Hide')).toBeInTheDocument()
    expect(screen.getByText('Clear')).toBeInTheDocument()
    expect(screen.getByText('Search')).toBeInTheDocument()
  })
  
  it('toggles the filter form visibility when Hide/Show button is clicked', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    // Initially the form should be visible
    expect(screen.getByTestId('check-scans-search')).toBeInTheDocument()
    
    // Click the Hide button
    fireEvent.click(screen.getByText('Hide'))
    
    // Now the form should be hidden
    expect(screen.queryByTestId('check-scans-search')).not.toBeInTheDocument()
    
    // The button text should change to "Show"
    expect(screen.getByText('Show')).toBeInTheDocument()
    
    // Click the Show button
    fireEvent.click(screen.getByText('Show'))
    
    // The form should be visible again
    expect(screen.getByTestId('check-scans-search')).toBeInTheDocument()
    
    // The button text should change back to "Hide"
    expect(screen.getByText('Hide')).toBeInTheDocument()
  })
  
  it('renders the search button with correct styling', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    const searchButton = screen.getByText('Search').closest('button')
    expect(searchButton).toHaveClass('bg-[#3C36A9]')
    expect(searchButton).toHaveClass('text-white')
  })
  
  it('passes searchParams to the CheckScansTable component', () => {
    const mockSearchParams = { query: 'test' }
    render(<CheckScansPage searchParams={mockSearchParams} />)
    
    // This is a bit limited since we're mocking the component,
    // but in a real test we'd verify the props are passed correctly
    expect(screen.getByTestId('check-scans-table')).toBeInTheDocument()
  })
  
  it('renders the components in the correct order', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Check Scans')
    const filterSection = screen.getByText('Search Filters').closest('div')
    const table = screen.getByTestId('check-scans-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(filterSection!)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(filterSection!.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('has the "Checks Uploaded Today" button with correct styling', () => {
    render(<CheckScansPage searchParams={{}} />)
    
    const todayButton = screen.getByText('Checks Uploaded Today')
    expect(todayButton).toHaveClass('text-[#3C36A9]')
  })
}) 