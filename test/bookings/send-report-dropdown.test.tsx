import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { SendReportDropdown } from '../../app/bookings/large-bookings/components/send-report-dropdown'

// Mock the useToast hook
jest.mock('../../app/components/ui/use-toast', () => ({
  useToast: jest.fn(() => ({ toast: jest.fn() })),
}))

// Mock the Radix UI dropdown components
jest.mock('../../app/components/ui/dropdown-menu', () => {
  return {
    DropdownMenu: ({ children }) => <div>{children}</div>,
    DropdownMenuTrigger: ({ children }) => <div data-testid="dropdown-trigger">{children}</div>,
    DropdownMenuContent: ({ children }) => <div data-testid="dropdown-content">{children}</div>,
    DropdownMenuItem: ({ children, onClick }) => (
      <button data-testid="dropdown-item" onClick={onClick}>
        {children}
      </button>
    ),
    DropdownMenuLabel: ({ children }) => <div data-testid="dropdown-label">{children}</div>,
    DropdownMenuSeparator: () => <hr />,
  }
})

describe('SendReportDropdown', () => {
  const mockBookings = [
    {
      vcPIN: '123456',
      vcDisplayName: 'Hotel/Resort',
      vcConfirmationID: '12345',
      vcClientName: 'John Doe',
      vcVendor: 'Marriott',
      booked: '2023-01-01',
      travelStartDate: '2023-06-01',
      travelEndDate: '2023-06-07',
      nTotalCharges: 15000,
      nTotalCommission: 10,
    },
  ]
  const mockFilters = { vendorName: 'Marriott' }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the dropdown button', () => {
    render(<SendReportDropdown bookings={mockBookings} filters={mockFilters} />)
    
    expect(screen.getByText('Send Report')).toBeInTheDocument()
  })
  
  it('renders dropdown content', () => {
    render(<SendReportDropdown bookings={mockBookings} filters={mockFilters} />)
    
    // With our mock, the content is always visible
    expect(screen.getByTestId('dropdown-label')).toBeInTheDocument()
    expect(screen.getByText('Send Report To')).toBeInTheDocument()
    
    // Check for staff members
    const dropdownItems = screen.getAllByTestId('dropdown-item')
    expect(dropdownItems.length).toBeGreaterThan(0)
    
    // Check for "Send to All Staff" option
    expect(screen.getByText('Send to All Staff')).toBeInTheDocument()
  })
  
  it('shows a toast when sending to a specific person', () => {
    const mockToast = jest.fn()
    require('../../app/components/ui/use-toast').useToast.mockReturnValue({ toast: mockToast })
    
    render(<SendReportDropdown bookings={mockBookings} filters={mockFilters} />)
    
    // Find the first staff member item and click it
    const staffItems = screen.getAllByTestId('dropdown-item')
    const firstStaffItem = staffItems.find(item => !item.textContent?.includes('Send to All'))
    firstStaffItem?.click()
    
    // Check that the toast was called
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Report would be sent',
        description: expect.stringContaining('would be sent'),
      })
    )
  })
  
  it('shows a toast when sending to all staff', () => {
    const mockToast = jest.fn()
    require('../../app/components/ui/use-toast').useToast.mockReturnValue({ toast: mockToast })
    
    render(<SendReportDropdown bookings={mockBookings} filters={mockFilters} />)
    
    // Find and click the "Send to All Staff" button
    const sendToAllButton = screen.getByText('Send to All Staff')
    sendToAllButton.click()
    
    // Check that the toast was called
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'Report would be sent to all',
        description: expect.stringContaining('all staff members'),
      })
    )
  })
  
  it('shows an error toast when there are no bookings', () => {
    const mockToast = jest.fn()
    require('../../app/components/ui/use-toast').useToast.mockReturnValue({ toast: mockToast })
    
    render(<SendReportDropdown bookings={[]} filters={mockFilters} />)
    
    // Find the first staff member item and click it
    const staffItems = screen.getAllByTestId('dropdown-item')
    const firstStaffItem = staffItems.find(item => !item.textContent?.includes('Send to All'))
    firstStaffItem?.click()
    
    // Check that the error toast was called
    expect(mockToast).toHaveBeenCalledWith(
      expect.objectContaining({
        title: 'No data to send',
        variant: 'destructive',
      })
    )
  })
}) 