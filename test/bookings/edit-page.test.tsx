import { render, screen } from '@testing-library/react'
import BookingEditPage from '../../app/bookings/[id]/edit/page'
import { BookingEditForm } from '../../app/components/booking-edit/booking-edit-form'
import { Breadcrumb } from '../../app/components/ui/breadcrumb'

// Mock the components used in the page
jest.mock('../../app/components/booking-edit/booking-edit-form', () => ({
  BookingEditForm: jest.fn(() => <div data-testid="booking-edit-form" />),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb" />),
}))

describe('BookingEditPage', () => {
  const mockParams = {
    id: '123',
  }

  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the page with correct title', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByText('Edit Booking #123')).toBeTruthy()
  })

  it('renders the breadcrumb component', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByTestId('breadcrumb')).toBeTruthy()
    expect(Breadcrumb).toHaveBeenCalled()
  })

  it('renders the booking edit form with correct booking ID', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByTestId('booking-edit-form')).toBeTruthy()
    expect(BookingEditForm).toHaveBeenCalledWith(
      { bookingId: 123 },
      expect.anything()
    )
  })

  it('parses the booking ID correctly from params', async () => {
    const testParams = { id: '456' }
    render(await BookingEditPage({ params: testParams }))
    
    expect(screen.getByText('Edit Booking #456')).toBeTruthy()
    expect(BookingEditForm).toHaveBeenCalledWith(
      { bookingId: 456 },
      expect.anything()
    )
  })

  it('handles non-numeric IDs gracefully', async () => {
    const testParams = { id: 'abc' }
    render(await BookingEditPage({ params: testParams }))
    
    // When parseInt encounters non-numeric strings, it returns NaN
    // The component should handle this gracefully
    expect(BookingEditForm).toHaveBeenCalledWith(
      { bookingId: NaN },
      expect.anything()
    )
  })
}) 