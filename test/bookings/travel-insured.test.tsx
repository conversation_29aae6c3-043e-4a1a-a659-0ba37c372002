import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TravelInsuredPage from '../../app/bookings/travel-insured/page'

// Mock the components used in the page
jest.mock('../../app/components/travel-insured/travel-insured-upload', () => ({
  TravelInsuredUpload: jest.fn(() => <div data-testid="travel-insured-upload">Travel Insured Upload Component</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('TravelInsuredPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the travel insured page correctly', () => {
    render(<TravelInsuredPage />)
    
    // Check for page title and description
    expect(screen.getByText('Travel Insured Upload')).toBeInTheDocument()
    expect(screen.getByText('Upload and process Travel Insured files')).toBeInTheDocument()
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('travel-insured-upload')).toBeInTheDocument()
  })
}) 