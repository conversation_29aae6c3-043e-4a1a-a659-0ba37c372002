import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import FreeUserBookingsPage from '../../app/bookings/free-user/page'
import { useRouter, useSearchParams } from 'next/navigation'

// Mock the next/navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock the components used in the page
jest.mock('../../app/components/booking-search/search-wrapper', () => ({
  SearchWrapper: jest.fn(() => <div data-testid="search-wrapper">Search Wrapper Component</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('FreeUserBookingsPage', () => {
  const mockPush = jest.fn()
  const mockSearchParams = new URLSearchParams()
  
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup router mock
    ;(useRouter as jest.Mock).mockReturnValue({
      push: mockPush,
    })
    
    // Setup search params mock
    ;(useSearchParams as jest.Mock).mockReturnValue({
      toString: () => mockSearchParams.toString(),
      get: (key: string) => mockSearchParams.get(key),
    })
  })

  it('renders the free user bookings page correctly', () => {
    render(<FreeUserBookingsPage searchParams={{}} />)
    
    expect(screen.getByText('Free User Bookings')).toBeInTheDocument()
    expect(screen.getByText('Enter a free user PIN to view their bookings')).toBeInTheDocument()
    expect(screen.getByLabelText('Free User PIN')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Search' })).toBeInTheDocument()
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('handles PIN input and form submission', () => {
    render(<FreeUserBookingsPage searchParams={{}} />)
    
    const pinInput = screen.getByLabelText('Free User PIN')
    const searchButton = screen.getByRole('button', { name: 'Search' })
    
    // Enter a PIN
    fireEvent.change(pinInput, { target: { value: '12345' } })
    expect(pinInput).toHaveValue('12345')
    
    // Submit the form
    fireEvent.click(searchButton)
    
    // Verify router.push was called with the correct URL
    expect(mockPush).toHaveBeenCalledWith(
      expect.stringContaining('vcFreeUserID=')
    )
    
    // Verify the URL contains the encoded PIN filter
    const pushUrl = mockPush.mock.calls[0][0]
    expect(pushUrl).toContain(encodeURIComponent(JSON.stringify({
      operator: 'equals',
      value: '12345'
    })))
    expect(pushUrl).toContain('page=1')
    expect(pushUrl).toContain('pageSize=10')
  })

  it('does not submit the form if PIN is empty', () => {
    render(<FreeUserBookingsPage searchParams={{}} />)
    
    const searchButton = screen.getByRole('button', { name: 'Search' })
    
    // Submit the form without entering a PIN
    fireEvent.click(searchButton)
    
    // Verify router.push was not called
    expect(mockPush).not.toHaveBeenCalled()
  })

  it('renders the SearchWrapper when vcFreeUserID is in search params', () => {
    // Setup search params with vcFreeUserID
    mockSearchParams.set('vcFreeUserID', JSON.stringify({ operator: 'equals', value: '12345' }))
    
    render(<FreeUserBookingsPage searchParams={{}} />)
    
    // Verify SearchWrapper is rendered
    expect(screen.getByTestId('search-wrapper')).toBeInTheDocument()
  })

  it('does not render the SearchWrapper when vcFreeUserID is not in search params', () => {
    // Clear search params
    mockSearchParams.delete('vcFreeUserID')
    
    render(<FreeUserBookingsPage searchParams={{}} />)
    
    // Verify SearchWrapper is not rendered
    expect(screen.queryByTestId('search-wrapper')).not.toBeInTheDocument()
  })
}) 