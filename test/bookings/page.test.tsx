import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import BookingsPage from '../../app/bookings/page'
import { ClientSearch } from '../../app/components/client-search'
import { SearchWrapper } from '../../app/components/booking-search/search-wrapper'
import { Breadcrumb } from '../../app/components/ui/breadcrumb'

// Mock the child components
jest.mock('../../app/components/client-search', () => ({
  ClientSearch: jest.fn(() => <div data-testid="client-search" />),
}))

jest.mock('../../app/components/booking-search/search-wrapper', () => ({
  SearchWrapper: jest.fn(() => <div data-testid="search-wrapper" />),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb" />),
}))

describe('BookingsPage', () => {
  const mockSearchParams = { query: 'test-client', status: 'active' }
  
  beforeEach(() => {
    jest.clearAllMocks()
  })
  
  it('renders the page title correctly', () => {
    render(<BookingsPage searchParams={mockSearchParams} />)
    
    expect(screen.getByText('Booking Search')).toBeTruthy()
  })
  
  it('renders the search components', () => {
    render(<BookingsPage searchParams={mockSearchParams} />)
    
    expect(screen.getByTestId('client-search')).toBeTruthy()
    expect(screen.getByTestId('search-wrapper')).toBeTruthy()
  })
  
  it('renders the breadcrumb component', () => {
    render(<BookingsPage searchParams={mockSearchParams} />)
    
    expect(screen.getByTestId('breadcrumb')).toBeTruthy()
  })
  
  it('maintains correct component hierarchy', () => {
    const { container } = render(<BookingsPage searchParams={mockSearchParams} />)
    
    // Get the main container div
    const mainDiv = container.firstChild
    expect(mainDiv).toBeTruthy()
    
    // Check that the breadcrumb is the first child
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeTruthy()
    
    // Check that the heading div is present
    const headingText = screen.getByText('Booking Search')
    expect(headingText).toBeTruthy()
    
    // Check that the search components are present
    expect(screen.getByTestId('client-search')).toBeTruthy()
    expect(screen.getByTestId('search-wrapper')).toBeTruthy()
  })
  
  it('applies the correct layout classes', () => {
    const { container } = render(<BookingsPage searchParams={mockSearchParams} />)
    
    const mainDiv = container.firstChild as HTMLElement
    expect(mainDiv.classList.contains('h-full')).toBe(true)
    expect(mainDiv.classList.contains('flex-1')).toBe(true)
    expect(mainDiv.classList.contains('flex-col')).toBe(true)
    expect(mainDiv.classList.contains('space-y-8')).toBe(true)
    expect(mainDiv.classList.contains('p-8')).toBe(true)
    expect(mainDiv.classList.contains('md:flex')).toBe(true)
  })
  
  it('passes empty searchParams when none are provided', () => {
    render(<BookingsPage searchParams={{}} />)
    
    expect(SearchWrapper).toHaveBeenCalledWith(
      { searchParams: {} },
      expect.anything()
    )
  })
}) 