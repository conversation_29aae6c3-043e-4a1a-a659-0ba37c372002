import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import LargeBookingsPage from '../../app/bookings/large-bookings/page'

// Mock the components used in the page
jest.mock('../../app/bookings/large-bookings/components/large-bookings-container', () => ({
  LargeBookingsContainer: jest.fn(() => <div data-testid="large-bookings-container">Large Bookings Container</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

jest.mock('../../app/components/ui/toaster', () => ({
  Toaster: jest.fn(() => <div data-testid="toaster">Toaster Component</div>),
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react');
  return {
    ...originalReact,
    Suspense: ({ children }) => children,
  };
})

describe('LargeBookingsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the Large Bookings page correctly', () => {
    render(<LargeBookingsPage />)
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('large-bookings-container')).toBeInTheDocument()
    expect(screen.getByText('Large Bookings')).toBeInTheDocument()
    expect(screen.getByTestId('toaster')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<LargeBookingsPage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('w-full')
    expect(mainContainer).toHaveClass('p-6')
  })
  
  it('renders the page title with correct styling', () => {
    render(<LargeBookingsPage />)
    
    const pageTitle = screen.getByText('Large Bookings')
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
  })
  
  it('renders the breadcrumb before the page title', () => {
    render(<LargeBookingsPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Large Bookings')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('renders the page title before the container', () => {
    render(<LargeBookingsPage />)
    
    // Get the elements
    const pageTitle = screen.getByText('Large Bookings')
    const container = screen.getByTestId('large-bookings-container')
    
    // Check their order in the document
    expect(pageTitle.compareDocumentPosition(container)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('has proper spacing between components', () => {
    render(<LargeBookingsPage />)
    
    // Check that the title container has margin-top and padding-bottom
    const titleContainer = screen.getByText('Large Bookings').closest('div')
    expect(titleContainer).toHaveClass('mt-4')
    expect(titleContainer).toHaveClass('pb-6')
  })
  
  it('includes the Toaster component for notifications', () => {
    render(<LargeBookingsPage />)
    expect(screen.getByTestId('toaster')).toBeInTheDocument()
  })
}) 