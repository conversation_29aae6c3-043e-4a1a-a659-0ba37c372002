import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import MorBookingsPage from '../../app/bookings/mor-bookings/page'

// Mock the components used in the page
jest.mock('../../app/bookings/mor-bookings/components/mor-bookings-client', () => ({
  MorBookingsClient: jest.fn(() => (
    <div data-testid="mor-bookings-client">
      <h3 className="text-xl font-medium">MOR Bookings Report</h3>
      <p className="text-sm text-muted-foreground">
        View all MOR bookings and their details
      </p>
      MOR Bookings Client Component
    </div>
  )),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('MorBookingsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the MOR bookings page correctly', () => {
    render(<MorBookingsPage />)
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('mor-bookings-client')).toBeInTheDocument()
    
    // Check for heading and description in the client component
    expect(screen.getByText('MOR Bookings Report')).toBeInTheDocument()
    expect(screen.getByText('View all MOR bookings and their details')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<MorBookingsPage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('h-full')
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('space-y-8')
    expect(mainContainer).toHaveClass('p-8')
    expect(mainContainer).toHaveClass('md:flex')
  })
  
  it('renders the breadcrumb before the client component', () => {
    render(<MorBookingsPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const clientComponent = screen.getByTestId('mor-bookings-client')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(clientComponent)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('maintains consistent spacing between components', () => {
    const { container } = render(<MorBookingsPage />)
    
    // The main container should have space-y-8 class for consistent spacing
    expect(container.firstChild).toHaveClass('space-y-8')
    
    // The content container should have space-y-6 class
    const contentContainer = screen.getByTestId('mor-bookings-client').parentElement
    expect(contentContainer).toHaveClass('space-y-6')
  })
  
  // We don't need to test the metadata directly as it's handled by Next.js
  // and doesn't affect the rendered output in our test environment
}) 