import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import MexicanBookingsPage from '../../app/bookings/mexican-bookings/page'

// Mock the components used in the page
jest.mock('../../app/bookings/mexican-bookings/components/mexican-bookings-client', () => ({
  MexicanBookingsClient: jest.fn(() => <div data-testid="mexican-bookings-client">Mexican Bookings Client Component</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('MexicanBookingsPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the mexican bookings page correctly', () => {
    render(<MexicanBookingsPage />)
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('mexican-bookings-client')).toBeInTheDocument()
    
    // Check for heading and description
    expect(screen.getByText('Mexican Bookings Report')).toBeInTheDocument()
    expect(screen.getByText('Generate and view reports for Mexican bookings to be paid')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<MexicanBookingsPage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('h-full')
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('space-y-8')
    expect(mainContainer).toHaveClass('p-8')
    expect(mainContainer).toHaveClass('md:flex')
  })
  
  it('renders the breadcrumb before the content section', () => {
    render(<MexicanBookingsPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const heading = screen.getByText('Mexican Bookings Report')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(heading)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('maintains consistent spacing between components', () => {
    const { container } = render(<MexicanBookingsPage />)
    
    // The main container should have space-y-8 class for consistent spacing
    expect(container.firstChild).toHaveClass('space-y-8')
    
    // The content container should have space-y-6 class
    const contentContainer = screen.getByText('Mexican Bookings Report').parentElement?.parentElement
    expect(contentContainer).toHaveClass('space-y-6')
  })
  
  // We don't need to test the metadata directly as it's handled by Next.js
  // and doesn't affect the rendered output in our test environment
}) 