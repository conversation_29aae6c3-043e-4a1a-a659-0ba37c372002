import { render, screen } from '@testing-library/react'
import BookingEditPage from '../../app/bookings/[id]/edit/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb" />
}))

// Mock the BookingEditForm component
jest.mock('../../app/components/booking-edit/booking-edit-form', () => ({
  BookingEditForm: ({ bookingId }) => <div data-testid="booking-edit-form">Editing booking {bookingId}</div>
}))

describe('BookingEditPage Integration', () => {
  const mockParams = {
    id: '123',
  }

  it('renders the page with correct title', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByText('Edit Booking #123')).toBeTruthy()
  })

  it('renders the breadcrumb component', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByTestId('breadcrumb')).toBeTruthy()
  })

  it('passes the correct booking ID to the edit form', async () => {
    render(await BookingEditPage({ params: mockParams }))
    
    expect(screen.getByTestId('booking-edit-form')).toBeTruthy()
    expect(screen.getByText('Editing booking 123')).toBeTruthy()
  })
}) 