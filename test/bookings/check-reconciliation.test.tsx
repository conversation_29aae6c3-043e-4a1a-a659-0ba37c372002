import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import CheckReconciliationPage from '../../app/bookings/check-reconciliation/page'

// Mock the components used in the page
jest.mock('../../app/components/check-reconciliation/check-reconciliation-table', () => ({
  CheckReconciliationTable: jest.fn(() => <div data-testid="check-reconciliation-table">Check Reconciliation Table</div>),
}))

jest.mock('../../app/components/check-reconciliation/check-reconciliation-header', () => ({
  CheckReconciliationHeader: jest.fn(() => <div data-testid="check-reconciliation-header">Check Reconciliation Header</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('CheckReconciliationPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the Check Reconciliation page with all components', () => {
    render(<CheckReconciliationPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title and subtitle
    expect(screen.getByText('Check Reconciliation')).toBeInTheDocument()
    expect(screen.getByText('View and manage CTS vendor check reconciliation')).toBeInTheDocument()
    
    // Check for header component
    expect(screen.getByTestId('check-reconciliation-header')).toBeInTheDocument()
    
    // Check for table component
    expect(screen.getByTestId('check-reconciliation-table')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<CheckReconciliationPage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('h-full')
    expect(mainContainer).toHaveClass('flex-1')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('space-y-8')
    expect(mainContainer).toHaveClass('p-8')
    expect(mainContainer).toHaveClass('md:flex')
  })
  
  it('renders the page title with correct styling', () => {
    render(<CheckReconciliationPage />)
    
    const pageTitle = screen.getByText('Check Reconciliation')
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-bold')
    expect(pageTitle).toHaveClass('tracking-tight')
  })
  
  it('renders the subtitle with correct styling', () => {
    render(<CheckReconciliationPage />)
    
    const subtitle = screen.getByText('View and manage CTS vendor check reconciliation')
    expect(subtitle).toHaveClass('text-muted-foreground')
  })
  
  it('renders the components in the correct order', () => {
    render(<CheckReconciliationPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Check Reconciliation')
    const header = screen.getByTestId('check-reconciliation-header')
    const table = screen.getByTestId('check-reconciliation-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(header)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(header.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('maintains consistent spacing between components', () => {
    const { container } = render(<CheckReconciliationPage />)
    
    // Check that the main container has space-y-8 class for consistent spacing
    expect(container.firstChild).toHaveClass('space-y-8')
    
    // Check that the content container has space-y-4 class for consistent spacing
    const contentContainer = screen.getByTestId('check-reconciliation-header').parentElement
    expect(contentContainer).toHaveClass('space-y-4')
  })
  
  it('renders the header before the table', () => {
    render(<CheckReconciliationPage />)
    
    // Get the elements
    const header = screen.getByTestId('check-reconciliation-header')
    const table = screen.getByTestId('check-reconciliation-table')
    
    // Check their order in the document
    expect(header.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('has the correct DOM structure', () => {
    const { container } = render(<CheckReconciliationPage />)
    
    // Check the basic structure
    expect(container.firstChild).toBeInTheDocument()
    expect(container.firstChild?.firstChild).toBeInTheDocument() // Breadcrumb
    expect(container.firstChild?.childNodes.length).toBe(3) // Breadcrumb, title section, content section
  })
  
  it('has the title and subtitle in the same container', () => {
    render(<CheckReconciliationPage />)
    
    const title = screen.getByText('Check Reconciliation')
    const subtitle = screen.getByText('View and manage CTS vendor check reconciliation')
    
    // They should be siblings or have the same parent
    expect(title.parentElement).toBe(subtitle.parentElement)
  })
  
  it('has the header and table in the same container', () => {
    render(<CheckReconciliationPage />)
    
    const header = screen.getByTestId('check-reconciliation-header')
    const table = screen.getByTestId('check-reconciliation-table')
    
    // They should have the same parent
    expect(header.parentElement).toBe(table.parentElement)
  })
  
  it('renders the page with semantic HTML structure', () => {
    render(<CheckReconciliationPage />)
    
    // Check that the page title is in an h2 element
    const pageTitle = screen.getByText('Check Reconciliation')
    expect(pageTitle.tagName).toBe('H2')
  })
}) 