import { render } from '@testing-library/react'
import BookingEditPage from '../../app/bookings/[id]/edit/page'

// Mock the components
jest.mock('../../app/components/booking-edit/booking-edit-form', () => ({
  BookingEditForm: () => <div data-testid="booking-edit-form" />,
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb" />,
}))

describe('BookingEditPage Snapshot', () => {
  it('matches snapshot', async () => {
    const { container } = render(
      await BookingEditPage({ params: { id: '123' } })
    )
    expect(container).toMatchSnapshot()
  })
}) 