import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { LargeBookingsContainer } from '../../app/bookings/large-bookings/components/large-bookings-container'

// Mock the GraphQL client and queries
jest.mock('../../app/lib/graphql/client', () => ({
  graphqlClient: {
    request: jest.fn(),
  },
}))

jest.mock('../../app/lib/graphql/queries', () => ({
  LARGE_BOOKINGS_QUERY: 'mocked-query',
}))

// Mock Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(() => new URLSearchParams()),
  useRouter: jest.fn(() => ({ push: jest.fn() })),
  usePathname: jest.fn(() => '/bookings/large-bookings'),
}))

// Mock the components used in the container
jest.mock('../../app/bookings/large-bookings/components/large-bookings-table', () => ({
  LargeBookingsTable: jest.fn(({ bookings, isLoading }) => (
    <div data-testid="large-bookings-table">
      {isLoading ? 'Loading...' : `Table with ${bookings.length} bookings`}
    </div>
  )),
}))

jest.mock('../../app/bookings/large-bookings/components/large-bookings-filters', () => ({
  LargeBookingsFilters: jest.fn(({ onFilterChange }) => (
    <div data-testid="large-bookings-filters">
      <button 
        data-testid="apply-filters-button" 
        onClick={() => onFilterChange({ vendorName: 'Marriott', minTotalCharges: 15000 })}
      >
        Apply Filters
      </button>
    </div>
  )),
}))

jest.mock('../../app/bookings/large-bookings/components/send-report-dropdown', () => ({
  SendReportDropdown: jest.fn(() => (
    <div data-testid="send-report-dropdown">Send Report Dropdown</div>
  )),
}))

describe('LargeBookingsContainer', () => {
  const mockBookings = [
    {
      vcPIN: '123456',
      vcDisplayName: 'Hotel/Resort',
      vcConfirmationID: '12345',
      vcClientName: 'John Doe',
      vcVendor: 'Marriott',
      booked: '2023-01-01',
      travelStartDate: '2023-06-01',
      travelEndDate: '2023-06-07',
      nTotalCharges: 15000,
      nTotalCommission: 10,
    },
  ]

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Setup mock response
    const { graphqlClient } = require('../../app/lib/graphql/client')
    graphqlClient.request.mockResolvedValue({ largeBookings: mockBookings })
  })

  it('renders the container with all components', async () => {
    render(<LargeBookingsContainer />)
    
    // Check for components
    expect(screen.getByTestId('large-bookings-filters')).toBeInTheDocument()
    expect(screen.getByTestId('send-report-dropdown')).toBeInTheDocument()
    
    // Initially shows loading state
    expect(screen.getByText('Loading bookings...')).toBeInTheDocument()
    
    // Wait for data to load
    await waitFor(() => {
      expect(screen.getByText('1 bookings found')).toBeInTheDocument()
      expect(screen.getByText('Table with 1 bookings')).toBeInTheDocument()
    })
  })
  
  it('fetches data from GraphQL API on mount', async () => {
    const { graphqlClient } = require('../../app/lib/graphql/client')
    render(<LargeBookingsContainer />)
    
    await waitFor(() => {
      expect(graphqlClient.request).toHaveBeenCalledTimes(1)
    })
  })
  
  it('updates URL and refetches when filters are applied', async () => {
    const { useRouter } = require('next/navigation')
    const mockRouter = { push: jest.fn() }
    useRouter.mockReturnValue(mockRouter)
    
    render(<LargeBookingsContainer />)
    
    // Click the apply filters button
    fireEvent.click(screen.getByTestId('apply-filters-button'))
    
    // Check that router.push was called
    expect(mockRouter.push).toHaveBeenCalled()
  })
  
  it('handles API errors gracefully', async () => {
    const { graphqlClient } = require('../../app/lib/graphql/client')
    graphqlClient.request.mockRejectedValueOnce(new Error('API Error'))
    
    render(<LargeBookingsContainer />)
    
    // Wait for error message
    await waitFor(() => {
      expect(screen.getByText('Failed to load bookings. Please try again.')).toBeInTheDocument()
    })
  })
}) 