import { test, expect, type Page } from '@playwright/test'

test.describe('Bookings Page', () => {
  test('loads the bookings page correctly', async ({ page }: { page: Page }) => {
    // Navigate to the bookings page
    await page.goto('/bookings')
    
    // Verify the page title and description are visible
    await expect(page.locator('h2:has-text("Booking Search")')).toBeVisible()
    await expect(page.locator('p:has-text("Search and manage your bookings here")')).toBeVisible()
  })
  
  test('shows search components', async ({ page }: { page: Page }) => {
    await page.goto('/bookings')
    
    // Verify that the search components are rendered and visible
    // Note: These components should have data-testid attributes added to them
    await expect(page.locator('[data-testid="client-search"]')).toBeVisible()
    await expect(page.locator('[data-testid="search-wrapper"]')).toBeVisible()
  })
  
  test('maintains search parameters in URL', async ({ page }: { page: Page }) => {
    // Navigate to bookings with specific search parameters
    await page.goto('/bookings?query=test-client&status=active')
    
    // Verify that the UI reflects the URL parameters
    // This tests that the components correctly read from and display URL parameters
    await expect(page.locator('[data-testid="search-input"]')).toHaveValue('test-client')
    await expect(page.locator('[data-testid="status-filter"]')).toHaveValue('active')
  })
  
  test('updates URL when performing a search', async ({ page }: { page: Page }) => {
    await page.goto('/bookings')
    
    // Perform a search action
    await page.fill('[data-testid="search-input"]', 'new client')
    await page.click('[data-testid="search-button"]')
    
    // Verify the URL was updated with the search parameters
    await expect(page).toHaveURL(/query=new%20client/)
    
    // Verify the search results are displayed
    await expect(page.locator('[data-testid="search-results"]')).toBeVisible()
  })
  
  test('is responsive on mobile viewports', async ({ page }: { page: Page }) => {
    // Set viewport to mobile size to test responsive behavior
    await page.setViewportSize({ width: 375, height: 667 })
    await page.goto('/bookings')
    
    // Verify that the page layout adapts correctly for mobile
    // The md:flex class should be present but not active at mobile width
    await expect(page.locator('.md\\:flex')).toBeVisible()
    
    // Verify that all important elements are visible on mobile viewport
    await expect(page.locator('h2:has-text("Booking Search")')).toBeVisible()
    await expect(page.locator('[data-testid="client-search"]')).toBeVisible()
  })
  
  test('successfully fetches bookings data from GraphQL', async ({ page }: { page: Page }) => {
    // Intercept GraphQL requests to verify successful responses
    await page.route('**/graphql', async (route) => {
      const request = route.request();
      const postData = request.postDataJSON();
      
      // Only intercept GraphQL queries related to bookings
      if (postData.query && postData.query.includes('bookings')) {
        // Let the request continue but monitor the response
        const response = await route.fetch();
        const json = await response.json();
        
        // Verify the response structure before continuing
        expect(response.status()).toBe(200);
        expect(json).toHaveProperty('data');
        expect(json.data).toHaveProperty('bookings');
        expect(json.errors).toBeUndefined();
        
        // Continue with the original response
        await route.fulfill({ response });
      } else {
        // For other GraphQL queries, just let them through
        await route.continue();
      }
    });
    
    // Navigate to bookings page
    await page.goto('/bookings');
    
    // Wait for the table to be populated with data
    await page.waitForSelector('[data-testid="bookings-table"]');
    
    // Verify that booking records exist in the table
    const tableRows = await page.locator('[data-testid="bookings-table"] tbody tr').count();
    expect(tableRows).toBeGreaterThan(0);
    
    // Verify specific data elements are present in the table
    await expect(page.locator('[data-testid="bookings-table"] tbody tr:first-child')).toBeVisible();
    await expect(page.locator('[data-testid="bookings-table"] tbody tr:first-child td:first-child')).not.toBeEmpty();
  })
}) 