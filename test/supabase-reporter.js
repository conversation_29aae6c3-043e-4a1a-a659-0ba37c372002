const { createClient } = require('@supabase/supabase-js');
const path = require('path');

class SupabaseReporter {
  constructor() {
    this.supabase = null;
    this.isInitialized = false;

    const supabaseUrl = 'https://iwngkowmldjydpgkgtme.supabase.co';
    const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Iml3bmdrb3dtbGRqeWRwZ2tndG1lIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0MDY2NTU3NywiZXhwIjoyMDU2MjQxNTc3fQ.TesNv8iOEMv4luFPtid02D5udU-Y8zL5twhcJ-rEv9k';

    if (!supabaseUrl || !supabaseKey) {
      console.warn('Supabase credentials not found. Test reporting will be disabled.');
      return;
    }

    this.supabase = createClient(supabaseUrl, supabaseKey, {
      auth: {
        persistSession: false
      }
    });
    this.isInitialized = true;
  }

  async onTestResult(_test, testResult) {
    if (!this.isInitialized || !this.supabase) return;

    try {
      // Debug the structure of the test result objects
      console.log('_test parameter:', Object.keys(_test));
      console.log('testResult parameter:', Object.keys(testResult));
      console.log('Test file path from testResult:', testResult.testFilePath);
      
      const results = testResult.testResults.map((result, index) => {
        // Log the structure of each test result
        if (index === 0) {
          console.log('Sample test result structure:', Object.keys(result));
        }
        
        // Try to get the file path from various possible locations
        let filePath = '';
        
        if (result.testFilePath) {
          filePath = result.testFilePath;
        } else if (testResult.testFilePath) {
          filePath = testResult.testFilePath;
        } else if (_test.path) {
          filePath = _test.path;
        }
        
        // Get the relative path from the project root
        const relativePath = filePath ? path.relative(process.cwd(), filePath) : '';
        
        console.log(`Test #${index} file path:`, filePath);
        console.log(`Test #${index} relative path:`, relativePath);
        
        return {
          test_file_path: relativePath || 'unknown',
          test_name: result.fullName || result.title || 'unnamed test',
          status: result.status || 'unknown',
          duration: result.duration || 0,
          error_message: result.failureMessages?.join('\n') || '',
          timestamp: new Date().toISOString(),
          branch: process.env.GITHUB_REF_NAME || 'local',
          commit_hash: process.env.GITHUB_SHA || 'local'
        };
      });

      // Log the results for debugging
      console.log('Sending test results:', JSON.stringify(results, null, 2));

      // Send results to Supabase in batches
      const batchSize = 10;
      for (let i = 0; i < results.length; i += batchSize) {
        const batch = results.slice(i, i + batchSize);
        const { error } = await this.supabase
          .from('test_results')
          .insert(batch);

        if (error) {
          console.error('Error sending test results to Supabase:', error);
        } else {
          console.log(`Successfully sent batch ${i / batchSize + 1}`);
        }
      }
    } catch (error) {
      console.error('Error processing test results:', error);
    }
  }

  onRunComplete(_contexts, results) {
    if (!this.isInitialized) return;

    console.log('\nTest Run Summary:');
    console.log('================');
    console.log(`Total Tests: ${results.numTotalTests}`);
    console.log(`Passed Tests: ${results.numPassedTests}`);
    console.log(`Failed Tests: ${results.numFailedTests}`);
    console.log(`Test Duration: ${results.startTime ? (Date.now() - results.startTime) / 1000 : 0}s`);
  }
}

module.exports = SupabaseReporter; 