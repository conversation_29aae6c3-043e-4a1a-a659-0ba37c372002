import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import SuppliersPage from '../../app/suppliers/page'

// Mock the Next.js Link component
jest.mock('next/link', () => {
  return {
    __esModule: true,
    default: ({ href, children }: { href: string; children: React.ReactNode }) => (
      <a href={href} data-testid="next-link">
        {children}
      </a>
    ),
  }
})

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

describe('SuppliersPage Interactions', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Mock window.location.href for navigation testing
    Object.defineProperty(window, 'location', {
      value: {
        href: 'http://localhost/suppliers',
      },
      writable: true,
    })
  })

  it('navigates to add new supplier page when button is clicked', () => {
    render(<SuppliersPage />)
    
    const addButton = screen.getByRole('link', { name: /add new supplier/i })
    
    // Since we're using a mock for Link, we can't directly test navigation
    // But we can check that the link has the correct href
    expect(addButton).toHaveAttribute('href', '/suppliers/new')
  })

  it('has hover state class on table rows', () => {
    render(<SuppliersPage />)
    
    // Get the first row (excluding header)
    const rows = screen.getAllByRole('row')
    const firstDataRow = rows[1]
    
    // Check that the row has the hover class
    // Note: This is a bit tricky to test as the hover effect is CSS-based
    // We're checking for the class that should be applied
    expect(firstDataRow).toHaveClass('hover:bg-slate-50')
  })

  it('renders edit and delete buttons for each supplier', () => {
    render(<SuppliersPage />)
    
    // Get all edit buttons
    const editButtons = screen.getAllByRole('button', { name: /edit/i })
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
    
    // Verify buttons exist
    expect(editButtons[0]).toBeInTheDocument()
    expect(deleteButtons[0]).toBeInTheDocument()
  })
}) 