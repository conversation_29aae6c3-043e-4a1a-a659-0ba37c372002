import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import MorSuppliersPage from '../../app/suppliers/mor/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

// Mock the MorSuppliersForm component
jest.mock('../../app/suppliers/mor/components/mor-suppliers-form', () => ({
  MorSuppliersForm: jest.fn(() => <div data-testid="mor-suppliers-form">MOR Suppliers Form</div>),
}))

describe('MOR Suppliers Page', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the MOR suppliers page with correct title', () => {
    render(<MorSuppliersPage />)
    
    // Check for the page title
    expect(screen.getByText('MOR Suppliers')).toBeInTheDocument()
    
    // Check for the breadcrumb component
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('renders the MOR suppliers form', () => {
    render(<MorSuppliersPage />)
    
    // Check that the form component is rendered
    expect(screen.getByTestId('mor-suppliers-form')).toBeInTheDocument()
  })

  it('has the correct layout structure', () => {
    const { container } = render(<MorSuppliersPage />)
    
    // Check for the main container with proper padding
    const mainContainer = container.firstChild
    expect(mainContainer).toHaveClass('w-full p-6')
    
    // Check for the header section with proper spacing
    const headerSection = screen.getByText('MOR Suppliers').parentElement
    expect(headerSection).toHaveClass('flex items-center mt-4 justify-between pb-6')
  })

  it('displays the heading with correct styling', () => {
    render(<MorSuppliersPage />)
    
    // Check that the heading has the correct styling
    const heading = screen.getByRole('heading', { name: 'MOR Suppliers' })
    expect(heading).toHaveClass('text-2xl font-semibold')
  })
}) 