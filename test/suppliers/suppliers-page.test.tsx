import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import SuppliersPage from '../../app/suppliers/page'

// Mock the Next.js Link component
jest.mock('next/link', () => {
  return {
    __esModule: true,
    default: ({ href, children }: { href: string; children: React.ReactNode }) => (
      <a href={href} data-testid="next-link">
        {children}
      </a>
    ),
  }
})

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

describe('SuppliersPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the suppliers page with correct title', () => {
    render(<SuppliersPage />)
    
    expect(screen.getByText('InteleTravel Suppliers')).toBeInTheDocument()
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('displays the Add New Supplier button with correct link', () => {
    render(<SuppliersPage />)
    
    const addButton = screen.getByRole('link', { name: /add new supplier/i })
    expect(addButton).toBeInTheDocument()
    expect(addButton).toHaveAttribute('href', '/suppliers/new')
  })

  it('renders the suppliers table with correct headers', () => {
    render(<SuppliersPage />)
    
    // Check table headers
    const headers = ['ID', 'Supplier Name', 'Type', 'Status', 'Last Updated', 'Created By', 'Actions']
    headers.forEach(header => {
      expect(screen.getByRole('columnheader', { name: header })).toBeInTheDocument()
    })
  })

  it('displays the correct number of suppliers', () => {
    render(<SuppliersPage />)
    
    // There should be 4 suppliers in the sample data
    const rows = screen.getAllByRole('row')
    // +1 for the header row
    expect(rows.length).toBe(5)
  })

  it('displays supplier data correctly', () => {
    render(<SuppliersPage />)
    
    // Check first supplier data
    expect(screen.getByText('Global Travel Corp')).toBeInTheDocument()
    expect(screen.getByText('Tour Operator')).toBeInTheDocument()
    expect(screen.getByText('2024-03-20')).toBeInTheDocument()
    
    // Use getAllByText for elements that appear multiple times
    const adminEmails = screen.getAllByText('<EMAIL>')
    expect(adminEmails.length).toBeGreaterThan(0)
    
    // Check status badges
    const statusBadges = screen.getAllByText('Active')
    expect(statusBadges.length).toBe(4) // All 4 suppliers are active
  })

  it('renders edit and delete buttons for each supplier', () => {
    render(<SuppliersPage />)
    
    // There should be 4 suppliers, each with Edit and Delete buttons
    const editButtons = screen.getAllByRole('button', { name: /edit/i })
    const deleteButtons = screen.getAllByRole('button', { name: /delete/i })
    
    expect(editButtons.length).toBe(4)
    expect(deleteButtons.length).toBe(4)
  })
}) 