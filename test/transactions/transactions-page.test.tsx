import { render, screen, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useSearchParams } from 'next/navigation'
import TransactionsPage from '../../app/transactions/page'
import { fetchTransactions } from '../../app/lib/api/transactions'

// Mock the next/navigation module
jest.mock('next/navigation', () => ({
  useSearchParams: jest.fn(),
}))

// Mock the API call - use relative path
jest.mock('../../app/lib/api/transactions', () => ({
  fetchTransactions: jest.fn(),
}))

// Mock the components - use relative paths
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

jest.mock('../../app/components/transactions/filters-wrapper', () => ({
  FiltersWrapper: () => <div data-testid="filters-wrapper">Filters</div>,
}))

jest.mock('../../app/components/transactions/transactions-table', () => ({
  TransactionsTable: ({ initialData }: { initialData: any }) => (
    <div data-testid="transactions-table">
      Transactions Table
      <span data-testid="transaction-count">{initialData.transactions.length}</span>
    </div>
  ),
}))

describe('TransactionsPage', () => {
  const mockSearchParams = new URLSearchParams()
  const mockTransactionsData = {
    transactions: [
      { id: '1', amount: 100, description: 'Test Transaction 1' },
      { id: '2', amount: 200, description: 'Test Transaction 2' },
    ],
    totalCount: 2,
    page: 1,
    limit: 10,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    ;(fetchTransactions as jest.Mock).mockResolvedValue(mockTransactionsData)
  })

  it('renders the transactions page with default pagination', async () => {
    render(await TransactionsPage({ searchParams: {} }))

    expect(screen.getByText('Transactions')).toBeInTheDocument()
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('filters-wrapper')).toBeInTheDocument()
    expect(screen.getByText('Transaction List')).toBeInTheDocument()
    expect(screen.getByTestId('transactions-table')).toBeInTheDocument()
    
    // Verify the API was called with default pagination
    expect(fetchTransactions).toHaveBeenCalledWith({ page: 1, limit: 10 })
  })

  it('passes custom pagination parameters to fetchTransactions', async () => {
    render(await TransactionsPage({ searchParams: { page: '2', limit: '20' } }))

    expect(fetchTransactions).toHaveBeenCalledWith({ page: 2, limit: 20 })
  })

  it('handles invalid pagination parameters gracefully', async () => {
    render(await TransactionsPage({ searchParams: { page: 'invalid', limit: 'invalid' } }))

    // Should fall back to defaults
    expect(fetchTransactions).toHaveBeenCalledWith({ page: 1, limit: 10 })
  })

  it('passes the transaction data to the TransactionsTable component', async () => {
    render(await TransactionsPage({ searchParams: {} }))

    expect(screen.getByTestId('transaction-count').textContent).toBe('2')
  })
}) 