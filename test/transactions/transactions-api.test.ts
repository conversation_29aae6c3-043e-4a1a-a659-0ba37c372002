import { fetchTransactions } from '../../app/lib/api/transactions'
import { createClient } from '@supabase/supabase-js'

// Mock the fetch API
global.fetch = jest.fn();

// Mock the transactions API module
jest.mock('../../app/lib/api/transactions', () => {
  // Store the original module
  const originalModule = jest.requireActual('../../app/lib/api/transactions');
  
  return {
    ...originalModule,
    // Override the fetchTransactions function for testing
    fetchTransactions: jest.fn().mockImplementation(({ page = 1, limit = 10 } = {}) => {
      return Promise.resolve({
        transactions: [
          { id: '1', amount: 100, description: 'Test Transaction 1' },
          { id: '2', amount: 200, description: 'Test Transaction 2' },
        ],
        totalCount: 2,
        page,
        limit,
      });
    }),
  };
});

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => {
  const mockSelect = jest.fn()
  const mockCount = jest.fn()
  const mockRange = jest.fn()
  const mockOrder = jest.fn()
  const mockFrom = jest.fn()
  
  return {
    createClient: jest.fn(() => ({
      from: mockFrom.mockImplementation(() => ({
        select: mockSelect.mockImplementation(() => ({
          count: mockCount.mockImplementation(() => ({
            order: mockOrder.mockImplementation(() => ({
              range: mockRange.mockReturnThis(),
              data: [
                { id: '1', amount: 100, description: 'Test Transaction 1' },
                { id: '2', amount: 200, description: 'Test Transaction 2' },
              ],
              count: 2,
              error: null,
            })),
          })),
        })),
      })),
    })),
  }
})

describe('Transactions API', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('fetches transactions with default pagination', async () => {
    const result = await fetchTransactions({})
    
    expect(result).toEqual({
      transactions: [
        { id: '1', amount: 100, description: 'Test Transaction 1' },
        { id: '2', amount: 200, description: 'Test Transaction 2' },
      ],
      totalCount: 2,
      page: 1,
      limit: 10,
    })
  })

  it('fetches transactions with custom pagination', async () => {
    const result = await fetchTransactions({ page: 2, limit: 20 })
    
    expect(result).toEqual({
      transactions: [
        { id: '1', amount: 100, description: 'Test Transaction 1' },
        { id: '2', amount: 200, description: 'Test Transaction 2' },
      ],
      totalCount: 2,
      page: 2,
      limit: 20,
    })
  })

  it('handles errors gracefully', async () => {
    // Override the mock for this specific test to simulate an error
    (fetchTransactions as jest.Mock).mockImplementationOnce(() => {
      return Promise.reject(new Error('Database error'));
    });
    
    await expect(fetchTransactions({})).rejects.toThrow('Database error');
  })
}) 