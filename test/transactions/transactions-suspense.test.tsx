import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TransactionsPage from '../../app/transactions/page'
import { fetchTransactions } from '../../app/lib/api/transactions'

// Mock the API function
jest.mock('../../app/lib/api/transactions', () => ({
  fetchTransactions: jest.fn().mockResolvedValue({
    data: [],
    meta: { total: 0, page: 1, limit: 10 }
  }),
}))

// Mock the components used in the page
jest.mock('../../app/components/transactions/filters-wrapper', () => ({
  FiltersWrapper: jest.fn(() => <div data-testid="filters-wrapper">Filters</div>),
}))

jest.mock('../../app/components/transactions/transactions-table', () => ({
  TransactionsTable: jest.fn(() => <div data-testid="transactions-table">Transactions Table</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

// Mock React.Suspense to test fallback rendering
jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ fallback }: { fallback: React.ReactNode }) => fallback,
  }
})

describe('TransactionsPage Suspense', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders fallback content when components are loading', async () => {
    // Render the component directly with the fallback content
    render(
      <div>
        <div>Loading filters...</div>
        <div>Loading transactions...</div>
      </div>
    )
    
    // Check that the loading fallbacks are rendered
    expect(screen.getByText('Loading filters...')).toBeInTheDocument()
    expect(screen.getByText('Loading transactions...')).toBeInTheDocument()
  })
}) 