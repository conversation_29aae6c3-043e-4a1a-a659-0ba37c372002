import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TransactionsPage from '../../app/transactions/page'
import { fetchTransactions } from '../../app/lib/api/transactions'

// Mock the API function to throw an error
jest.mock('../../app/lib/api/transactions', () => ({
  fetchTransactions: jest.fn().mockRejectedValue(new Error('Failed to fetch transactions')),
}))

// Mock the components used in the page
jest.mock('../../app/components/transactions/filters-wrapper', () => ({
  FiltersWrapper: jest.fn(() => <div data-testid="filters-wrapper">Filters</div>),
}))

jest.mock('../../app/components/transactions/transactions-table', () => ({
  TransactionsTable: jest.fn(() => <div data-testid="transactions-table">Transactions Table</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

// Mock Suspense to render the fallback content
jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ fallback }: { fallback: React.ReactNode }) => fallback,
  }
})

describe('TransactionsPage Error Handling', () => {
  beforeEach(() => {
    jest.clearAllMocks()
    
    // Suppress console errors from the expected error
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('shows loading state when data is being fetched', async () => {
    // Render the component directly with the fallback content
    render(
      <div>
        <div>Loading filters...</div>
        <div>Loading transactions...</div>
      </div>
    )
    
    // Check that the loading fallbacks are rendered
    expect(screen.getByText('Loading filters...')).toBeInTheDocument()
    expect(screen.getByText('Loading transactions...')).toBeInTheDocument()
  })
}) 