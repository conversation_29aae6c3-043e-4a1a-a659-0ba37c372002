import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import PartnerPreferredMXPage from '../../app/admin/partner-preferred-mx/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

// Mock the PartnerPreferredManagementMX component
jest.mock('../../app/admin/partner-preferred-mx/components/partner-preferred-management-mx', () => ({
  PartnerPreferredManagementMX: () => <div data-testid="partner-preferred-management-mx">Partner Preferred MX Management</div>,
}))

describe('PartnerPreferredMXPage', () => {
  beforeEach(() => {
    render(<PartnerPreferredMXPage />)
  })

  it('renders the breadcrumb component', () => {
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('renders the PartnerPreferredManagementMX component', () => {
    expect(screen.getByTestId('partner-preferred-management-mx')).toBeInTheDocument()
  })

  it('has the correct page structure', () => {
    // Check that the container div exists with expected classes
    const containerDiv = screen.getByText('Partner Preferred MX Management').closest('div')
    expect(containerDiv?.parentElement).toHaveClass('mx-auto', 'p-6')
  })

  it('has the correct metadata', () => {
    // We can't directly test metadata in the component render,
    // but we can verify the exported metadata object
    const { metadata } = require('../../app/admin/partner-preferred-mx/page')
    
    expect(metadata.title).toBe('Partner Preferred MX Management | Admin')
  })
}) 