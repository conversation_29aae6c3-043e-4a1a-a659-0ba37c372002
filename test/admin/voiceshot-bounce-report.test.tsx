import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import VoiceShotBouncePage from '../../app/admin/voiceshot-bounce-report/page'

// Mock the components used in the page
jest.mock('../../app/admin/voiceshot-bounce-report/components/voiceshot-bounce-report', () => ({
  VoiceShotBounceReport: jest.fn(() => <div data-testid="voiceshot-bounce-report">VoiceShot Bounce Report Component</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('VoiceShotBouncePage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the VoiceShot bounce report page correctly', () => {
    render(<VoiceShotBouncePage />)
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('voiceshot-bounce-report')).toBeInTheDocument()
    
    // Check for heading
    expect(screen.getByText('VoiceShot Email Bounce Report')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<VoiceShotBouncePage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('p-6')
  })
  
  it('renders the page title with correct styling', () => {
    render(<VoiceShotBouncePage />)
    
    const pageTitle = screen.getByText('VoiceShot Email Bounce Report')
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('mb-6')
    expect(pageTitle).toHaveClass('mt-4')
  })
  
  it('renders the breadcrumb before the page title', () => {
    render(<VoiceShotBouncePage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('VoiceShot Email Bounce Report')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('renders the page title before the report component', () => {
    render(<VoiceShotBouncePage />)
    
    // Get the elements
    const pageTitle = screen.getByText('VoiceShot Email Bounce Report')
    const reportComponent = screen.getByTestId('voiceshot-bounce-report')
    
    // Check their order in the document
    expect(pageTitle.compareDocumentPosition(reportComponent)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  // We don't need to test the metadata directly as it's handled by Next.js
  // and doesn't affect the rendered output in our test environment
}) 