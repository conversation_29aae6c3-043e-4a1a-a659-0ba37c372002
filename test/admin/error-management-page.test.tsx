import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'
import ErrorManagementPage from '../../app/admin/error-management/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

describe('ErrorManagementPage', () => {
  beforeEach(() => {
    render(<ErrorManagementPage />)
  })

  it('renders the page title correctly', () => {
    expect(screen.getByText('Error Management')).toBeInTheDocument()
  })

  it('renders the breadcrumb component', () => {
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('renders the "Add New Error Code" form', () => {
    expect(screen.getByText('Add New Error Code')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter error code')).toBeInTheDocument()
    expect(screen.getByPlaceholderText('Enter error description')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Add Error Code' })).toBeInTheDocument()
  })

  it('renders the error codes table with correct headers', () => {
    expect(screen.getByText('All Error Codes')).toBeInTheDocument()
    
    const headers = screen.getAllByRole('columnheader')
    expect(headers[0]).toHaveTextContent('Error Code')
    expect(headers[1]).toHaveTextContent('Description')
    expect(headers[2]).toHaveTextContent('Severity')
  })

  it('renders the error logs table with correct headers', () => {
    expect(screen.getByText('Recent Error Logs')).toBeInTheDocument()
    
    const headers = screen.getAllByRole('columnheader')
    expect(headers[3]).toHaveTextContent('Timestamp')
    expect(headers[4]).toHaveTextContent('Error Code')
    expect(headers[5]).toHaveTextContent('Message')
    expect(headers[6]).toHaveTextContent('Source')
    expect(headers[7]).toHaveTextContent('Status')
  })

  it('displays the correct number of error codes', () => {
    // Get all rows from the first table (error codes table)
    const allErrorCodeTableRows = screen.getAllByText('All Error Codes')[0]
      .closest('div')
      ?.querySelectorAll('tr');
    
    // Subtract 1 for the header row
    expect(allErrorCodeTableRows?.length - 1).toBe(5);
  })

  it('displays the correct number of error logs', () => {
    // There should be 4 error logs in the mock data
    const logRows = screen.getAllByRole('row').filter(row => 
      row.textContent?.includes('2024-03-10')
    )
    
    expect(logRows.length).toBe(4)
  })

  it('renders severity badges with correct styling', () => {
    const criticalBadge = screen.getByText('Critical')
    const highBadges = screen.getAllByText('High')
    const mediumBadge = screen.getByText('Medium')
    const lowBadge = screen.getByText('Low')

    expect(criticalBadge).toHaveClass('bg-destructive')
    expect(highBadges[0]).toHaveClass('bg-orange-500')
    expect(mediumBadge).toHaveClass('bg-yellow-500')
    expect(lowBadge).toHaveClass('bg-green-500')
  })

  it('renders status badges with correct styling', () => {
    const resolvedBadges = screen.getAllByText('Resolved')
    const pendingBadge = screen.getByText('Pending')
    const investigatingBadge = screen.getByText('Investigating')

    expect(resolvedBadges[0]).toHaveClass('bg-green-500')
    expect(pendingBadge).toHaveClass('bg-yellow-500')
    expect(investigatingBadge).toHaveClass('bg-blue-500')
  })

  it('allows input in the error code form fields', () => {
    const codeInput = screen.getByPlaceholderText('Enter error code')
    const descriptionInput = screen.getByPlaceholderText('Enter error description')
    
    fireEvent.change(codeInput, { target: { value: 'ERR_006' } })
    fireEvent.change(descriptionInput, { target: { value: 'New test error' } })
    
    expect(codeInput).toHaveValue('ERR_006')
    expect(descriptionInput).toHaveValue('New test error')
  })
}) 