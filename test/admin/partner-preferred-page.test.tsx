import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import PartnerPreferredPage from '../../app/admin/partner-preferred/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

// Mock the PartnerPreferredManagement component
jest.mock('../../app/admin/partner-preferred/components/partner-preferred-management', () => ({
  PartnerPreferredManagement: () => <div data-testid="partner-preferred-management">Partner Preferred Management</div>,
}))

describe('PartnerPreferredPage', () => {
  beforeEach(() => {
    render(<PartnerPreferredPage />)
  })

  it('renders the breadcrumb component', () => {
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('renders the PartnerPreferredManagement component', () => {
    expect(screen.getByTestId('partner-preferred-management')).toBeInTheDocument()
  })

  it('has the correct page structure', () => {
    // Check that the main container exists with expected classes
    const mainElement = screen.getByRole('main')
    expect(mainElement).toHaveClass('flex', 'flex-col', 'w-full', 'min-h-0')
    
    // Check that there's a container div with the expected classes
    const containerDiv = mainElement.firstChild
    expect(containerDiv).toHaveClass('flex', 'flex-col', 'space-y-6', 'p-6')
  })

  it('has the correct metadata', () => {
    // We can't directly test metadata in the component render,
    // but we can verify the exported metadata object
    const { metadata } = require('../../app/admin/partner-preferred/page')
    
    expect(metadata.title).toBe('Preferred Partner Management - InteleTravel Admin')
    expect(metadata.description).toBe('Manage preferred partner listings for InteleTravel')
  })
}) 