import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import ReactivationReportPage from '../../app/admin/reactivation-report/page'

// Mock the components used in the page
jest.mock('../../app/admin/reactivation-report/components/reactivation-report', () => ({
  ReactivationReport: jest.fn(() => <div data-testid="reactivation-report">Reactivation Report Component</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb Component</div>),
}))

describe('ReactivationReportPage', () => {
  beforeEach(() => {
    jest.clearAllMocks()
  })

  it('renders the reactivation report page correctly', () => {
    render(<ReactivationReportPage />)
    
    // Check for components
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('reactivation-report')).toBeInTheDocument()
    
    // Check for heading
    expect(screen.getByText('Robert Reactivation Report')).toBeInTheDocument()
  })
  
  it('renders with the correct layout structure', () => {
    const { container } = render(<ReactivationReportPage />)
    
    // Check for the main container div with expected classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('p-6')
  })
  
  it('renders the page title with correct styling', () => {
    render(<ReactivationReportPage />)
    
    const pageTitle = screen.getByText('Robert Reactivation Report')
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('mb-6')
    expect(pageTitle).toHaveClass('mt-4')
  })
  
  it('renders the breadcrumb before the page title', () => {
    render(<ReactivationReportPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Robert Reactivation Report')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('renders the page title before the report component', () => {
    render(<ReactivationReportPage />)
    
    // Get the elements
    const pageTitle = screen.getByText('Robert Reactivation Report')
    const reportComponent = screen.getByTestId('reactivation-report')
    
    // Check their order in the document
    expect(pageTitle.compareDocumentPosition(reportComponent)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  // We don't need to test the metadata directly as it's handled by Next.js
  // and doesn't affect the rendered output in our test environment
}) 