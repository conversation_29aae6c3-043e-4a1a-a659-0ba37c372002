import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TicketLimitReportPage from '../../app/admin/ticket-limit-report/page'

// Mock the Breadcrumb component
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

// Mock the TicketLimitReportForm component
jest.mock('../../app/admin/ticket-limit-report/components/ticket-limit-report-form', () => ({
  TicketLimitReportForm: () => <div data-testid="ticket-limit-report-form">Ticket Limit Report Form</div>,
}))

describe('TicketLimitReportPage', () => {
  beforeEach(() => {
    render(<TicketLimitReportPage />)
  })

  it('renders the breadcrumb component', () => {
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
  })

  it('renders the page title correctly', () => {
    const heading = screen.getByRole('heading', { level: 1 })
    expect(heading).toHaveTextContent('Ticket Limit Report')
    expect(heading).toHaveClass('text-2xl', 'font-bold', 'text-primary')
  })

  it('renders the TicketLimitReportForm component', () => {
    expect(screen.getByTestId('ticket-limit-report-form')).toBeInTheDocument()
  })

  it('has the correct page structure', () => {
    // Get the main container div
    const containerDiv = screen.getByText('Ticket Limit Report').closest('div')
    
    // Check that it has the expected classes
    expect(containerDiv).toHaveClass('flex', 'flex-col', 'space-y-6', 'p-6')
  })

  it('has the correct metadata', () => {
    // We can't directly test metadata in the component render,
    // but we can verify the exported metadata object
    const { metadata } = require('../../app/admin/ticket-limit-report/page')
    
    expect(metadata.title).toBe('Ticket Limit Report - InteleTravel Admin')
    expect(metadata.description).toBe('Search and view ticket limit reports for agents and free users')
  })

  it('renders components in the correct order', () => {
    // Get all the elements we want to check
    const breadcrumb = screen.getByTestId('breadcrumb')
    const heading = screen.getByRole('heading', { level: 1 })
    const form = screen.getByTestId('ticket-limit-report-form')
    
    // Get the parent container
    const container = heading.parentElement
    
    // Check that the elements are in the correct order in the DOM
    if (container) {
      const children = Array.from(container.children)
      expect(children.indexOf(breadcrumb)).toBeLessThan(children.indexOf(heading))
      expect(children.indexOf(heading)).toBeLessThan(children.indexOf(form))
    }
  })
}) 