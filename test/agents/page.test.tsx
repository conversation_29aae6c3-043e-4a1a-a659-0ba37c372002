import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import AgentsPage from '../../app/agents/page'

// Get a reference to the mocked function
const { fetchAgents } = jest.requireMock('../../app/lib/api/agent-api')

// Use relative paths instead of path aliases for all mocks
jest.mock('../../app/lib/api/agent-api', () => ({
  fetchAgents: jest.fn(),
}))

// Create a mock supabaseAdmin object directly
const mockSupabaseAdmin = {
  from: jest.fn().mockReturnThis(),
  insert: jest.fn().mockResolvedValue({}),
}

jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(() => new URLSearchParams()),
}))

// Mock the components used in the page with relative paths
jest.mock('../../app/components/agents/agent-filters', () => ({
  AgentFilters: () => <div data-testid="agent-filters">Agent Filters</div>,
}))

jest.mock('../../app/components/agents/agent-table', () => ({
  AgentTable: ({ data }: { data: any }) => (
    <div data-testid="agent-table">
      Agent Table with {data?.agents?.length || 0} agents
    </div>
  ),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb</div>,
}))

jest.mock('../../app/components/ui/button', () => ({
  Button: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <button className={className} data-testid="button">
      {children}
    </button>
  ),
}))

jest.mock('lucide-react', () => ({
  Plus: () => <span data-testid="plus-icon">Plus Icon</span>,
}))

describe('AgentsPage', () => {
  const mockAgentsData = {
    agents: [
      { id: 1, vcFName: 'John', vcLName: 'Doe' },
      { id: 2, vcFName: 'Jane', vcLName: 'Smith' },
    ],
    totalCount: 2,
    page: 1,
    limit: 10,
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetchAgents as jest.Mock).mockResolvedValue(mockAgentsData)
  })

  afterEach(async () => {
    // Use the mock supabaseAdmin instead
    await mockSupabaseAdmin
      .from('test_results')
      .insert([
        {
          test_name: 'AgentsPage Test',
          passed: true,
          timestamp: new Date().toISOString(),
          details: 'Test completed successfully',
        },
      ])
  })

  it('renders the agents page with correct components', async () => {
    const { container } = render(await AgentsPage({}))
    
    // Check if all components are rendered
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByText('Advisor Management')).toBeInTheDocument()
    expect(screen.getByTestId('button')).toBeInTheDocument()
    expect(screen.getByTestId('plus-icon')).toBeInTheDocument()
    expect(screen.getByText('Add Agent')).toBeInTheDocument()
    expect(screen.getByTestId('agent-filters')).toBeInTheDocument()
    expect(screen.getByTestId('agent-table')).toBeInTheDocument()
    
    // Verify the page structure
    expect(container.firstChild).toHaveClass('p-6 space-y-6')
  })

  it('calls fetchAgents with default parameters when no search params are provided', async () => {
    render(await AgentsPage({}))
    
    expect(fetchAgents).toHaveBeenCalledWith({
      page: 1,
      limit: 10,
      orderBy: 'vcFName',
      sortDirection: 'ASC',
      filters: {
        vcFName: '',
        vcLName: '',
      }
    })
  })

  it('calls fetchAgents with provided search parameters', async () => {
    const searchParams = {
      page: '2',
      limit: '20',
      orderBy: 'vcLName',
      sortDirection: 'DESC',
      name: 'John',
      lastName: 'Doe'
    }
    
    render(await AgentsPage({ searchParams }))
    
    expect(fetchAgents).toHaveBeenCalledWith({
      page: 2,
      limit: 20,
      orderBy: 'vcLName',
      sortDirection: 'DESC',
      filters: {
        vcFName: 'John',
        vcLName: 'Doe',
      }
    })
  })

  it('passes the fetched data to the AgentTable component', async () => {
    render(await AgentsPage({}))
    
    expect(screen.getByTestId('agent-table')).toHaveTextContent('Agent Table with 2 agents')
  })
}) 