import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import TrialAgentReportPage from '../../app/agents/trial-report/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
}))

jest.mock('../../app/components/agents/trial-report/trial-agent-table', () => ({
  TrialAgentTable: () => <div data-testid="trial-agent-table">Trial Agent Table</div>,
}))

describe('TrialAgentReportPage', () => {
  it('renders the page with correct components', () => {
    render(<TrialAgentReportPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Trial Agent Status Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    
    // Check for table component
    expect(screen.getByTestId('trial-agent-table')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<TrialAgentReportPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('p-6')
    expect(mainContainer).toHaveClass('space-y-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<TrialAgentReportPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Trial Agent Status Report' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('renders components in the correct order', () => {
    render(<TrialAgentReportPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Trial Agent Status Report')
    const table = screen.getByTestId('trial-agent-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('has proper spacing between elements', () => {
    const { container } = render(<TrialAgentReportPage />)
    
    // Check that the container has space-y-6 for proper spacing
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('space-y-6')
  })
  
  it('displays the trial agent table directly in the page container', () => {
    render(<TrialAgentReportPage />)
    
    // Get the elements
    const table = screen.getByTestId('trial-agent-table')
    const mainContainer = screen.getByTestId('breadcrumb').parentElement
    
    // Check that the table is a direct child of the main container
    expect(table.parentElement).toBe(mainContainer)
  })
  
  it('has the correct page title for the report', () => {
    render(<TrialAgentReportPage />)
    
    // Check for the specific title text
    const pageTitle = screen.getByText('Trial Agent Status Report')
    expect(pageTitle).toBeInTheDocument()
    
    // Verify it's the main heading
    expect(pageTitle.tagName).toBe('H1')
  })
}) 