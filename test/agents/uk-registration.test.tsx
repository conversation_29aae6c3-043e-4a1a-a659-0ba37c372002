import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import * as UkRegistrationPageModule from '../../app/agents/uk-registration/page'
import { fetchUKRegistrationData } from '../../app/lib/api/uk-registration'

// Mock the API function
jest.mock('../../app/lib/api/uk-registration', () => ({
  fetchUKRegistrationData: jest.fn(),
}))

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/agents/uk-registration/components/uk-registration-table', () => ({
  UkRegistrationTable: ({ initialData }: { initialData: any[] }) => (
    <div data-testid="uk-registration-table">
      UK Registration Table with {initialData.length} entries
    </div>
  ),
}))

jest.mock('../../app/agents/uk-registration/components/email-report-dialog', () => ({
  EmailReportDialog: () => <div data-testid="email-report-dialog">Email Report Dialog</div>,
}))

jest.mock('../../app/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-header" className={className}>{children}</div>
  ),
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-title" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content">{children}</div>
  ),
}))

jest.mock('../../app/components/ui/button', () => ({
  Button: ({ children, variant }: { children: React.ReactNode; variant?: string }) => (
    <button data-testid="button" data-variant={variant}>{children}</button>
  ),
}))

jest.mock('lucide-react', () => ({
  Users: () => <span data-testid="users-icon">Users Icon</span>,
  Download: () => <span data-testid="download-icon">Download Icon</span>,
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ children }: { children: React.ReactNode }) => <div data-testid="suspense">{children}</div>,
  }
})

// Create a simplified version of the page component for testing
const MockUkRegistrationPage = () => {
  return (
    <div className="flex flex-col gap-4 p-6">
      <div data-testid="breadcrumb">Breadcrumb Component</div>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">UK Agent Registration Report</h1>
        <div className="flex gap-2">
          <div data-testid="email-report-dialog">Email Report Dialog</div>
          <button data-testid="button" data-variant="outline">
            <span data-testid="download-icon">Download Icon</span>
            Export Excel
          </button>
        </div>
      </div>
      
      <div data-testid="suspense">
        <div data-testid="uk-registration-content">UK Registration Content</div>
      </div>
    </div>
  )
}

describe('UkRegistrationPage', () => {
  const mockData = {
    totalAgents: 150,
    reportData: [
      { id: 1, name: 'John Doe', status: 'Active' },
      { id: 2, name: 'Jane Smith', status: 'Active' },
    ],
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(fetchUKRegistrationData as jest.Mock).mockResolvedValue(mockData)
  })

  it('renders the page with correct layout and components', async () => {
    render(<MockUkRegistrationPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('UK Agent Registration Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for action buttons
    expect(screen.getByTestId('email-report-dialog')).toBeInTheDocument()
    expect(screen.getByTestId('button')).toBeInTheDocument()
    expect(screen.getByText('Export Excel')).toBeInTheDocument()
    expect(screen.getByTestId('download-icon')).toBeInTheDocument()
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = UkRegistrationPageModule
    
    // Check metadata properties
    expect(metadata.title).toBe('UK Agent Registration | Agents')
    expect(metadata.description).toBe('View and export UK agent registration data')
  })
  
  it('has the correct page structure and layout', async () => {
    const { container } = render(<MockUkRegistrationPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('UK Agent Registration Report').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
  })
  
  it('makes the correct API call to fetch registration data', () => {
    // We can verify the UkRegistrationContent component exists in the page component
    const pageContent = UkRegistrationPageModule.default.toString()
    expect(pageContent).toContain('UkRegistrationContent')
    
    // Verify the page has a Suspense component that wraps content
    expect(pageContent).toContain('Suspense')
    
    // Verify the API function is mocked correctly
    expect(fetchUKRegistrationData).toBeDefined()
    expect(jest.isMockFunction(fetchUKRegistrationData)).toBe(true)
  })
}) 