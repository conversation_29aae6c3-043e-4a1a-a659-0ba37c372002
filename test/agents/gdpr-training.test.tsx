import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import GdprTrainingPage from '../../app/agents/gdpr-training/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/agents/gdpr-training/components/gdpr-training-table', () => ({
  GdprTrainingTable: () => <div data-testid="gdpr-training-table">GDPR Training Table</div>,
}))

jest.mock('../../app/agents/gdpr-training/components/training-stats', () => ({
  TrainingStats: () => <div data-testid="training-stats">Training Statistics</div>,
}))

describe('GdprTrainingPage', () => {
  it('renders the page with correct components', () => {
    render(<GdprTrainingPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('GDPR Training Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for stats and table components
    expect(screen.getByTestId('training-stats')).toBeInTheDocument()
    expect(screen.getByTestId('gdpr-training-table')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<GdprTrainingPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-6')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('GDPR Training Report').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<GdprTrainingPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'GDPR Training Report' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = require('../../app/agents/gdpr-training/page')
    
    // Check metadata properties
    expect(metadata.title).toBe('GDPR Training Report | Agents')
    expect(metadata.description).toBe("View agents' GDPR training completion status")
  })
  
  it('renders components in the correct order', () => {
    render(<GdprTrainingPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('GDPR Training Report')
    const stats = screen.getByTestId('training-stats')
    const table = screen.getByTestId('gdpr-training-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(stats)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(stats.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
}) 