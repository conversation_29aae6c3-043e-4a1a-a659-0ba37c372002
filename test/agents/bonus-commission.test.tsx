import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import AgentBonusCommissionPage from '../../app/agents/bonus-commission/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
}))

jest.mock('../../app/components/agents/bonus-commission/agent-bonus-table', () => ({
  AgentBonusTable: () => <div data-testid="agent-bonus-table">Agent Bonus Table</div>,
}))

jest.mock('../../app/components/ui/table-skeleton', () => ({
  TableSkeleton: () => <div data-testid="table-skeleton">Loading Table...</div>,
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ children }) => children,
  }
})

describe('AgentBonusCommissionPage', () => {
  it('renders the page with correct components', () => {
    render(<AgentBonusCommissionPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Agent Bonus Commission')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    
    // Check for card component
    const card = screen.getByTestId('card')
    expect(card).toBeInTheDocument()
    expect(card).toHaveClass('bg-white')
    expect(card).toHaveClass('rounded-none')
    expect(card).toHaveClass('p-6')
    
    // Check for table component
    expect(screen.getByTestId('agent-bonus-table')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<AgentBonusCommissionPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('p-6')
    expect(mainContainer).toHaveClass('space-y-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the card has the correct styling
    const card = screen.getByTestId('card')
    expect(card).toHaveClass('bg-white')
    expect(card).toHaveClass('rounded-none')
    expect(card).toHaveClass('p-6')
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<AgentBonusCommissionPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Agent Bonus Commission' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('renders components in the correct order', () => {
    render(<AgentBonusCommissionPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Agent Bonus Commission')
    const table = screen.getByTestId('agent-bonus-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('wraps the table component in Suspense for loading states', () => {
    // This is a bit tricky to test directly since we're mocking Suspense
    // But we can verify the structure of the component
    const originalModule = jest.requireActual('../../app/agents/bonus-commission/page')
    
    // Check that the page uses Suspense for the table
    const pageContent = originalModule.default.toString()
    expect(pageContent).toContain('Suspense')
    expect(pageContent).toContain('TableSkeleton')
  })
  
  it('uses a Card component to contain the table', () => {
    render(<AgentBonusCommissionPage />)
    
    // Verify the table is inside the card
    const table = screen.getByTestId('agent-bonus-table')
    const card = screen.getByTestId('card')
    
    expect(table.closest('[data-testid="card"]')).toBe(card)
  })
  
  it('has proper spacing between elements', () => {
    const { container } = render(<AgentBonusCommissionPage />)
    
    // Check that the container has space-y-6 for proper spacing
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('space-y-6')
  })
}) 