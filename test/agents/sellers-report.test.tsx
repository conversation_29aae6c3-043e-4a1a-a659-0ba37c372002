import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import SellersReportPage from '../../app/agents/sellers-report/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/agents/sellers-report/components/sellers-report-filters', () => ({
  SellersReportFilters: () => <div data-testid="sellers-report-filters">Sellers Report Filters</div>,
}))

jest.mock('../../app/agents/sellers-report/components/sellers-report-table', () => ({
  SellersReportTable: () => <div data-testid="sellers-report-table">Sellers Report Table</div>,
}))

jest.mock('../../app/agents/sellers-report/components/sellers-report-summary', () => ({
  SellersReportSummary: () => <div data-testid="sellers-report-summary">Sellers Report Summary</div>,
}))

jest.mock('@/components/ui/table-skeleton', () => ({
  TableSkeleton: () => <div data-testid="table-skeleton">Loading Table...</div>,
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ children }) => children,
  }
})

describe('SellersReportPage', () => {
  it('renders the page with correct components', () => {
    render(<SellersReportPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Sellers Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for report components
    expect(screen.getByTestId('sellers-report-summary')).toBeInTheDocument()
    expect(screen.getByTestId('sellers-report-filters')).toBeInTheDocument()
    expect(screen.getByTestId('sellers-report-table')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<SellersReportPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('Sellers Report').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<SellersReportPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Sellers Report' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = require('../../app/agents/sellers-report/page')
    
    // Check metadata properties
    expect(metadata.title).toBe('Sellers Report | Agents')
    expect(metadata.description).toBe('View and analyze seller performance metrics')
  })
  
  it('renders components in the correct order', () => {
    render(<SellersReportPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Sellers Report')
    const summary = screen.getByTestId('sellers-report-summary')
    const filters = screen.getByTestId('sellers-report-filters')
    const table = screen.getByTestId('sellers-report-table')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(summary)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(summary.compareDocumentPosition(filters)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(filters.compareDocumentPosition(table)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('wraps data components in Suspense for loading states', () => {
    // This is a bit tricky to test directly since we're mocking Suspense
    // But we can verify the structure of the component
    const originalModule = jest.requireActual('../../app/agents/sellers-report/page')
    
    // Check that the page uses Suspense for the table and summary
    const pageContent = originalModule.default.toString()
    expect(pageContent).toContain('Suspense')
    expect(pageContent).toContain('TableSkeleton')
  })
}) 