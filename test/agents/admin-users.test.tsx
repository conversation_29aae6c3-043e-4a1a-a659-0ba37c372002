import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import AdminUsersPage from '../../app/agents/admin-users/page'
import { graphqlClient } from '../../app/lib/graphql-client'

// Mock the GraphQL client
jest.mock('../../app/lib/graphql-client', () => ({
  graphqlClient: {
    request: jest.fn(),
  },
}))

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/components/ui/button', () => ({
  Button: ({ children, asChild, ...props }: any) => (
    <button data-testid="button" {...props}>
      {asChild ? children : children}
    </button>
  ),
}))

jest.mock('../../app/agents/admin-users/components/admin-users-table', () => ({
  AdminUsersTable: ({ initialData }: { initialData: any[] }) => (
    <div data-testid="admin-users-table">
      Admin Users Table with {initialData.length} users
    </div>
  ),
}))

jest.mock('../../app/components/ui/table-skeleton', () => ({
  TableSkeleton: () => <div data-testid="table-skeleton">Loading Table...</div>,
}))

jest.mock('next/link', () => ({
  __esModule: true,
  default: ({ href, children }: { href: string; children: React.ReactNode }) => (
    <a href={href} data-testid="next-link">
      {children}
    </a>
  ),
}))

jest.mock('lucide-react', () => ({
  Plus: () => <span data-testid="plus-icon">Plus Icon</span>,
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ children }: { children: React.ReactNode }) => children,
  }
})

describe('AdminUsersPage', () => {
  const mockAdminUsers = [
    { userId: '1', username: 'admin1', securityLevel: 'Admin', status: 'Active', cellNumber: '************' },
    { userId: '2', username: 'admin2', securityLevel: 'User', status: 'Active', cellNumber: '************' },
  ]

  const mockResponse = {
    getAllAdminUsers: {
      status: 'true',
      message: 'Success',
      totalCount: 2,
      adminUsers: mockAdminUsers,
    },
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(graphqlClient.request as jest.Mock).mockResolvedValue(mockResponse)
  })

  it('fetches admin users data from the API', async () => {
    await render(await AdminUsersPage())
    
    expect(graphqlClient.request).toHaveBeenCalledTimes(1)
    expect(graphqlClient.request).toHaveBeenCalledWith(expect.stringContaining('GetAllAdminUsers'))
  })

  it('renders the page with correct components when data is loaded', async () => {
    await render(await AdminUsersPage())
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Admin Users')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for create button
    const createButton = screen.getByTestId('button')
    expect(createButton).toBeInTheDocument()
    expect(screen.getByText('Create New User')).toBeInTheDocument()
    expect(screen.getByTestId('plus-icon')).toBeInTheDocument()
    
    // Check for table component
    expect(screen.getByTestId('admin-users-table')).toBeInTheDocument()
    expect(screen.getByText('Admin Users Table with 2 users')).toBeInTheDocument()
  })
  
  it('renders an error message when API call fails', async () => {
    // Mock API failure
    (graphqlClient.request as jest.Mock).mockResolvedValue({ getAllAdminUsers: null })
    
    await render(await AdminUsersPage())
    
    expect(screen.getByText('Error loading admin users')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', async () => {
    const { container } = await render(await AdminUsersPage())
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('Admin Users').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = require('../../app/agents/admin-users/page')
    
    // Check metadata properties
    expect(metadata.title).toBe('Admin Users | Agents')
    expect(metadata.description).toBe('Manage admin users and their permissions')
  })
  
  it('has the correct dynamic rendering configuration', () => {
    // Import the dynamic and revalidate settings
    const { dynamic, revalidate } = require('../../app/agents/admin-users/page')
    
    // Check the settings
    expect(dynamic).toBe('force-dynamic')
    expect(revalidate).toBe(0)
  })
  
  it('links to the create new user page', async () => {
    await render(await AdminUsersPage())
    
    const link = screen.getByTestId('next-link')
    expect(link).toHaveAttribute('href', '/agents/admin-users/new')
  })
}) 