import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import CoachReportPage from '../../app/agents/coach-report/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/agents/coach-report/components/coach-report-content', () => ({
  CoachReportContent: () => <div data-testid="coach-report-content">Coach Report Content</div>,
}))

describe('CoachReportPage', () => {
  it('renders the page with correct components', () => {
    render(<CoachReportPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Coach Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for report content
    expect(screen.getByTestId('coach-report-content')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<CoachReportPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('Coach Report').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<CoachReportPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Coach Report' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = require('../../app/agents/coach-report/page')
    
    // Check metadata properties
    expect(metadata.title).toBe('Coach Report | Agents')
    expect(metadata.description).toBe('View and analyze coach-agent relationships and performance')
  })
  
  it('has the correct dynamic rendering configuration', () => {
    // Import the dynamic and revalidate settings
    const { dynamic, revalidate } = require('../../app/agents/coach-report/page')
    
    // Check the settings
    expect(dynamic).toBe('force-dynamic')
    expect(revalidate).toBe(0)
  })
  
  it('renders components in the correct order', () => {
    render(<CoachReportPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Coach Report')
    const reportContent = screen.getByTestId('coach-report-content')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(reportContent)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
}) 