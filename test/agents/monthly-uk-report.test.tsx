import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import * as MonthlyUkReportPageModule from '../../app/agents/monthly-uk-report/page'
import { graphqlClient } from '../../app/lib/graphql-client'

// Mock the GraphQL client
jest.mock('../../app/lib/graphql-client', () => ({
  graphqlClient: {
    request: jest.fn(),
  },
}))

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/components/error-boundary', () => ({
  ErrorBoundaryClient: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="error-boundary">{children}</div>
  ),
}))

jest.mock('../../app/agents/monthly-uk-report/components/monthly-uk-table', () => ({
  MonthlyUkTable: ({ activeAgents, canceledAgents, amendments }: any) => (
    <div data-testid="monthly-uk-table">
      Monthly UK Table with {activeAgents.length} active agents, {canceledAgents.length} canceled agents, and {amendments.length} amendments
    </div>
  ),
}))

jest.mock('../../app/agents/monthly-uk-report/components/email-report-dialog', () => ({
  EmailReportDialog: () => <div data-testid="email-report-dialog">Email Report Dialog</div>,
}))

jest.mock('../../app/agents/monthly-uk-report/components/date-display', () => ({
  DateDisplay: ({ startDate, endDate }: { startDate: string; endDate: string }) => (
    <div data-testid="date-display">Date Range: {startDate} to {endDate}</div>
  ),
}))

jest.mock('../../app/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>{children}</div>
  ),
  CardHeader: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-header" className={className}>{children}</div>
  ),
  CardTitle: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card-title" className={className}>{children}</div>
  ),
  CardContent: ({ children }: { children: React.ReactNode }) => (
    <div data-testid="card-content">{children}</div>
  ),
}))

jest.mock('../../app/components/ui/button', () => ({
  Button: ({ children, variant }: { children: React.ReactNode; variant?: string }) => (
    <button data-testid="button" data-variant={variant}>{children}</button>
  ),
}))

jest.mock('lucide-react', () => ({
  Users: () => <span data-testid="users-icon">Users Icon</span>,
  Download: () => <span data-testid="download-icon">Download Icon</span>,
  Mail: () => <span data-testid="mail-icon">Mail Icon</span>,
}))

jest.mock('react', () => {
  const originalReact = jest.requireActual('react')
  return {
    ...originalReact,
    Suspense: ({ children }: { children: React.ReactNode }) => <div data-testid="suspense">{children}</div>,
  }
})

// Create a simplified version of the page component for testing
const MockMonthlyUkReportPage = () => {
  return (
    <div className="flex flex-col gap-4 p-6">
      <div data-testid="breadcrumb">Breadcrumb Component</div>
      <div data-testid="error-boundary">
        <div data-testid="suspense">
          <div>
            <div className="flex items-center justify-between">
              <h1 className="text-2xl font-semibold tracking-tight">Monthly UK Agent Report</h1>
              <div className="flex gap-2">
                <div data-testid="email-report-dialog">Email Report Dialog</div>
                <button data-testid="button" data-variant="outline">
                  <span data-testid="download-icon">Download Icon</span>
                  Export Excel
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

describe('MonthlyUkReportPage', () => {
  const mockReport = {
    activeAgents: [{ firstName: 'John', lastName: 'Doe' }],
    canceledAgents: [],
    amendments: [],
    reportDate: '2023-05-01',
    dateRange: {
      startDate: '2023-05-01',
      endDate: '2023-05-31',
    },
  }

  beforeEach(() => {
    jest.clearAllMocks()
    ;(graphqlClient.request as jest.Mock).mockResolvedValue({ generateUKABTAReport: mockReport })
  })

  it('renders the page with correct layout and components', async () => {
    render(<MockMonthlyUkReportPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for error boundary
    expect(screen.getByTestId('error-boundary')).toBeInTheDocument()
    
    // Check for suspense
    expect(screen.getByTestId('suspense')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Monthly UK Agent Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = MonthlyUkReportPageModule
    
    // Check metadata properties
    expect(metadata.title).toBe('Monthly UK Agent Report | Agents')
    expect(metadata.description).toBe('View and export monthly UK agent registration data')
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<MockMonthlyUkReportPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
  })
  
  it('verifies the page has a loading fallback component', () => {
    // Check that the page module has a LoadingFallback function
    const pageContent = MonthlyUkReportPageModule.default.toString()
    
    // Check that the page uses LoadingFallback
    expect(pageContent).toContain('LoadingFallback')
    expect(pageContent).toContain('Suspense')
  })
  
  it('verifies the page makes API calls to fetch report data', () => {
    // Check that the page module has a function that makes API calls
    const pageContent = MonthlyUkReportPageModule.default.toString()
    
    // Check that the page uses ReportContent which should make API calls
    expect(pageContent).toContain('ReportContent')
    expect(pageContent).toContain('Suspense')
  })
}) 