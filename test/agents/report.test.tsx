import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import AgentReportPage from '../../app/agents/report/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/card', () => ({
  Card: ({ children, className }: { children: React.ReactNode; className?: string }) => (
    <div data-testid="card" className={className}>
      {children}
    </div>
  ),
}))

jest.mock('../../app/components/agents/report/agent-report-form', () => ({
  AgentReportForm: () => <div data-testid="agent-report-form">Agent Report Form</div>,
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

describe('AgentReportPage', () => {
  it('renders the page with correct components', () => {
    render(<AgentReportPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Agent Report')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    
    // Check for card component
    const card = screen.getByTestId('card')
    expect(card).toBeInTheDocument()
    expect(card).toHaveClass('bg-white')
    expect(card).toHaveClass('rounded-none')
    expect(card).toHaveClass('p-6')
    
    // Check for report form
    expect(screen.getByTestId('agent-report-form')).toBeInTheDocument()
  })
  
  it('renders the components in the correct order', () => {
    render(<AgentReportPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Agent Report')
    const reportForm = screen.getByTestId('agent-report-form')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(reportForm)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<AgentReportPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('p-6')
    expect(mainContainer).toHaveClass('space-y-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    expect(breadcrumb).toBe(mainContainer.firstChild)
    
    // Verify the card has the correct styling for the layout
    const card = screen.getByTestId('card')
    expect(card).toHaveClass('bg-white')
    expect(card).toHaveClass('rounded-none')
    expect(card).toHaveClass('p-6')
    
    // Verify the form is inside the card
    const reportForm = screen.getByTestId('agent-report-form')
    expect(reportForm.closest('[data-testid="card"]')).toBeTruthy()
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<AgentReportPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Agent Report' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
}) 