import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import NewAdminUserPage from '../../app/agents/admin-users/new/page'

// Mock the components used in the page
jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: () => <div data-testid="breadcrumb">Breadcrumb Component</div>,
}))

jest.mock('../../app/agents/admin-users/new/components/admin-user-form', () => ({
  AdminUserForm: () => <div data-testid="admin-user-form">Admin User Form</div>,
}))

describe('NewAdminUserPage', () => {
  it('renders the page with correct components', () => {
    render(<NewAdminUserPage />)
    
    // Check for breadcrumb
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    
    // Check for page title
    const pageTitle = screen.getByText('Create Admin User')
    expect(pageTitle).toBeInTheDocument()
    expect(pageTitle).toHaveClass('text-2xl')
    expect(pageTitle).toHaveClass('font-semibold')
    expect(pageTitle).toHaveClass('tracking-tight')
    
    // Check for form component
    expect(screen.getByTestId('admin-user-form')).toBeInTheDocument()
  })
  
  it('has the correct page structure and layout', () => {
    const { container } = render(<NewAdminUserPage />)
    
    // Check the main container has the correct classes
    const mainContainer = container.firstChild as HTMLElement
    expect(mainContainer).toHaveClass('flex')
    expect(mainContainer).toHaveClass('flex-col')
    expect(mainContainer).toHaveClass('gap-4')
    expect(mainContainer).toHaveClass('p-6')
    
    // Verify the breadcrumb is at the top of the page
    const breadcrumb = screen.getByTestId('breadcrumb')
    expect(breadcrumb).toBeInTheDocument()
    expect(breadcrumb.parentElement).toBe(mainContainer)
    
    // Verify the title container has the correct styling
    const titleContainer = screen.getByText('Create Admin User').closest('div')
    expect(titleContainer).toHaveClass('flex')
    expect(titleContainer).toHaveClass('items-center')
    expect(titleContainer).toHaveClass('justify-between')
    
    // Verify the form container has margin top
    const formContainer = screen.getByTestId('admin-user-form').parentElement
    expect(formContainer).toHaveClass('mt-4')
  })
  
  it('has the correct semantic structure with heading', () => {
    render(<NewAdminUserPage />)
    
    // Check that the page title is an h1 element
    const heading = screen.getByRole('heading', { level: 1, name: 'Create Admin User' })
    expect(heading).toBeInTheDocument()
    expect(heading).toHaveClass('text-2xl')
    expect(heading).toHaveClass('font-semibold')
  })
  
  it('has the correct metadata configuration', () => {
    // Import the metadata from the page
    const { metadata } = require('../../app/agents/admin-users/new/page')
    
    // Check metadata properties
    expect(metadata.title).toBe('Create Admin User | Agents')
    expect(metadata.description).toBe('Create a new admin user')
  })
  
  it('renders components in the correct order', () => {
    render(<NewAdminUserPage />)
    
    // Get the elements
    const breadcrumb = screen.getByTestId('breadcrumb')
    const pageTitle = screen.getByText('Create Admin User')
    const form = screen.getByTestId('admin-user-form')
    
    // Check their order in the document
    expect(breadcrumb.compareDocumentPosition(pageTitle)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
    expect(pageTitle.compareDocumentPosition(form)).toBe(Node.DOCUMENT_POSITION_FOLLOWING)
  })
}) 