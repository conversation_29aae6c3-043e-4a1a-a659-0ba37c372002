import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { TravelInsuredUpload } from '../../app/components/travel-insured/travel-insured-upload'
import { act } from 'react'

// Mock fetch
global.fetch = jest.fn()

// Create a reusable mock FileReader that we can customize per test
const createMockFileReader = () => ({
  onload: jest.fn(),
  onerror: jest.fn(),
  readAsDataURL: jest.fn(function() {
    this.onload({ target: { result: 'data:text/csv;base64,dGVzdCBkYXRh' } })
  }),
})

describe('TravelInsuredUpload Component', () => {
  let mockFileReader: any

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Create a new mock FileReader for each test
    mockFileReader = createMockFileReader()
    global.FileReader = jest.fn(() => mockFileReader)
    
    // Mock successful API response by default
    ;(global.fetch as jest.Mock).mockResolvedValue({
      json: jest.fn().mockResolvedValue({
        data: {
          uploadTravelInsuredCSV: {
            success: true,
            message: 'File uploaded and processed: 2 of 2 records processed successfully',
            fileName: 'test_file.csv',
            filePath: '/path/to/file.csv',
            totalRecords: 2,
            processedRecords: 2,
            errors: []
          }
        }
      })
    })
  })

  it('renders the upload form correctly', () => {
    render(<TravelInsuredUpload />)
    
    // Check for form elements
    expect(screen.getByText('Upload a CSV file containing Travel Insured policy information')).toBeInTheDocument()
    expect(screen.getByText('CSV File')).toBeInTheDocument()
    expect(screen.getByText('Upload a CSV file with Travel Insured policy data')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Upload' })).toBeInTheDocument()
  })

  it('handles file upload and shows success message', async () => {
    render(<TravelInsuredUpload />)
    
    // Create a mock file
    const file = new File(['test data'], 'test.csv', { type: 'text/csv' })
    
    // Get file input directly
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    expect(fileInput).not.toBeNull()
    
    await act(async () => {
      if (fileInput) {
        fireEvent.change(fileInput, { target: { files: [file] } })
      }
    })
    
    // Submit the form
    const uploadButton = screen.getByRole('button', { name: 'Upload' })
    await act(async () => {
      fireEvent.click(uploadButton)
    })
    
    // Wait for the API call to complete
    await waitFor(() => {
      expect(global.fetch).toHaveBeenCalledWith(
        expect.any(String),
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
          }),
          body: expect.stringContaining('uploadTravelInsuredCSV'),
        })
      )
    }, { timeout: 3000 })
    
    // Check success message
    await waitFor(() => {
      const successElements = screen.queryAllByText(/Success/i)
      expect(successElements.length).toBeGreaterThan(0)
      
      const messageElements = screen.queryAllByText(/File uploaded and processed/i)
      expect(messageElements.length).toBeGreaterThan(0)
      
      const totalRecordsElements = screen.queryAllByText(/Total records/i)
      expect(totalRecordsElements.length).toBeGreaterThan(0)
    }, { timeout: 3000 })
  })

  // Test that the form submission works
  it('submits the form correctly', async () => {
    render(<TravelInsuredUpload />)
    
    // Create a mock file
    const file = new File(['test data'], 'test.csv', { type: 'text/csv' })
    
    // Get file input directly
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    expect(fileInput).not.toBeNull()
    
    await act(async () => {
      if (fileInput) {
        fireEvent.change(fileInput, { target: { files: [file] } })
      }
    })
    
    // Submit the form
    const uploadButton = screen.getByRole('button', { name: 'Upload' })
    
    // Mock a delayed response
    let resolvePromise: (value: any) => void
    const delayedPromise = new Promise((resolve) => {
      resolvePromise = resolve
    })
    
    ;(global.fetch as jest.Mock).mockImplementation(() => {
      return delayedPromise
    })
    
    await act(async () => {
      fireEvent.click(uploadButton)
    })
    
    // Verify that fetch was called
    expect(global.fetch).toHaveBeenCalled()
    
    // Resolve the promise to complete the test
    await act(async () => {
      resolvePromise!({
        json: () => Promise.resolve({
          data: {
            uploadTravelInsuredCSV: {
              success: true,
              message: 'File uploaded and processed successfully',
              fileName: 'test_file.csv',
              filePath: '/path/to/file.csv',
              totalRecords: 2,
              processedRecords: 2,
              errors: []
            }
          }
        })
      })
    })
  })

  it('validates file extension', async () => {
    render(<TravelInsuredUpload />)
    
    // Create a mock file with wrong extension
    const file = new File(['test data'], 'test.txt', { type: 'text/plain' })
    
    // Get file input directly
    const fileInput = document.querySelector('input[type="file"]') as HTMLInputElement
    expect(fileInput).not.toBeNull()
    
    await act(async () => {
      if (fileInput) {
        fireEvent.change(fileInput, { target: { files: [file] } })
      }
    })
    
    // Submit the form to trigger validation
    const uploadButton = screen.getByRole('button', { name: 'Upload' })
    await act(async () => {
      fireEvent.click(uploadButton)
    })
    
    // We're not checking for specific error messages as they might be handled differently
    // Just verify the form submission doesn't trigger the API call
    expect(global.fetch).not.toHaveBeenCalled()
  })
}) 