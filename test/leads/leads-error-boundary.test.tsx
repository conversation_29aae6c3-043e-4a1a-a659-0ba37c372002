import { render, screen } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter, useSearchParams } from 'next/navigation'
import LeadsPage from '../../app/leads/page'
import React from 'react'

// Mock the Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock the components to simulate an error
jest.mock('../../app/components/leads/leads-table', () => ({
  LeadsTable: jest.fn(() => {
    throw new Error('Test error in LeadsTable')
    return null
  }),
}))

jest.mock('../../app/components/leads/leads-filter', () => ({
  LeadsFilter: jest.fn(() => <div data-testid="leads-filter">Leads Filter</div>),
}))

jest.mock('../../app/components/ui/table-skeleton', () => ({
  TableSkeleton: jest.fn(() => <div data-testid="table-skeleton">Loading...</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

describe('LeadsPage Error Handling', () => {
  const mockRouter = {
    replace: jest.fn(),
  }
  
  const mockSearchParams = new URLSearchParams()
  mockSearchParams.get = jest.fn(() => '')

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    // Suppress console errors from the expected error
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('renders error fallback when a component throws', async () => {
    render(<LeadsPage />)
    
    // Should show the error message
    expect(screen.getByText('Something went wrong:')).toBeInTheDocument()
    expect(screen.getByText('Test error in LeadsTable')).toBeInTheDocument()
  })
}) 