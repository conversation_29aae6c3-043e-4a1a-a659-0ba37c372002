import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter, useSearchParams } from 'next/navigation'
import LeadsPage from '../../app/leads/page'
import React from 'react'
import { LeadsTable } from '@/app/components/leads/leads-table'
import { LeadsFilter } from '@/app/components/leads/leads-filter'

// Mock the Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Mock the components used in the page
jest.mock('../../app/components/leads/leads-table', () => ({
  LeadsTable: jest.fn(() => <div data-testid="leads-table">Leads Table</div>),
}))

jest.mock('../../app/components/leads/leads-filter', () => ({
  LeadsFilter: jest.fn(() => <div data-testid="leads-filter">Leads Filter</div>),
}))

jest.mock('../../app/components/ui/table-skeleton', () => ({
  TableSkeleton: jest.fn(() => <div data-testid="table-skeleton">Loading...</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

describe('LeadsPage', () => {
  const mockRouter = {
    replace: jest.fn(),
  }
  
  const mockSearchParams = new URLSearchParams()
  mockSearchParams.get = jest.fn((param) => {
    switch (param) {
      case 'page':
        return '1'
      case 'per_page':
        return '10'
      default:
        return ''
    }
  })

  beforeEach(() => {
    jest.clearAllMocks()
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/leads',
      },
      writable: true,
    })
    
    // Mock console.log and console.error to avoid cluttering test output
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('renders the leads page with correct components', async () => {
    // Mock useState to control isMounted state
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [false, jest.fn()])
    
    render(<LeadsPage />)
    
    // Initially should show skeleton while mounting
    expect(screen.getByTestId('table-skeleton')).toBeInTheDocument()
    
    // Now mock useState for a second render with isMounted true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    render(<LeadsPage />)
    
    // After mounting, should show the components
    expect(screen.getByText('Leads')).toBeInTheDocument()
    expect(screen.getByTestId('breadcrumb')).toBeInTheDocument()
    expect(screen.getByTestId('leads-filter')).toBeInTheDocument()
    expect(screen.getByTestId('leads-table')).toBeInTheDocument()
  })

  it('passes correct props to LeadsTable', async () => {
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    render(<LeadsPage />)
    
    expect(jest.mocked(require('../../app/components/leads/leads-table').LeadsTable)).toHaveBeenCalledWith(
      expect.objectContaining({
        currentPage: 1,
        perPage: 10,
        filters: expect.objectContaining({
          agent_id: '',
          travel_type: '',
          destination: '',
          departure_state: '',
          departure_city: '',
          travel_duration: '',
          claimed: false,
          booking_status: '',
          start_date: '',
          end_date: '',
        }),
      }),
      expect.anything()
    )
  })

  it('passes correct props to LeadsFilter', async () => {
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    render(<LeadsPage />)
    
    expect(jest.mocked(require('../../app/components/leads/leads-filter').LeadsFilter)).toHaveBeenCalledWith(
      expect.objectContaining({
        onFilterChange: expect.any(Function),
        onReset: expect.any(Function),
        initialFilters: expect.any(Object),
        defaultFilters: expect.any(Object),
      }),
      expect.anything()
    )
  })

  it('updates URL when filters change', async () => {
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    render(<LeadsPage />)
    
    // Get the onFilterChange prop passed to LeadsFilter
    const { onFilterChange } = (jest.mocked(require('../../app/components/leads/leads-filter').LeadsFilter).mock.calls[0][0])
    
    // Call the function with new filters
    onFilterChange({
      agentId: 'agent123',
      travelType: 'cruise',
      destination: 'Caribbean',
      departureState: 'FL',
      departureCity: 'Miami',
      duration: '7-days',
      claimedStatus: 'claimed',
      bookingStatus: 'confirmed',
      startDate: new Date('2023-01-01'),
      endDate: new Date('2023-01-07'),
    })
    
    // Check if router.replace was called with correct URL
    expect(mockRouter.replace).toHaveBeenCalledWith(
      expect.stringContaining('/leads?page=1&per_page=10&agent_id=agent123&travel_type=cruise&destination=Caribbean&departure_state=FL&departure_city=Miami&travel_duration=7-days&claimed=true&booking_status=confirmed&start_date=2023-01-01&end_date=2023-01-07'),
      { scroll: false }
    )
  })

  it('resets filters when reset is called', async () => {
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    render(<LeadsPage />)
    
    // Get the onReset prop passed to LeadsFilter
    const { onReset } = (jest.mocked(require('../../app/components/leads/leads-filter').LeadsFilter).mock.calls[0][0])
    
    // Call the reset function
    onReset()
    
    // Check if router.replace was called with basic URL
    expect(mockRouter.replace).toHaveBeenCalledWith(
      '/leads?page=1&per_page=10',
      { scroll: false }
    )
  })
}) 