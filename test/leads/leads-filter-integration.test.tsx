import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { useRouter, useSearchParams } from 'next/navigation'
import LeadsPage from '../../app/leads/page'
import React from 'react'

// Mock the Next.js navigation hooks
jest.mock('next/navigation', () => ({
  useRouter: jest.fn(),
  useSearchParams: jest.fn(),
}))

// Create a real implementation of LeadsFilter for integration testing
jest.mock('../../app/components/leads/leads-filter', () => ({
  LeadsFilter: jest.fn(({ onFilterChange, onReset }) => (
    <div data-testid="leads-filter">
      <button 
        data-testid="apply-filters" 
        onClick={() => onFilterChange({
          agentId: 'test-agent',
          travelType: 'cruise',
          destination: '',
          departureState: '',
          departureCity: '',
          duration: '',
          claimedStatus: 'claimed',
          bookingStatus: '',
          startDate: undefined,
          endDate: undefined,
        })}
      >
        Apply Filters
      </button>
      <button 
        data-testid="reset-filters" 
        onClick={onReset}
      >
        Reset Filters
      </button>
    </div>
  )),
}))

jest.mock('../../app/components/leads/leads-table', () => ({
  LeadsTable: jest.fn(({ filters }) => (
    <div data-testid="leads-table">
      <div data-testid="current-filters">{JSON.stringify(filters)}</div>
    </div>
  )),
}))

jest.mock('../../app/components/ui/table-skeleton', () => ({
  TableSkeleton: jest.fn(() => <div data-testid="table-skeleton">Loading...</div>),
}))

jest.mock('../../app/components/ui/breadcrumb', () => ({
  Breadcrumb: jest.fn(() => <div data-testid="breadcrumb">Breadcrumb</div>),
}))

describe('LeadsPage Filter Integration', () => {
  const mockRouter = {
    replace: jest.fn(),
  }
  
  let mockSearchParams: any

  beforeEach(() => {
    jest.clearAllMocks()
    
    // Create a fresh mock for search params
    mockSearchParams = new URLSearchParams()
    const paramsMap: Record<string, string> = {
      'page': '1',
      'per_page': '10',
    }
    
    mockSearchParams.get = jest.fn((key) => paramsMap[key] || '')
    
    ;(useRouter as jest.Mock).mockReturnValue(mockRouter)
    ;(useSearchParams as jest.Mock).mockReturnValue(mockSearchParams)
    
    // Mock useState to ensure isMounted is true
    jest.spyOn(React, 'useState').mockImplementationOnce(() => [true, jest.fn()])
    
    // Mock window.location
    Object.defineProperty(window, 'location', {
      value: {
        pathname: '/leads',
      },
      writable: true,
    })
    
    // Mock console methods
    jest.spyOn(console, 'log').mockImplementation(() => {})
    jest.spyOn(console, 'error').mockImplementation(() => {})
  })

  it('applies filters when filter button is clicked', async () => {
    render(<LeadsPage />)
    
    expect(screen.getByTestId('leads-filter')).toBeInTheDocument()
    
    // Click the apply filters button
    fireEvent.click(screen.getByTestId('apply-filters'))
    
    // Check if router.replace was called with the correct URL
    expect(mockRouter.replace).toHaveBeenCalledWith(
      expect.stringContaining('/leads?page=1&per_page=10&agent_id=test-agent&travel_type=cruise&claimed=true'),
      { scroll: false }
    )
  })

  it('resets filters when reset button is clicked', async () => {
    render(<LeadsPage />)
    
    expect(screen.getByTestId('leads-filter')).toBeInTheDocument()
    
    // Click the reset filters button
    fireEvent.click(screen.getByTestId('reset-filters'))
    
    // Check if router.replace was called with the basic URL
    expect(mockRouter.replace).toHaveBeenCalledWith(
      '/leads?page=1&per_page=10',
      { scroll: false }
    )
  })
}) 