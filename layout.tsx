import type { Metadata } from "next"
import "./globals.css"
import { MainLayout } from './app/components/layout/main-layout'
import { RelayProvider } from './app/components/providers/relay-provider'
import { SearchParamsProvider } from './app/components/providers/search-params-provider'
import { cn } from '@/app/lib/utils'
import { Toaster } from "@/app/components/ui/toaster"
import { ApolloWrapper } from './app/lib/apollo-provider'
import { SidebarProvider } from './app/contexts/SidebarContext'

export const metadata: Metadata = {
  title: "Booking Search",
  description: "Search and manage bookings",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn('min-h-screen bg-background font-sans antialiased')}>
        <ApolloWrapper>
          <RelayProvider>
            <SearchParamsProvider>
              <SidebarProvider>
                <MainLayout>{children}</MainLayout>
              </SidebarProvider>
            </SearchParamsProvider>
          </RelayProvider>
        </ApolloWrapper>
        <Toaster />
      </body>
    </html>
  )
}
