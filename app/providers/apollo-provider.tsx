"use client"

import { ApolloC<PERSON>, InM<PERSON>oryCache, ApolloProvider, HttpLink } from "@apollo/client"
import { ReactNode } from "react"

interface ApolloProviderWrapperProps {
  children: ReactNode
}

export function ApolloProviderWrapper({ children }: ApolloProviderWrapperProps) {
  const httpLink = new HttpLink({
    uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
    credentials: "same-origin"
  })

  const client = new ApolloClient({
    link: httpLink,
    cache: new InMemoryCache(),
    defaultOptions: {
      watchQuery: {
        fetchPolicy: "network-only",
        errorPolicy: "all",
      },
      query: {
        fetchPolicy: "network-only",
        errorPolicy: "all",
      },
    },
  })

  return <ApolloProvider client={client}>{children}</ApolloProvider>
} 