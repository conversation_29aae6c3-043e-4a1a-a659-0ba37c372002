import { Icons } from "@/app/components/ui/icons"

interface SidebarLink {
  title: string
  href: string
  icon: keyof typeof Icons
}

interface SidebarConfig {
  title: string
  items: {
    title: string
    href: string
    icon: keyof typeof Icons
  }[]
}

export const sidebarConfig: SidebarConfig[] = [
 
  {
    title: "Agents",
    items: [
      // Agent related items
    ]
  },
  {
    title: "Bookings",
    items: [
      // Booking related items
    ]
  },
 
  {
    title: "Events",
    items: [
      {
        title: "Upload Event",
        href: "/admin/events/upload",
        icon: "calendar"
      },
      {
        title: "Event Calendar",
        href: "/admin/events/calendar",
        icon: "calendar"
      }
    ]
  },
  
]