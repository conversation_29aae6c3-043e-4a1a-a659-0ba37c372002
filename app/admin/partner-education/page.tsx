"use client";

import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Textarea } from "@/app/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Checkbox } from "@/app/components/ui/checkbox";
import { Ta<PERSON>, TabsList, TabsTrigger, TabsContent } from "@/app/components/ui/tabs";
import { useState } from "react";
import { ChevronRight } from "lucide-react";

interface EducationItem {
  id: number;
  title: string;
  description: string;
  category: string;
  sortOrder: number;
  link: string;
  image: string;
  regions: string[];
}

export default function PartnerEducationPage() {
  const [activeTab, setActiveTab] = useState("all");

  return (
    <div className="p-8 pt-6 space-y-6">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-muted-foreground">
        <a href="/admin" className="hover:text-primary transition-colors">
          Admin
        </a>
        <ChevronRight className="h-4 w-4 mx-2" />
        <span className="text-foreground">Partner Education</span>
      </div>

      <div className="flex justify-between items-center">
        <h1 className="text-2xl font-bold">Partner Education Management</h1>
      </div>

      <Card className="rounded-none">
        <CardHeader>
          <CardTitle>Add New Education Content</CardTitle>
        </CardHeader>
        <CardContent>
          <form className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="title">Title</Label>
                <Input id="title" maxLength={100} placeholder="Enter title" className="rounded-none" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="sortOrder">Sort Order</Label>
                <Input id="sortOrder" type="number" max={9999} defaultValue={1} className="rounded-none" />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                placeholder="Enter description"
                rows={4}
                className="rounded-none"
              />
              <p className="text-sm text-muted-foreground">
                HTML formatting supported
              </p>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label htmlFor="link">Link</Label>
                <Input id="link" type="url" maxLength={250} placeholder="https://..." className="rounded-none" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="category">Category</Label>
                <Select>
                  <SelectTrigger className="rounded-none">
                    <SelectValue placeholder="Select category" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none">
                    <SelectItem value="1">Affiliated Organization</SelectItem>
                    <SelectItem value="2">Supplier Course</SelectItem>
                    <SelectItem value="3">Special Interest Course</SelectItem>
                    <SelectItem value="4">Destination Specialist</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Regional Visibility</Label>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {["US", "UK", "IE", "MX"].map((region) => (
                  <div key={region} className="flex items-center space-x-2">
                    <Checkbox id={`region-${region}`} className="rounded-none" />
                    <Label htmlFor={`region-${region}`}>Show in {region}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex justify-end">
              <Button type="submit" className="rounded-none">Add Education Content</Button>
            </div>
          </form>
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader>
          <CardTitle>Current Education Content</CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="all" className="w-full">
            <TabsList className="rounded-none">
              <TabsTrigger value="all" className="rounded-none">All Categories</TabsTrigger>
              <TabsTrigger value="affiliated" className="rounded-none">Affiliated Organizations</TabsTrigger>
              <TabsTrigger value="supplier" className="rounded-none">Supplier Courses</TabsTrigger>
            </TabsList>
            
            <TabsContent value="all" className="mt-6">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Example Education Item */}
                <Card className="rounded-none">
                  <CardContent className="pt-6">
                    <div className="flex items-start justify-between">
                      <div>
                        <h3 className="font-semibold">Sample Education Title</h3>
                        <p className="text-sm text-muted-foreground">
                          Affiliated Organization
                        </p>
                      </div>
                      <span className="text-sm text-muted-foreground">Sort: 1</span>
                    </div>
                    
                    <div className="mt-4 space-y-2">
                      <div className="w-32 h-24 bg-muted rounded" />
                      <p className="text-sm">Sample description text goes here...</p>
                      <a href="#" className="text-primary hover:underline text-sm">
                        View Link →
                      </a>
                    </div>

                    <div className="mt-4 flex flex-wrap gap-2">
                      {["US", "UK"].map((region) => (
                        <span
                          key={region}
                          className="px-2 py-1 bg-primary/10 text-primary text-xs rounded"
                        >
                          {region}
                        </span>
                      ))}
                    </div>

                    <div className="mt-4 flex justify-end space-x-2">
                      <Button variant="ghost" size="sm" className="rounded-none">
                        Edit
                      </Button>
                      <Button variant="ghost" size="sm" className="text-destructive rounded-none">
                        Delete
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader>
          <CardTitle>HTML Formatting Guide</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-2 text-sm text-muted-foreground">
            <p><strong>Links:</strong> &lt;a href="http://example.com/"&gt;link text&lt;/a&gt;</p>
            <p><strong>New Window Links:</strong> &lt;a href="http://example.com/" target="_blank"&gt;link text&lt;/a&gt;</p>
            <p><strong>Bold Text:</strong> &lt;strong&gt;bold text&lt;/strong&gt;</p>
            <p><strong>Italic Text:</strong> &lt;em&gt;italic text&lt;/em&gt;</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 