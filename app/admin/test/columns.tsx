"use client";

import { Badge, badgeVariants } from "@/app/components/ui/badge";
import { Button } from "@/app/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import { format } from "date-fns";
import { type VariantProps } from "class-variance-authority"

interface TestResult {
  id: string;
  test_file_path: string;
  test_name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error_message: string | null;
  timestamp: string;
  branch: string;
  commit_hash: string;
  created_at: string;
}

type BadgeVariant = VariantProps<typeof badgeVariants>["variant"]

export const columns = [
  {
    key: "test_name",
    header: "Test Name",
  },
  {
    key: "test_file_path",
    header: "File Path",
  },
  {
    key: "status",
    header: "Status",
    render: (result: TestResult) => (
      <Badge variant={
        result.status === "passed" ? "success" as BadgeVariant :
        result.status === "failed" ? "destructive" as BadgeVariant :
        "secondary" as BadgeVariant
      }>
        {result.status}
      </Badge>
    ),
  },
  {
    key: "duration",
    header: "Duration",
    render: (result: TestResult) => `${result.duration}ms`,
  },
  {
    key: "branch",
    header: "Branch",
  },
  {
    key: "timestamp",
    header: "Timestamp",
    render: (result: TestResult) => format(new Date(result.timestamp), "PPpp"),
  },
  {
    key: "actions",
    header: "Actions",
    render: (result: TestResult) => result.error_message ? (
      <Dialog>
        <DialogTrigger asChild>
          <Button variant="ghost" size="sm">
            View Error
          </Button>
        </DialogTrigger>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Error Details</DialogTitle>
          </DialogHeader>
          <div className="mt-4">
            <pre className="bg-secondary p-4 rounded-lg overflow-auto max-h-[400px] text-sm">
              {result.error_message}
            </pre>
          </div>
        </DialogContent>
      </Dialog>
    ) : null,
  },
]; 