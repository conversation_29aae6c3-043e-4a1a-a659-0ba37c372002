"use client";

import { createClient } from '@supabase/supabase-js';
import { Card, CardContent } from '@/app/components/ui/card';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { Badge, badgeVariants } from "@/app/components/ui/badge";
import { format } from "date-fns";
import { Button } from "@/app/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog";
import { Breadcrumb } from "@/app/components/ui/breadcrumb";
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select";
import { Input } from "@/app/components/ui/input";
import { 
  Pagination, 
  PaginationContent, 
  PaginationEllipsis, 
  PaginationItem, 
  PaginationLink, 
  PaginationNext, 
  PaginationPrevious 
} from "@/app/components/ui/pagination";
import { useSearch<PERSON>ara<PERSON>, useRouter, usePathname } from "next/navigation";
import { useCallback, useEffect, useState } from "react";
import { type VariantProps } from "class-variance-authority";
import { Loader2Icon, FilterIcon, XIcon } from "lucide-react";

export const revalidate = 0; // Disable caching for this route
export const dynamic = 'force-dynamic'; // Force dynamic rendering

interface TestResult {
  id: string;
  test_file_path: string;
  test_name: string;
  status: 'passed' | 'failed' | 'skipped';
  duration: number;
  error_message: string | null;
  timestamp: string;
  branch: string;
  commit_hash: string;
  created_at: string;
}

type BadgeVariant = VariantProps<typeof badgeVariants>["variant"];

export default function TestResultsPage() {
  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  
  // State for test results and loading
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Get initial values from URL
  const getInitialParam = (param: string, defaultValue: string = '') => {
    return searchParams.get(param) || defaultValue;
  };
  
  // Pagination state
  const [page, setPage] = useState(getInitialParam('page', '1'));
  const [pageSize, setPageSize] = useState(getInitialParam('pageSize', '10'));
  
  // Filter state
  const [statusFilter, setStatusFilter] = useState(getInitialParam('status'));
  const [branchFilter, setBranchFilter] = useState(getInitialParam('branch'));
  const [searchQuery, setSearchQuery] = useState(getInitialParam('search'));
  const [availableBranches, setAvailableBranches] = useState<string[]>([]);

  // Add a state for the current time
  const [currentTime, setCurrentTime] = useState('');
  
  // Set the current time once when component mounts on client
  useEffect(() => {
    setCurrentTime(format(new Date(), "PPpp"));
  }, []);

  // Create a memoized function for updating the URL
  const createQueryString = useCallback(
    (params: Record<string, string | null>) => {
      const newSearchParams = new URLSearchParams(searchParams.toString());
      
      Object.entries(params).forEach(([name, value]) => {
        if (value === null) {
          newSearchParams.delete(name);
        } else {
          newSearchParams.set(name, value);
        }
      });
      
      return newSearchParams.toString();
    },
    [searchParams]
  );
  
  // Update URL when state changes
  const updateUrl = useCallback(() => {
    const params: Record<string, string | null> = {
      page,
      pageSize,
      status: statusFilter || null,
      branch: branchFilter || null,
      search: searchQuery || null
    };
    
    const queryString = createQueryString(params);
    router.push(`${pathname}?${queryString}`, { scroll: false });
  }, [page, pageSize, statusFilter, branchFilter, searchQuery, createQueryString, pathname, router]);
  
  // Update URL when state changes
  useEffect(() => {
    updateUrl();
  }, [page, pageSize, statusFilter, branchFilter, searchQuery, updateUrl]);

  // Fetch test results
  const fetchTestResults = useCallback(async () => {
    setIsLoading(true);
    setError(null);

    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      setError('Supabase environment variables are not configured');
      setIsLoading(false);
      return;
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        auth: {
          persistSession: false
        }
      }
    );

    try {
      // Build query with filters
      let query = supabase
        .from('test_results')
        .select('*', { count: 'exact' });

      // Apply filters
      if (statusFilter && statusFilter !== "all") {
        query = query.eq('status', statusFilter);
      }
      
      if (branchFilter && branchFilter !== "all") {
        query = query.eq('branch', branchFilter);
      }
      
      if (searchQuery) {
        query = query.or(`test_name.ilike.%${searchQuery}%,test_file_path.ilike.%${searchQuery}%`);
      }

      // Calculate pagination
      const currentPage = parseInt(page) || 1;
      const itemsPerPage = parseInt(pageSize) || 10;
      const from = (currentPage - 1) * itemsPerPage;
      const to = from + itemsPerPage - 1;

      // Apply pagination and ordering
      const { data, error, count } = await query
        .order('created_at', { ascending: false })
        .range(from, to);

      if (error) {
        throw error;
      }

      setTestResults(data as TestResult[]);
      setTotalCount(count || 0);
      
      // Fetch available branches for filter dropdown
      const { data: branchesData } = await supabase
        .from('test_results')
        .select('branch')
        .order('branch')
        .limit(100);
        
      if (branchesData) {
        const uniqueBranches = Array.from(new Set(branchesData.map(item => item.branch)));
        setAvailableBranches(uniqueBranches);
      }
    } catch (err) {
      console.error('Error fetching test results:', err);
      const errorMessage = err instanceof Error ? err.message : 'Unknown error occurred';
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [page, pageSize, statusFilter, branchFilter, searchQuery]);

  // Fetch data when filters or pagination changes
  useEffect(() => {
    fetchTestResults();
  }, [fetchTestResults]);

  // Reset page when filters change
  useEffect(() => {
    setPage("1");
  }, [statusFilter, branchFilter, searchQuery]);

  // Handle page size change
  const handlePageSizeChange = (value: string) => {
    setPageSize(value);
    setPage("1");
  };

  // Handle filter reset
  const handleResetFilters = () => {
    setStatusFilter("all");
    setBranchFilter("all");
    setSearchQuery("");
    setPage("1");
  };

  // Calculate pagination values
  const currentPage = parseInt(page) || 1;
  const itemsPerPage = parseInt(pageSize) || 10;
  const totalPages = Math.ceil(totalCount / itemsPerPage);

  // Generate pagination items
  const getPaginationItems = () => {
    const items = [];
    
    // Always show first page
    items.push(
      <PaginationItem key="first">
        <PaginationLink 
          href={`${pathname}?${createQueryString({ page: "1" })}`}
          isActive={currentPage === 1}
        >
          1
        </PaginationLink>
      </PaginationItem>
    );
    
    // Show ellipsis if needed
    if (currentPage > 3) {
      items.push(
        <PaginationItem key="ellipsis-1">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Show pages around current page
    for (let i = Math.max(2, currentPage - 1); i <= Math.min(totalPages - 1, currentPage + 1); i++) {
      if (i <= 1 || i >= totalPages) continue;
      items.push(
        <PaginationItem key={i}>
          <PaginationLink 
            href={`${pathname}?${createQueryString({ page: i.toString() })}`}
            isActive={currentPage === i}
          >
            {i}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    // Show ellipsis if needed
    if (currentPage < totalPages - 2) {
      items.push(
        <PaginationItem key="ellipsis-2">
          <PaginationEllipsis />
        </PaginationItem>
      );
    }
    
    // Always show last page if there's more than one page
    if (totalPages > 1) {
      items.push(
        <PaginationItem key="last">
          <PaginationLink 
            href={`${pathname}?${createQueryString({ page: totalPages.toString() })}`}
            isActive={currentPage === totalPages}
          >
            {totalPages}
          </PaginationLink>
        </PaginationItem>
      );
    }
    
    return items;
  };

  return (
    <div className="p-8 pl-8">
      <div className="space-y-6">
        <Breadcrumb/>

        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-semibold">Test Results</h1>
          <div className="text-sm text-muted-foreground">
            {/* Use the state value instead of direct formatting */}
            Last updated: {currentTime || 'Loading...'}
          </div>
        </div>

        {/* Filters */}
        <div className="bg-card border rounded-md p-4">
          <div className="flex flex-col sm:flex-row gap-4 items-start sm:items-center">
            <div className="flex-1 w-full sm:w-auto">
              <Input
                placeholder="Search test name or file path..."
                value={searchQuery || ""}
                onChange={(e) => setSearchQuery(e.target.value || "")}
                className="w-full"
              />
            </div>
            
            <div className="flex flex-wrap gap-2 items-center">
              <Select
                value={statusFilter || "all"}
                onValueChange={(value) => setStatusFilter(value)}
              >
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  <SelectItem value="passed">Passed</SelectItem>
                  <SelectItem value="failed">Failed</SelectItem>
                  <SelectItem value="skipped">Skipped</SelectItem>
                </SelectContent>
              </Select>
              
              <Select
                value={branchFilter || "all"}
                onValueChange={(value) => setBranchFilter(value)}
              >
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Branch" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Branches</SelectItem>
                  {availableBranches.map(branch => (
                    <SelectItem key={branch} value={branch}>{branch}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
              
              {(statusFilter || branchFilter || searchQuery) && (
                <Button 
                  variant="outline" 
                  size="icon"
                  onClick={handleResetFilters}
                  title="Clear filters"
                >
                  <XIcon className="h-4 w-4" />
                </Button>
              )}
            </div>
          </div>
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-12">
            <Loader2Icon className="h-8 w-8 animate-spin text-primary" />
          </div>
        ) : error ? (
          <Card>
            <CardContent className="py-8">
              <div className="text-center space-y-4">
                <Badge variant="destructive" className="mb-2">Connection Error</Badge>
                <h3 className="text-lg font-medium">Failed to fetch test results</h3>
                <p className="text-muted-foreground max-w-md mx-auto">
                  There was a problem connecting to the database. Please check your network connection and Supabase configuration.
                </p>
                <div className="bg-secondary p-4 rounded-lg overflow-auto max-h-[200px] text-sm text-left mx-auto max-w-2xl">
                  <code>{error}</code>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : testResults.length === 0 ? (
          <Card>
            <CardContent className="text-center text-muted-foreground py-8">
              {(statusFilter || branchFilter || searchQuery) ? (
                <div className="space-y-2">
                  <p>No test results match your filters.</p>
                  <Button variant="outline" onClick={handleResetFilters}>
                    Clear Filters
                  </Button>
                </div>
              ) : (
                <p>No test results found. Make sure your database is properly configured and contains test data.</p>
              )}
            </CardContent>
          </Card>
        ) : (
          <>
            <div className="flex items-center justify-between mb-2">
              <div className="text-sm text-muted-foreground">
                Showing {((currentPage - 1) * itemsPerPage) + 1} to {Math.min(currentPage * itemsPerPage, totalCount)} of {totalCount} results
              </div>
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Rows per page:</span>
                <Select
                  value={pageSize}
                  onValueChange={handlePageSizeChange}
                >
                  <SelectTrigger className="w-[70px]">
                    <SelectValue placeholder={pageSize} />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="25">25</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div className="border bg-card rounded-md overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Test Name</TableHead>
                    <TableHead>File Path</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Duration</TableHead>
                    <TableHead>Branch</TableHead>
                    <TableHead>Timestamp</TableHead>
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {testResults.map((result) => (
                    <TableRow key={result.id}>
                      <TableCell className="font-medium">{result.test_name}</TableCell>
                      <TableCell className="text-sm text-muted-foreground">{result.test_file_path}</TableCell>
                      <TableCell>
                        <Badge variant={
                          (result.status === "passed" ? "success" :
                          result.status === "failed" ? "destructive" :
                          "secondary") as BadgeVariant
                        }>
                          {result.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{result.duration}ms</TableCell>
                      <TableCell>{result.branch}</TableCell>
                      <TableCell>
                        {format(new Date(result.timestamp), "PPpp")}
                      </TableCell>
                      <TableCell>
                        {result.error_message && (
                          <Dialog>
                            <DialogTrigger asChild>
                              <Button variant="ghost" size="sm">
                                View Error
                              </Button>
                            </DialogTrigger>
                            <DialogContent>
                              <DialogHeader>
                                <DialogTitle>Error Details</DialogTitle>
                              </DialogHeader>
                              <div className="mt-4">
                                <pre className="bg-secondary p-4 rounded-lg overflow-auto max-h-[400px] text-sm">
                                  {result.error_message}
                                </pre>
                              </div>
                            </DialogContent>
                          </Dialog>
                        )}
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
            
            {/* Pagination */}
            {totalPages > 1 && (
              <Pagination className="mt-4">
                <PaginationContent>
                  <PaginationItem>
                    <PaginationPrevious 
                      href={`${pathname}?${createQueryString({ page: (currentPage > 1 ? (currentPage - 1).toString() : "1") })}`}
                      aria-disabled={currentPage === 1}
                      className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                  
                  {getPaginationItems()}
                  
                  <PaginationItem>
                    <PaginationNext 
                      href={`${pathname}?${createQueryString({ page: (currentPage < totalPages ? (currentPage + 1).toString() : totalPages.toString()) })}`}
                      aria-disabled={currentPage === totalPages}
                      className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
                    />
                  </PaginationItem>
                </PaginationContent>
              </Pagination>
            )}
          </>
        )}
      </div>
    </div>
  );
} 