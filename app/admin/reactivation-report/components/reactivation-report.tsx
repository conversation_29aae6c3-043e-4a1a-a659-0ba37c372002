"use client"

import { useState } from "react"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Button } from "@/app/components/ui/button"
import { ReportTable } from "./report-table"
import { useReactivationReport } from "../hooks/use-reactivation-report"

export function ReactivationReport() {
  const { data, isLoading, error, fetchReport } = useReactivationReport()
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [startDate, setStartDate] = useState<Date | null>(null)
  const [endDate, setEndDate] = useState<Date | null>(null)

  return (
    <div className="space-y-6 max-w-[1600px]">
      <div className="border bg-card">
        <div className="flex items-center justify-between border-b px-6 h-14">
          <h2 className="font-medium">Filters</h2>
          <div className="flex items-center gap-3">
            <Button 
              variant="ghost" 
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? "Expand" : "Collapse"}
            </Button>
            <div className="h-4 w-[1px] bg-border" />
            <Button variant="ghost">Reset</Button>
            <Button>Generate Report</Button>
          </div>
        </div>

        {!isCollapsed && (
          <div className="p-6">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="agentId">Agent ID</Label>
                <Input id="agentId" placeholder="Enter Agent ID" className="rounded-none" />
              </div>
              <div className="space-y-2">
                <Label>Start Date</Label>
                <DatePicker date={startDate} setDate={setStartDate} />
              </div>
              <div className="space-y-2">
                <Label>End Date</Label>
                <DatePicker date={endDate} setDate={setEndDate} />
              </div>
            </div>
          </div>
        )}
      </div>

      {error && (
        <div className="bg-destructive/15 text-destructive p-4">
          {error.message}
        </div>
      )}

      <div className="border">
        <ReportTable data={data} isLoading={isLoading} />
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <a href="/" className="hover:underline">InteleTravel Homepage</a>
        {' / '}
        <a href="/admin/mainmenu" className="hover:underline">Administrator Main Menu</a>
      </div>
    </div>
  )
} 