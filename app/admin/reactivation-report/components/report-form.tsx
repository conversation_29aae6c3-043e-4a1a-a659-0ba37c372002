"use client"

import { useState } from "react"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { SearchIcon } from "lucide-react"

interface ReportFormProps {
  onSubmit: (startDate: string, endDate: string) => Promise<void>
}

export function ReportForm({ onSubmit }: ReportFormProps) {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <form 
      className="flex items-center gap-4" 
      onSubmit={(e) => {
        e.preventDefault()
        onSubmit(startDate, endDate)
      }}
    >
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">From:</label>
        <Input
          type="date"
          value={startDate}
          onChange={(e) => setStartDate(e.target.value)}
          className="rounded-none"
          required
        />
      </div>

      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium">To:</label>
        <Input
          type="date"
          value={endDate}
          onChange={(e) => setEndDate(e.target.value)}
          className="rounded-none"
          required
        />
      </div>

      <Button 
        type="submit" 
        className="rounded-none bg-[#3C36A9] hover:bg-[#3C36A9]/90 text-white flex items-center gap-2 whitespace-nowrap mt-8"
      >
        <SearchIcon className="h-4 w-4" />
        Generate Report
      </Button>
    </form>
  )
} 