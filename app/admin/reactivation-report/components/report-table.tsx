"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { ReactivationRecord } from "../types"

interface ReportTableProps {
  data: ReactivationRecord[]
  isLoading?: boolean
}

export function ReportTable({ data, isLoading }: ReportTableProps) {
  if (isLoading) {
    return <div className="text-center py-4">Loading report data...</div>
  }

  if (!data?.length) {
    return <div className="text-center py-4">No records found for the selected criteria.</div>
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>PIN</TableHead>
          <TableHead>Name</TableHead>
          <TableHead>Inactivate Date</TableHead>
          <TableHead>Reactivate Date</TableHead>
          <TableHead>Months Inactive</TableHead>
          <TableHead>Amount</TableHead>
          <TableHead>Contact Date</TableHead>
          <TableHead>Contact Type</TableHead>
          <TableHead>Enroll Date</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((record) => (
          <TableRow key={record.pin}>
            <TableCell>
              <a 
                href={`/agents/${record.pin}`} 
                className="text-[#3C36A9] hover:underline"
              >
                {record.pin}
              </a>
            </TableCell>
            <TableCell>{record.name}</TableCell>
            <TableCell>{record.inactivateDate}</TableCell>
            <TableCell>{record.reactivateDate}</TableCell>
            <TableCell>{record.monthsInactive}</TableCell>
            <TableCell>${record.amount.toFixed(2)}</TableCell>
            <TableCell>{record.contactDate}</TableCell>
            <TableCell>{record.contactType}</TableCell>
            <TableCell>{record.enrollDate}</TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  )
} 