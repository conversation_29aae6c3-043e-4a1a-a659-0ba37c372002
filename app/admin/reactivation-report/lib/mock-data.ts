import { ReactivationRecord } from "../types"

// Helper to get formatted date string
function getFormattedDate(daysAgo: number) {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toLocaleDateString('en-US')
}

export const mockData: ReactivationRecord[] = [
  {
    pin: "12345",
    name: "<PERSON>",
    inactivateDate: getFormattedDate(45),
    reactivateDate: getFormattedDate(15),
    monthsInactive: 1,
    amount: 150.00,
    contactDate: getFormattedDate(16),
    contactType: "Phone",
    enrollDate: getFormattedDate(180)
  },
  {
    pin: "12346",
    name: "<PERSON>",
    inactivateDate: getFormattedDate(90),
    reactivateDate: getFormattedDate(15),
    monthsInactive: 2.5,
    amount: 300.00,
    contactDate: getFormattedDate(17),
    contactType: "Email",
    enrollDate: getFormattedDate(365)
  },
  {
    pin: "12347",
    name: "<PERSON>",
    inactivateDate: getFormattedDate(75),
    reactivateDate: getFormattedDate(14),
    monthsInactive: 2,
    amount: 250.00,
    contactDate: getFormattedDate(15),
    contactType: "Phone",
    enrollDate: getFormattedDate(545)
  }
] 