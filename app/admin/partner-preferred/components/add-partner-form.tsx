"use client"

import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Checkbox } from "@/app/components/ui/checkbox"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/app/components/ui/form"
import { ImageUpload } from "./image-upload"

const partnerFormSchema = z.object({
  title: z.string().min(1).max(100),
  sort: z.number().min(1).max(9999),
  link: z.string().url().max(250),
  photo: z.instanceof(File).optional(),
  showUK: z.boolean(),
  showIE: z.boolean(),
})

type PartnerFormValues = z.infer<typeof partnerFormSchema>

export function AddPartnerForm() {
  const form = useForm<PartnerFormValues>({
    resolver: zodResolver(partnerFormSchema),
    defaultValues: {
      sort: 1,
      showUK: false,
      showIE: false,
    },
  })

  async function onSubmit(data: PartnerFormValues) {
    try {
      const formData = new FormData()
      Object.entries(data).forEach(([key, value]) => {
        if (value instanceof File) {
          formData.append(key, value)
        } else {
          formData.append(key, String(value))
        }
      })
      
      // TODO: Implement API endpoint
      const response = await fetch("/api/admin/partners/preferred", {
        method: "POST",
        body: formData,
      })
      
      if (!response.ok) throw new Error("Failed to add partner")
      
      form.reset()
    } catch (error) {
      console.error("Failed to add partner:", error)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <FormField
            control={form.control}
            name="title"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Partner Title</FormLabel>
                <FormControl>
                  <Input {...field} maxLength={100} />
                </FormControl>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="sort"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Sort Order</FormLabel>
                <FormControl>
                  <Input 
                    type="number" 
                    {...field} 
                    onChange={e => field.onChange(parseInt(e.target.value))}
                  />
                </FormControl>
              </FormItem>
            )}
          />
        </div>

        <FormField
          control={form.control}
          name="link"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Partner Link</FormLabel>
              <FormControl>
                <Input {...field} type="url" placeholder="https://" />
              </FormControl>
            </FormItem>
          )}
        />

        <FormField
          control={form.control}
          name="photo"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Partner Logo</FormLabel>
              <FormControl>
                <ImageUpload 
                  onChange={file => field.onChange(file)}
                  value={field.value}
                />
              </FormControl>
            </FormItem>
          )}
        />

        <div className="flex space-x-4">
          <FormField
            control={form.control}
            name="showUK"
            render={({ field }) => (
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <Checkbox 
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormLabel className="!mt-0">Show in UK</FormLabel>
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="showIE"
            render={({ field }) => (
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <Checkbox 
                    checked={field.value}
                    onCheckedChange={field.onChange}
                  />
                </FormControl>
                <FormLabel className="!mt-0">Show in IE</FormLabel>
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end">
          <Button type="submit">Add Partner</Button>
        </div>
      </form>
    </Form>
  )
} 