"use client"

import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Checkbox } from "@/app/components/ui/checkbox"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
} from "@/app/components/ui/form"
import { ImageUpload } from "./image-upload"
import { Card } from "@/app/components/ui/card"

interface Partner {
  id: number
  title: string
  sort: number
  link: string
  photo: string
  showUK: boolean
  showIE: boolean
}

const partnerSchema = z.object({
  id: z.number(),
  title: z.string().min(1).max(100),
  sort: z.number().min(1).max(9999),
  link: z.string().url().max(250),
  photo: z.instanceof(File).optional(),
  showUK: z.boolean(),
  showIE: z.boolean(),
  delete: z.boolean().optional(),
})

type PartnerFormValues = z.infer<typeof partnerSchema>

export function ExistingPartnersList() {
  const form = useForm<{ partners: PartnerFormValues[] }>({
    resolver: zodResolver(z.object({
      partners: z.array(partnerSchema)
    })),
    defaultValues: {
      partners: []
    }
  })

  async function onSubmit(data: { partners: PartnerFormValues[] }) {
    try {
      const formData = new FormData()
      data.partners.forEach((partner, index) => {
        Object.entries(partner).forEach(([key, value]) => {
          if (value instanceof File) {
            formData.append(`partners[${index}].${key}`, value)
          } else {
            formData.append(`partners[${index}].${key}`, String(value))
          }
        })
      })

      // TODO: Implement API endpoint
      const response = await fetch("/api/admin/partners/preferred", {
        method: "PUT",
        body: formData,
      })

      if (!response.ok) throw new Error("Failed to update partners")
    } catch (error) {
      console.error("Failed to update partners:", error)
    }
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {form.watch("partners").map((partner, index) => (
          <Card key={partner.id} className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name={`partners.${index}.title`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input {...field} maxLength={100} />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`partners.${index}.sort`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sort Order</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field}
                          onChange={e => field.onChange(parseInt(e.target.value))}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name={`partners.${index}.link`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Link</FormLabel>
                      <FormControl>
                        <Input {...field} type="url" />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <div className="flex space-x-4">
                  <FormField
                    control={form.control}
                    name={`partners.${index}.showUK`}
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox 
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="!mt-0">UK</FormLabel>
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`partners.${index}.showIE`}
                    render={({ field }) => (
                      <FormItem className="flex items-center space-x-2">
                        <FormControl>
                          <Checkbox 
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <FormLabel className="!mt-0">IE</FormLabel>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name={`partners.${index}.photo`}
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Logo</FormLabel>
                      <FormControl>
                        <ImageUpload 
                          onChange={file => field.onChange(file)}
                          value={field.value}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name={`partners.${index}.delete`}
                  render={({ field }) => (
                    <FormItem className="flex items-center justify-end space-x-2">
                      <FormControl>
                        <Checkbox 
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                      <FormLabel className="!mt-0 text-red-600">Delete Partner</FormLabel>
                    </FormItem>
                  )}
                />
              </div>
            </div>
          </Card>
        ))}

        <div className="flex justify-end">
          <Button type="submit">Update Partners</Button>
        </div>
      </form>
    </Form>
  )
} 