"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>ead<PERSON>, Card<PERSON>itle } from "@/app/components/ui/card"
import { AddPartnerForm } from "./add-partner-form"
import { ExistingPartnersList } from "./existing-partners-list"

export function PartnerPreferredManagement() {
  return (
    <main className="mx-auto px-4 py-8 space-y-8">
      <Card className="rounded-none">
        <CardHeader className="rounded-none">
          <CardTitle>Add New Preferred Partner</CardTitle>
        </CardHeader>
        <CardContent className="rounded-none">
          <AddPartnerForm />
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader className="rounded-none">
          <CardTitle>Current Preferred Partners</CardTitle>
        </CardHeader>
        <CardContent className="rounded-none">
          <ExistingPartnersList />
        </CardContent>
      </Card>
    </main>
  )
} 