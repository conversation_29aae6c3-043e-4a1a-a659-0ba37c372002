"use client"

import { useState } from "react"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Search, Plus } from "lucide-react"
import Image from "next/image"
import { Tutorial } from "../types"

interface TutorialListProps {
  tutorials?: Tutorial[]
}

export function TutorialList({ tutorials = [] }: TutorialListProps) {
  const [searchQuery, setSearchQuery] = useState("")

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="relative flex-1 max-w-sm">
          <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search tutorials..."
            className="pl-8"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
          />
        </div>
        <Button>
          <Plus className="mr-2 h-4 w-4" />
          Add Tutorial
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {tutorials.map((tutorial) => (
          <Card key={tutorial.id} className="p-4">
            <div className="space-y-2">
              <Image 
                src={tutorial.thumbnailUrl} 
                alt={tutorial.title}
                width={400}
                height={300}
                className="w-full h-40 object-cover rounded-md"
              />
              <h3 className="font-semibold">{tutorial.title}</h3>
              <p className="text-sm text-muted-foreground">{tutorial.description}</p>
              <div className="flex justify-end">
                <Button variant="outline" size="sm">
                  View Tutorial
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 