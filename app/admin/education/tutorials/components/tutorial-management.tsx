"use client"

import { useState } from "react"
import { Card } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/app/components/ui/tabs"
import { TutorialList } from "./tutorial-list"
import { FeaturedTutorials } from "./featured-tutorials"
import { WeeklyCallsList } from "./weekly-calls-list"
import { dummyTutorials, dummyWeeklyCalls } from "../data/dummy-data"

interface Tutorial {
  id: string
  title: string
  description: string
  url: string
  category: "basics" | "supplier" | "weekly"
  thumbnailUrl: string
  featured?: boolean
}

export function TutorialManagement() {
  const [activeTab, setActiveTab] = useState("tutorials")
  const featuredTutorials = dummyTutorials.filter(tutorial => tutorial.featured)

  return (
    <Card>
      <div className="p-6 space-y-6">
        <Tabs defaultValue="tutorials" onValueChange={setActiveTab} className="w-full">
          <TabsList className="w-full justify-start">
            <TabsTrigger value="tutorials">All Tutorials</TabsTrigger>
            <TabsTrigger value="featured">Featured Content</TabsTrigger>
            <TabsTrigger value="weekly-calls">Weekly Calls</TabsTrigger>
          </TabsList>
          
          <div className="mt-6">
            <TabsContent value="tutorials">
              <TutorialList tutorials={dummyTutorials} />
            </TabsContent>
            
            <TabsContent value="featured">
              <FeaturedTutorials featuredTutorials={featuredTutorials} />
            </TabsContent>
            
            <TabsContent value="weekly-calls">
              <WeeklyCallsList calls={dummyWeeklyCalls} />
            </TabsContent>
          </div>
        </Tabs>
      </div>
    </Card>
  )
} 