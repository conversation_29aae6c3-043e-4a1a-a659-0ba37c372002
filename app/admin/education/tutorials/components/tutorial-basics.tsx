"use client"

import { Card } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { PlayCircle } from "lucide-react"
import Link from "next/link"
import { Tutorial } from "../types"

interface TutorialBasicsProps {
  tutorials?: Tutorial[]
}

export function TutorialBasics({ tutorials = [] }: TutorialBasicsProps) {
  const featuredTutorials = tutorials.filter(tutorial => tutorial.featured)

  return (
    <div className="grid gap-6 pt-4">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-semibold">Featured Tutorials</h2>
        <Link href="/admin/education/tutorials/all">
          <Button variant="link">
            View All Tutorials
          </Button>
        </Link>
      </div>

      <div className="grid md:grid-cols-2 gap-6">
        {featuredTutorials.map((tutorial) => (
          <Card key={tutorial.id} className="overflow-hidden">
            <div className="relative aspect-video">
              <img 
                src={tutorial.thumbnailUrl}
                alt={tutorial.title}
                className="object-cover"
              />
              <Button
                variant="ghost"
                size="icon"
                className="absolute inset-0 w-full h-full bg-black/50 opacity-0 hover:opacity-100 transition-opacity"
                onClick={() => window.open(tutorial.url)}
              >
                <PlayCircle className="w-12 h-12 text-white" />
              </Button>
            </div>
            <div className="p-4">
              <h3 className="font-medium">{tutorial.title}</h3>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 