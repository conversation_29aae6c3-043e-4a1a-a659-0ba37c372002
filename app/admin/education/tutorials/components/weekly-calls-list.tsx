"use client"

import { Card } from "@/app/components/ui/card"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Calendar, Phone } from "lucide-react"

interface WeeklyCall {
  id: string
  date: string
  title: string
  description: string
  recordingUrl?: string
}

interface WeeklyCallsListProps {
  calls?: WeeklyCall[]
}

export function WeeklyCallsList({ calls = [] }: WeeklyCallsListProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-lg font-semibold">Weekly Training Calls</h2>
          <p className="text-sm text-muted-foreground">
            Every Thursday at 6pm PT / 9pm ET
          </p>
        </div>
        <Button>
          <Calendar className="mr-2 h-4 w-4" />
          Schedule Call
        </Button>
      </div>

      <div className="space-y-4">
        {calls.map((call) => (
          <Card key={call.id} className="p-4">
            <div className="flex items-start gap-4">
              <Phone className="h-5 w-5 mt-1" />
              <div className="flex-1 space-y-1">
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold">{call.title}</h3>
                  <time className="text-sm text-muted-foreground">
                    {call.date}
                  </time>
                </div>
                <p className="text-sm text-muted-foreground">{call.description}</p>
                {call.recordingUrl && (
                  <Button variant="outline" size="sm">
                    Listen to Recording
                  </Button>
                )}
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 