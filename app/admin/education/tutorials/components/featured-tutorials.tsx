"use client"

import { Card } from "@/app/components/ui/card"
import { But<PERSON> } from "@/app/components/ui/button"
import { Star } from "lucide-react"
import Image from "next/image"
import { Tutorial } from "../types"

interface FeaturedTutorialsProps {
  featuredTutorials?: Tutorial[]
}

export function FeaturedTutorials({ featuredTutorials = [] }: FeaturedTutorialsProps) {
  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <h2 className="text-lg font-semibold">Featured Tutorials</h2>
        <Button variant="outline">
          <Star className="mr-2 h-4 w-4" />
          Manage Featured
        </Button>
      </div>

      <div className="grid gap-4 md:grid-cols-2">
        {featuredTutorials.map((tutorial) => (
          <Card key={tutorial.id} className="p-4">
            <div className="space-y-2">
              <Image 
                src={tutorial.thumbnailUrl} 
                alt={tutorial.title}
                width={400}
                height={300}
                className="w-full h-48 object-cover rounded-md"
              />
              <h3 className="font-semibold">{tutorial.title}</h3>
              <p className="text-sm text-muted-foreground">{tutorial.description}</p>
              <div className="flex justify-end gap-2">
                <Button variant="outline" size="sm">
                  Remove from Featured
                </Button>
                <Button size="sm">
                  View Tutorial
                </Button>
              </div>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
} 