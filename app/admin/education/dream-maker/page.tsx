import { DreamMakerCertificateForm } from "./components/dream-maker-certificate-form"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"

export const metadata = {
  title: "Dream Maker Certificate Management | Admin",
  description: "Send Dream Maker certification emails to qualified agents",
}

export default function DreamMakerCertificatePage() {
  return (
    <div className="flex h-full flex-col space-y-8 p-8">
      <div className="flex flex-col space-y-4">
        <Breadcrumb 
        
        />
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Dream Maker Certificate Management</h2>
            <p className="text-muted-foreground">
              Send certification emails to agents who completed their Dream Maker requirements
            </p>
          </div>
        </div>
      </div>
      <div className="flex-1">
        <DreamMakerCertificateForm />
      </div>
    </div>
  )
} 