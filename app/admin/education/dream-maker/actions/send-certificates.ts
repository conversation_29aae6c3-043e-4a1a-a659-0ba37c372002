"use server"

import { generatePDF } from "../utils/generate-pdf"
import { sendEmail } from "@/app/lib/email"

// Mock data interface
interface AgentData {
  vcEmail: string
  vcFName: string
  vcLName: string
  dDreamMaker: string
}

// Dummy data function
function getDummyAgents(date: string): AgentData[] {
  return [
    {
      vcEmail: "<EMAIL>",
      vcFName: "John",
      vcLName: "Doe",
      dDreamMaker: date
    },
    {
      vcEmail: "<EMAIL>",
      vcFName: "Jane",
      vcLName: "Smith",
      dDreamMaker: date
    }
  ]
}

export async function sendDreamMakerCertificates(date: string) {
  // Validate date is within last month
  const inputDate = new Date(date)
  const oneMonthAgo = new Date()
  oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1)
  
  if (inputDate < oneMonthAgo) {
    throw new Error("Please enter a date within the last month")
  }

  // Get dummy agents instead of SQL query
  const agents = getDummyAgents(date)

  if (!agents.length) {
    throw new Error("No qualified agents found for the specified date")
  }

  // Generate and send certificates
  for (const agent of agents) {
    const certificate = await generatePDF({
      firstName: agent.vcFName,
      lastName: agent.vcLName,
    })

    await sendEmail({
      to: agent.vcEmail,
      subject: "Dream Maker Certificate",
      html: `
        <p>Hello ${agent.vcFName} ${agent.vcLName},</p>
        <p>Congratulations on reaching your Dream Maker Certification status with InteleTravel.com! 
           Attached is your requested Dream Maker Certificate.</p>
        <p>Please note that InteleTravel only prints physical copies for Dream Maker Certificate at live events. 
           You can print your certificate from any home printer or at a local print shop.</p>
        <p>Congratulations on your education success! Keep working hard - Vacation Builder is next!</p>
        <p>Sincerely,<br>InteleTravel Customer Service</p>
      `,
      attachments: [{
        filename: "Certificate.pdf",
        content: certificate,
      }],
    })
  }

  // Send summary email to administrators
  await sendEmail({
    to: ["<EMAIL>", "<EMAIL>"],
    subject: `${agents.length} Dream Maker Certificate(s) sent`,
    text: `${agents.length} Certificates have been sent for ${date}!`,
  })

  return { count: agents.length }
} 