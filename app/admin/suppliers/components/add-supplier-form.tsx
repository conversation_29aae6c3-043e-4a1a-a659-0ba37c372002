"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { Switch } from "@/app/components/ui/switch"
import { Label } from "@/app/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"

export function AddSupplierForm() {
  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Add New Supplier</h1>
      </div>

      <Card className="bg-white rounded-none">
        <CardHeader className="border-b py-3">
          <h2 className="text-lg font-medium">Supplier Details</h2>
        </CardHeader>
        <CardContent className="p-6">
          <form className="space-y-6">
            {/* Basic Information */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Supplier Name *</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter supplier name"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Supplier Code *</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter supplier code"
                  required
                />
              </div>
            </div>

            {/* Contact Information */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Contact Email *</Label>
                <Input 
                  type="email"
                  className="rounded-none"
                  placeholder="Enter contact email"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label>Contact Phone</Label>
                <Input 
                  type="tel"
                  className="rounded-none"
                  placeholder="Enter contact phone"
                />
              </div>
            </div>

            {/* Address Information */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Address Line 1</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter address line 1"
                />
              </div>
              <div className="space-y-2">
                <Label>Address Line 2</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter address line 2"
                />
              </div>
            </div>

            <div className="grid grid-cols-3 gap-6">
              <div className="space-y-2">
                <Label>City</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter city"
                />
              </div>
              <div className="space-y-2">
                <Label>State/Province</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter state/province"
                />
              </div>
              <div className="space-y-2">
                <Label>Postal Code</Label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Enter postal code"
                />
              </div>
            </div>

            {/* Business Details */}
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label>Supplier Type *</Label>
                <Select required>
                  <SelectTrigger className="rounded-none">
                    <SelectValue placeholder="Select supplier type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="airline">Airline</SelectItem>
                    <SelectItem value="hotel">Hotel</SelectItem>
                    <SelectItem value="car_rental">Car Rental</SelectItem>
                    <SelectItem value="cruise">Cruise Line</SelectItem>
                    <SelectItem value="tour_operator">Tour Operator</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label>Payment Terms *</Label>
                <Select required>
                  <SelectTrigger className="rounded-none">
                    <SelectValue placeholder="Select payment terms" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="net_30">Net 30</SelectItem>
                    <SelectItem value="net_45">Net 45</SelectItem>
                    <SelectItem value="net_60">Net 60</SelectItem>
                    <SelectItem value="immediate">Immediate</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Additional Settings */}
            <div className="space-y-4">
              <div className="flex items-center space-x-2">
                <Switch id="full-payment" className="rounded-none" />
                <Label htmlFor="full-payment">Requires Full Payment Only</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Switch id="active" className="rounded-none" defaultChecked />
                <Label htmlFor="active">Active Status</Label>
              </div>
            </div>

            {/* Notes */}
            <div className="space-y-2">
              <Label>Additional Notes</Label>
              <Textarea 
                className="rounded-none min-h-[100px]"
                placeholder="Enter any additional notes or special requirements"
              />
            </div>

            {/* Form Actions */}
            <div className="flex justify-end space-x-2 pt-4">
              <Button
                type="button"
                variant="outline"
                className="rounded-none"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="rounded-none"
              >
                Create Supplier
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 