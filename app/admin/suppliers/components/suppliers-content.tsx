"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Input } from "@/app/components/ui/input"
import { ChevronDown, Filter, Plus } from "lucide-react"
import { useState } from "react"
import { cn } from "@/lib/utils"
import Link from "next/link"

const suppliers = [
  {
    id: "1001",
    name: "Delta Airlines",
    supportsFullPaymentOnly: true,
    createdDate: "2024-02-05",
    status: "Active"
  },
  {
    id: "1002",
    name: "Marriott Hotels",
    supportsFullPaymentOnly: false,
    createdDate: "2024-02-05",
    status: "Active"
  },
]

export function SuppliersContent() {
  const [isFilterVisible, setIsFilterVisible] = useState(true)

  const toggleFilter = () => {
    setIsFilterVisible(!isFilterVisible)
  }

  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Suppliers</h1>
        <Button asChild className="rounded-none">
          <Link href="/admin/suppliers/add">
            <Plus className="mr-2 h-4 w-4" />
            Add Supplier
          </Link>
        </Button>
      </div>

      {/* Filter Section */}
      <Card className="mb-8 bg-white rounded-none">
        <CardHeader className="flex flex-row items-center justify-between border-b py-3">
          <div className="flex items-center space-x-2">
            <Filter className="h-4 w-4" />
            <span className="font-medium">Filter</span>
          </div>
          <div className="flex space-x-2">
            <Button 
              variant="outline" 
              className="rounded-none h-8"
              onClick={toggleFilter}
            >
              <ChevronDown className={cn(
                "h-4 w-4 transition-transform duration-200",
                !isFilterVisible && "rotate-180"
              )} />
            </Button>
            <Button variant="outline" className="rounded-none h-8">Reset</Button>
            <Button className="rounded-none h-8">Apply Filter</Button>
          </div>
        </CardHeader>
        {isFilterVisible && (
          <CardContent className="space-y-4">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplier Name</label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Search by name"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Supplier ID</label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Search by ID"
                />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Status</label>
                <Input 
                  type="text"
                  className="rounded-none"
                  placeholder="Filter by status"
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Suppliers Table */}
      <div className="rounded-none border">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 hover:bg-muted/50">
              <TableHead className="h-10 w-[50px]"></TableHead>
              <TableHead className="h-10">ID</TableHead>
              <TableHead className="h-10">Name</TableHead>
              <TableHead className="h-10">Supports Full Payment Only</TableHead>
              <TableHead className="h-10">Created Date</TableHead>
              <TableHead className="h-10">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {suppliers.map((supplier) => (
              <TableRow key={supplier.id}>
                <TableCell className="py-2.5">
                  <Button variant="ghost" size="sm" className="rounded-none w-8 h-8 p-0">
                    <Plus className="h-4 w-4" />
                  </Button>
                </TableCell>
                <TableCell className="py-2.5">{supplier.id}</TableCell>
                <TableCell className="py-2.5">{supplier.name}</TableCell>
                <TableCell className="py-2.5">
                  {supplier.supportsFullPaymentOnly ? "Yes" : "No"}
                </TableCell>
                <TableCell className="py-2.5">{supplier.createdDate}</TableCell>
                <TableCell className="py-2.5">{supplier.status}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 