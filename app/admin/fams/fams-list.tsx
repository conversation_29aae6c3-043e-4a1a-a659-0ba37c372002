"use client"

import { Card } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/app/components/ui/tabs"

interface Fam {
  id: string
  title: string
  description: string
  startDate: string
  endDate: string
  supplier: string
  status: "active" | "archived"
}

export function FamsList() {
  return (
    <Tabs defaultValue="current" className="w-full">
      <TabsList>
        <TabsTrigger value="current">Current FAMs</TabsTrigger>
        <TabsTrigger value="archived">Archived FAMs</TabsTrigger>
        <TabsTrigger value="faq">FAQs</TabsTrigger>
      </TabsList>

      <TabsContent value="current">
        <Card className="p-6">
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
            {/* Current FAMs content */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold">What are FAM Trips?</h3>
              <p className="text-sm text-muted-foreground">
                Familiarization Trips are provided by travel suppliers like hotels, cruise lines, 
                resorts, vacation and tour companies, tourism boards to help travel agents 
                get familiar with their offerings.
              </p>
              <ul className="list-disc pl-4 space-y-2 text-sm">
                <li>Exclusively for Travel Agents</li>
                <li>Deep discounts up to 80%</li>
                <li>Familiarize with travel offerings</li>
                <li>Encourage recommendations and sales</li>
              </ul>
            </div>
            {/* Add more content sections */}
          </div>
        </Card>
      </TabsContent>

      <TabsContent value="archived">
        <Card className="p-6">
          {/* Archived FAMs content */}
        </Card>
      </TabsContent>

      <TabsContent value="faq">
        <Card className="p-6">
          {/* FAQ content */}
        </Card>
      </TabsContent>
    </Tabs>
  )
} 