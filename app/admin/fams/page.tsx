import { Metada<PERSON> } from "next"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { PageHeader } from "@/app/components/ui/page-header"
import { FamsList } from "./fams-list"

export const metadata: Metadata = {
  title: "FAMs | Admin Dashboard",
  description: "Manage familiarization trips for travel agents",
}

const breadcrumbItems = [
  { title: "Home", link: "/" },
  { title: "FAMs", link: "/admin/fams" },
]

export default function FamsPage() {
  return (
    <div className="flex flex-col gap-5 p-8">
      {/* <Breadcrumb  /> */}
      <PageHeader 
        title="Familiarization Trips (FAMs)"
        description="Manage and view travel agent exclusive familiarization trip opportunities"
      />
      <FamsList />
    </div>
  )
} 