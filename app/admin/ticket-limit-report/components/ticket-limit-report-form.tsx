"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { DatePicker } from "@/app/components/ui/date-picker"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { Search, RotateCcw, ChevronDown, ChevronUp } from "lucide-react"

interface TicketLimitResult {
  agentPin: string
  freeUserPin: string
  enrollmentDate: string
  agentId: string
}

interface TicketLimitFormData {
  agentPin: string
  freeUserPin: string
  startDate: Date | undefined
  endDate: Date | undefined
  lastNHours: number
}

// Add dummy data
const dummyResults: TicketLimitResult[] = [
  {
    agentPin: "AG123456",
    freeUserPin: "FU789012",
    enrollmentDate: "2024-03-15",
    agentId: "1"
  },
  {
    agentPin: "AG234567",
    freeUserPin: "FU890123",
    enrollmentDate: "2024-03-14",
    agentId: "2"
  },
  {
    agentPin: "AG345678",
    freeUserPin: "FU901234",
    enrollmentDate: "2024-03-13",
    agentId: "3"
  }
]

export function TicketLimitReportForm() {
  // Initialize with dummy data
  const [results, setResults] = useState<TicketLimitResult[]>(dummyResults)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const form = useForm<TicketLimitFormData>({
    defaultValues: {
      agentPin: "",
      freeUserPin: "",
      startDate: undefined,
      endDate: undefined,
      lastNHours: 72,
    },
  })

  const onSubmit = async (data: TicketLimitFormData) => {
    // TODO: Implement API call
    console.log("Form submitted:", data)
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <h2 className="text-lg font-semibold">Search Criteria</h2>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => form.reset()}
              className="rounded-none w-24"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button
              type="submit"
              size="sm"
              className="rounded-none w-24"
            >
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="grid grid-cols-2 gap-6">
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Label className="w-32">Agent PIN:</Label>
                  <Input
                    {...form.register("agentPin")}
                    className="w-48 rounded-none"
                    placeholder="Enter agent PIN"
                  />
                </div>
                <div className="flex items-center gap-4">
                  <Label className="w-32">Free User PIN:</Label>
                  <Input
                    {...form.register("freeUserPin")}
                    className="w-48 rounded-none"
                    placeholder="Enter free user PIN"
                  />
                </div>
              </div>
              <div className="space-y-4">
                <div className="flex items-center gap-4">
                  <Label className="w-32">Start Date:</Label>
                  <div className="rounded-none [&>div>button]:rounded-none [&>div]:rounded-none">
                    <DatePicker
                      date={form.watch("startDate")}
                      setDate={(date: Date | null) => form.setValue("startDate", date || undefined)}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Label className="w-32">End Date:</Label>
                  <div className="rounded-none [&>div>button]:rounded-none [&>div]:rounded-none">
                    <DatePicker
                      date={form.watch("endDate")}
                      setDate={(date: Date | null) => form.setValue("endDate", date || undefined)}
                    />
                  </div>
                </div>
                <div className="flex items-center gap-4">
                  <Label className="w-32">Last N Hours:</Label>
                  <Input
                    type="number"
                    {...form.register("lastNHours")}
                    className="w-48 rounded-none"
                  />
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Agent PIN</TableHead>
              <TableHead>Free User PIN</TableHead>
              <TableHead>Enrollment Date</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result, index) => (
              <TableRow key={index}>
                <TableCell>
                  <a 
                    href={`/admin/agents/${result.agentId}`}
                    className="text-primary hover:underline"
                  >
                    {result.agentPin}
                  </a>
                </TableCell>
                <TableCell>{result.freeUserPin}</TableCell>
                <TableCell>{result.enrollmentDate}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </form>
  )
} 