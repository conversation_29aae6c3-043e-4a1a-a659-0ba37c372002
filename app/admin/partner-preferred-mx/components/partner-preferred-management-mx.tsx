"use client"

import { useState } from "react"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { useToast } from "@/app/components/ui/use-toast"
import { ImageUpload } from "@/app/components/ui/image-upload"

interface PartnerPreferred {
  id: number
  title: string
  sortOrder: number
  link: string
  image: string
}

export function PartnerPreferredManagementMX() {
  const { toast } = useToast()
  const [partners, setPartners] = useState<PartnerPreferred[]>([])
  const [newPartner, setNewPartner] = useState({
    title: "",
    sortOrder: 1,
    link: "",
    image: "",
  })

  async function handleAddPartner(e: React.FormEvent) {
    e.preventDefault()
    // Add partner logic here
    toast({
      title: "Partner Added",
      description: "New preferred partner has been added successfully.",
    })
  }

  async function handleUpdatePartners(e: React.FormEvent) {
    e.preventDefault()
    // Update partners logic here
    toast({
      title: "Partners Updated",
      description: "Partner information has been updated successfully.",
    })
  }

  return (
    <div className="flex-1 w-full max-w-full">
      <div className="h-full p-6 space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-primary">
            InteleTravel.mx Preferred Partner
          </h1>
        </div>

        <div className="grid gap-6 w-full">
          <Card className="p-6 rounded-none">
            <h2 className="text-lg font-semibold mb-4">Add MX Preferred Partner</h2>
            <form onSubmit={handleAddPartner} className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label className="text-sm font-medium">Title:</label>
                <Input className="rounded-none" maxLength={100} value={newPartner.title} onChange={(e) =>
                  setNewPartner({ ...newPartner, title: e.target.value })
                } />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Sort Order:</label>
                <Input className="rounded-none" type="number" maxLength={4} value={newPartner.sortOrder} onChange={(e) =>
                  setNewPartner({
                    ...newPartner,
                    sortOrder: parseInt(e.target.value),
                  })
                } />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Link:</label>
                <Input className="rounded-none" maxLength={250} value={newPartner.link} onChange={(e) =>
                  setNewPartner({ ...newPartner, link: e.target.value })
                } />
              </div>
              <div className="space-y-2">
                <label className="text-sm font-medium">Image:</label>
                <ImageUpload
                  onUpload={(url) => setNewPartner({ ...newPartner, image: url })}
                />
              </div>
              <Button type="submit" className="col-span-2 rounded-none">
                Add Partner Preferred
              </Button>
            </form>
          </Card>

          <Card className="p-6 rounded-none">
            <h2 className="text-lg font-semibold mb-4">
              Current Partner Preferred Displayed on Public Site
            </h2>
            <form onSubmit={handleUpdatePartners} className="space-y-4">
              {partners.map((partner) => (
                <div
                  key={partner.id}
                  className="grid grid-cols-2 gap-4 p-4 border rounded-none"
                >
                  {/* Partner fields similar to add form */}
                  <div className="col-span-2 flex justify-end">
                    <Button variant="destructive" size="sm" className="rounded-none">
                      Delete
                    </Button>
                  </div>
                </div>
              ))}
              <Button type="submit" className="rounded-none">
                Update MX Preferred Partner
              </Button>
            </form>
          </Card>

          <Card className="p-6 rounded-none">
            <div className="text-sm text-muted-foreground">
              <p className="font-bold">NOTE:</p>
              <p>
                You can place a hyperlink within the Partner Preferred Description using:
                <br />
                {'<a href="http://www.whatever.com/">text goes here</a>'}
              </p>
              <p>
                For a new window: <br />
                {'<a href="http://www.whatever.com/" target="_blank">text goes here</a>'}
              </p>
              <p>
                For bold text: {'<strong>text goes here</strong>'} <br />
                For italics: {'<em>text goes here</em>'}
              </p>
            </div>
          </Card>
        </div>
      </div>
    </div>
  )
} 