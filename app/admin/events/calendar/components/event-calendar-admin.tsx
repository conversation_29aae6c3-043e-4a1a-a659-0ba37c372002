"use client"

import { useState } from "react"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Button } from "@/app/components/ui/button"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Textarea } from "@/app/components/ui/textarea"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { EventList } from "./event-list"

interface DateRange {
  start: Date | undefined
  end: Date | undefined
}

interface EventFormData {
  startDate: Date | undefined
  endDate: Date | undefined
  description: string
  image: File | null
  regions: {
    US: boolean
    UK: boolean
    MX: boolean
    IE: boolean
  }
}

export function EventCalendarAdmin() {
  const [dateRange, setDateRange] = useState<DateRange>({
    start: undefined,
    end: undefined,
  })
  
  const [eventForm, setEventForm] = useState<EventFormData>({
    startDate: undefined,
    endDate: undefined,
    description: "",
    image: null,
    regions: {
      US: false,
      UK: false,
      MX: false,
      IE: false,
    }
  })

  return (
    <div className=" mx-auto p-4 space-y-8">
      <Card>
        <CardHeader>
          <CardTitle>Event Display Range</CardTitle>
        </CardHeader>
        <CardContent className="flex gap-4 items-center">
          <div className="flex items-center gap-2">
            <span>From:</span>
            <DatePicker
              date={dateRange.start}
              setDate={(date: Date | null) => setDateRange(prev => ({ ...prev, start: date || undefined }))}
            />
          </div>
          <div className="flex items-center gap-2">
            <span>Until:</span>
            <DatePicker
              date={dateRange.end}
              setDate={(date: Date | null) => setDateRange(prev => ({ ...prev, end: date || undefined }))}
            />
          </div>
          <Button>Update Range</Button>
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Add New Event</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            {Object.entries(eventForm.regions).map(([region, checked]) => (
              <div key={region} className="flex items-center gap-2">
                <Checkbox
                  id={region}
                  checked={checked}
                  onCheckedChange={(checked) => 
                    setEventForm(prev => ({
                      ...prev,
                      regions: {
                        ...prev.regions,
                        [region]: checked === true
                      }
                    }))
                  }
                />
                <label htmlFor={region}>Show {region}</label>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <label>Start Date</label>
              <DatePicker
                date={eventForm.startDate}
                setDate={(date: Date | null) => setEventForm(prev => ({ ...prev, startDate: date || undefined }))}
              />
            </div>
            <div className="space-y-2">
              <label>End Date</label>
              <DatePicker
                date={eventForm.endDate}
                setDate={(date: Date | null) => setEventForm(prev => ({ ...prev, endDate: date || undefined }))}
              />
            </div>
          </div>

          <div className="space-y-2">
            <label>Event Description</label>
            <Textarea
              value={eventForm.description}
              onChange={(e) => setEventForm(prev => ({ ...prev, description: e.target.value }))}
              rows={5}
            />
          </div>

          <div className="space-y-2">
            <label>Image</label>
            <Input
              type="file"
              accept="image/*"
              onChange={(e) => setEventForm(prev => ({ 
                ...prev, 
                image: e.target.files?.[0] || null 
              }))}
            />
          </div>

          <Button className="w-full">Add Event</Button>
        </CardContent>
      </Card>

      <EventList />
    </div>
  )
} 