"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { Checkbox } from "@/app/components/ui/checkbox"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Textarea } from "@/app/components/ui/textarea"
import { Input } from "@/app/components/ui/input"

interface Event {
  id: number
  startDate: Date
  endDate?: Date
  description: string
  image?: string
  regions: {
    US: boolean
    UK: boolean
    MX: boolean
    IE: boolean
  }
}

// This would normally come from an API
const mockEvents: Event[] = [
  {
    id: 1,
    startDate: new Date(),
    description: "Sample Event",
    regions: {
      US: true,
      UK: false,
      MX: false,
      IE: false,
    }
  }
]

export function EventList() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Current Events</CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {mockEvents.map((event) => (
          <div key={event.id} className="border p-4 rounded-lg space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <label>Start Date</label>
                <DatePicker 
                  date={event.startDate} 
                  setDate={() => {}} 
                />
              </div>
              <div className="space-y-2">
                <label>End Date</label>
                <DatePicker 
                  date={event.endDate} 
                  setDate={() => {}} 
                />
              </div>
            </div>

            <div className="space-y-2">
              <label>Description</label>
              <Textarea defaultValue={event.description} rows={3} />
            </div>

            <div className="space-y-2">
              <label>Image</label>
              <Input type="file" accept="image/*" />
              {event.image && (
                <img 
                  src={event.image} 
                  alt="Event" 
                  className="w-24 h-24 object-cover rounded"
                />
              )}
            </div>

            <div className="flex gap-4">
              {Object.entries(event.regions).map(([region, checked]) => (
                <div key={region} className="flex items-center gap-2">
                  <Checkbox id={`${event.id}-${region}`} checked={checked} />
                  <label htmlFor={`${event.id}-${region}`}>Show {region}</label>
                </div>
              ))}
            </div>

            <div className="flex justify-between">
              <Checkbox id={`delete-${event.id}`} />
              <label htmlFor={`delete-${event.id}`}>Delete this event</label>
              <Button variant="outline">Update Event</Button>
            </div>
          </div>
        ))}
        
        <Button className="w-full">Update All Events</Button>
      </CardContent>
    </Card>
  )
} 