import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { UploadEventForm } from "./components/upload-event-form"

const breadcrumbItems = [
  { label: "InteleTravel Homepage", href: "/" },
  { label: "Administrator Main Menu", href: "/admin" },
  { label: "Upload Event File", href: "/admin/events/upload" }
]

export default function UploadEventPage() {
  return (
    <main className="min-h-screen flex flex-col p-8">
      <h1 className="text-2xl font-bold text-navy-600 mb-6">
        InteleTravel.com Upload Event File
      </h1>
      
      <div className="mb-8">
        <Breadcrumb  />
      </div>

      <div className="flex-1 w-full">
        <UploadEventForm />
      </div>
    </main>
  )
} 