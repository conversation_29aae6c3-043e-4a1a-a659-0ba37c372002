"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { FileUploader } from "./file-uploader"
import { uploadEventFile } from "../actions/upload-event"

interface FileValidation {
  hasPin: boolean
  hasEmail: boolean
  hasEmptyRows: boolean
}

export function UploadEventForm() {
  const [file, setFile] = useState<File | null>(null)
  const [isUploading, setIsUploading] = useState(false)
  const [error, setError] = useState<string | null>(null)

  async function handleSubmit(e: React.FormEvent) {
    e.preventDefault()
    if (!file) return

    try {
      setIsUploading(true)
      setError(null)
      await uploadEventFile(file)
      // Handle success (e.g., show success message, reset form)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to upload file")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Card className="w-full h-full rounded-none shadow-none p-6">
      <CardHeader>
        <CardTitle>Upload Event File</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            <Alert>
              <AlertDescription>
                <ul className="list-disc pl-4 space-y-1">
                  <li>File must have a column with header &apos;PIN&apos;</li>
                  <li>File must have a column with header &apos;Email&apos;</li>
                  <li>No empty rows or headers allowed</li>
                </ul>
              </AlertDescription>
            </Alert>

            <FileUploader
              accept=".csv,.xlsx"
              onChange={setFile}
              disabled={isUploading}
            />

            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button 
              type="submit" 
              disabled={!file || isUploading}
              className="w-full"
            >
              {isUploading ? "Uploading..." : "Upload Event File"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
} 