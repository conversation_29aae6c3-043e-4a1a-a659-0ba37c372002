"use client"

import { Card } from "@/app/components/ui/card"
import { useEffect, useState } from "react"
import { GoogleMap, useLoadScript, OverlayView } from "@react-google-maps/api"

interface AgentLocation {
  countryName: string
  agentCount: number
  latitude: number
  longitude: number
}

const dummyData: AgentLocation[] = [
  {
    countryName: "United States",
    agentCount: 15000,
    latitude: 39.4419,
    longitude: -97.1419
  },
  {
    countryName: "United Kingdom",
    agentCount: 5000,
    latitude: 54.7023,
    longitude: -3.2765
  },
  // Add more dummy data as needed
]

const mapOptions = {
  styles: [
    {
      featureType: "administrative",
      elementType: "geometry",
      stylers: [{ visibility: "on" }]
    },
    {
      featureType: "water",
      elementType: "geometry",
      stylers: [{ color: "#e9e9e9" }]
    }
  ]
}

export function WorldViewMap() {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!
  })

  if (loadError) {
    return (
      <Card className="p-6 text-center text-destructive">
        Failed to load Google Maps
      </Card>
    )
  }

  if (!isLoaded) {
    return (
      <Card className="rounded-none shadow-none overflow-hidden relative">
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
        <div className="w-full h-[768px]" />
      </Card>
    )
  }

  return <Map />
}

function Map() {
  const center = { lat: 39.4419, lng: -97.1419 }

  return (
    <Card className="rounded-none shadow-none overflow-hidden">
      <GoogleMap
        zoom={3}
        center={center}
        mapContainerClassName="w-full h-[768px]"
        options={mapOptions}
      >
        {dummyData.map((location, index) => (
          <OverlayView
            key={index}
            position={{ lat: location.latitude, lng: location.longitude }}
            mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
          >
            <div className="px-3 py-1.5 bg-white/90 border shadow-sm text-sm font-medium rounded-sm whitespace-nowrap cursor-default transform -translate-x-1/2 -translate-y-full">
              {location.countryName}: {location.agentCount.toLocaleString()}
            </div>
          </OverlayView>
        ))}
      </GoogleMap>
    </Card>
  )
} 