"use client"

import { useState } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Textarea } from "@/app/components/ui/textarea"
import { Switch } from "@/app/components/ui/switch"
import { DatePicker } from "@/app/components/ui/date-picker"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"

interface HotDealsFormProps {
  // Add props if needed
}

export function HotDealsForm({}: HotDealsFormProps) {
  const [endDate, setEndDate] = useState<Date | null>(null)
  
  return (
    <Card className="p-6 rounded-none">
      <form className="space-y-6">
        <div className="text-lg font-semibold">Add New Hot Deal</div>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <Label>End Date</Label>
              <DatePicker 
                date={endDate} 
                setDate={setEndDate} 
              />
            </div>

            <div>
              <Label>Title</Label>
              <Input placeholder="Enter deal title" />
            </div>

            <div>
              <Label>Category</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select categories" />
                </SelectTrigger>
                <SelectContent>
                  {/* Add categories */}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Supplier</Label>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="Select supplier" />
                </SelectTrigger>
                <SelectContent>
                  {/* Add suppliers */}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch id="uk-visibility" />
              <Label htmlFor="uk-visibility">Show in UK</Label>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <Label>IE Public Description</Label>
              <Textarea placeholder="Enter public description for IE" />
            </div>

            <div>
              <Label>IE Agent Description</Label>
              <Textarea placeholder="Enter agent description for IE" />
            </div>

            <div>
              <Label>UK Public Description</Label>
              <Textarea placeholder="Enter public description for UK" />
            </div>

            <div>
              <Label>UK Agent Description</Label>
              <Textarea placeholder="Enter agent description for UK" />
            </div>
          </div>
        </div>

        <div className="flex justify-end">
          <Button type="submit">Add Hot Deal</Button>
        </div>
      </form>
    </Card>
  )
} 