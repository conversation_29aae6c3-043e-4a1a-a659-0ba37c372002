"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/app/components/ui/tabs"
import { Card, CardContent } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { Button } from "@/app/components/ui/button"
import { ImageUpload } from "@/app/components/ui/image-upload"
import { DealCard } from "./components/deal-card"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"

interface MarketDeal {
  id: string
  market: "US" | "UK" | "MX"
  description: string
  imageUrl: string
  dealUrl: string
}

export function WeeklyDealsManagement() {
  const [activeMarket, setActiveMarket] = useState("US")
  
  const breadcrumbItems = [
    { label: "Admin", href: "/admin" },
    { label: "Marketing", href: "/admin/marketing" },
    { label: "Weekly Deals", href: "/admin/marketing/weekly-deals" },
  ]

  return (
    <main className="flex-1 space-y-4">
      <Breadcrumb  />
      
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Weekly Deals Management</h1>
      </div>

      <Tabs defaultValue="US" className="w-full" onValueChange={setActiveMarket}>
        <TabsList className="grid w-full grid-cols-3 lg:w-[400px] rounded-none">
          <TabsTrigger value="US" className="rounded-none">US Market</TabsTrigger>
          <TabsTrigger value="UK" className="rounded-none">UK Market</TabsTrigger>
          <TabsTrigger value="MX" className="rounded-none">MX Market</TabsTrigger>
        </TabsList>

        {["US", "UK", "MX"].map((market) => (
          <TabsContent key={market} value={market} className="space-y-6">
            <Card className="rounded-none border-x-0 border-t-0">
              <CardContent className="p-6 space-y-6">
                <div className="space-y-4">
                  <div>
                    <label className="text-sm font-medium">Deal URL</label>
                    <Input type="url" placeholder="https://example.com/deal" className="rounded-none" />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Deal Description</label>
                    <Textarea 
                      placeholder="Enter deal description..."
                      className="min-h-[100px] rounded-none"
                    />
                  </div>

                  <div>
                    <label className="text-sm font-medium">Deal Image</label>
                    <ImageUpload 
                      onUpload={(file) => console.log(file)}
                     
                    />
                  </div>
                </div>

                <div className="flex justify-end">
                  <Button className="rounded-none">Save Deal</Button>
                </div>
              </CardContent>
            </Card>

            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Current Deals</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                <DealCard
                  market={market as "US" | "UK" | "MX"}
                  description="Sample deal description goes here..."
                  imageUrl="/placeholder.jpg"
                  dealUrl="https://example.com"
                />
              </div>
            </div>
          </TabsContent>
        ))}
      </Tabs>
    </main>
  )
} 