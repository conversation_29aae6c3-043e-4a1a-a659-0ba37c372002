import { Card, CardContent, CardFooter } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import Image from "next/image"
import Link from "next/link"

interface DealCardProps {
  market: "US" | "UK" | "MX"
  description: string
  imageUrl: string
  dealUrl: string
}

export function DealCard({ market, description, imageUrl, dealUrl }: DealCardProps) {
  return (
    <Card className="rounded-none">
      <div className="relative h-48 w-full">
        <Image
          src={imageUrl}
          alt="Deal preview"
          fill
          className="object-cover"
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        />
      </div>
      <CardContent className="p-4 space-y-4">
        <p className="text-sm text-muted-foreground mb-2">{market} Market</p>
        <p className="text-sm text-card-foreground mb-4">{description}</p>
      </CardContent>
      <CardFooter className="p-4 pt-0">
        <Link href={dealUrl} className="text-primary hover:underline">
          View Deal →
        </Link>
      </CardFooter>
    </Card>
  )
} 