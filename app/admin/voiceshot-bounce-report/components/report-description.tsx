import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { InfoCircledIcon } from "@radix-ui/react-icons"

export function ReportDescription() {
  return (
    <Alert className="mb-6">
      <InfoCircledIcon className="h-4 w-4" />
      <AlertDescription className="ml-2">
        This report shows VoiceShot calls made to agents whose email addresses have bounced. 
        It helps identify agents who may need to update their contact information to ensure 
        they receive important communications. The report includes call details, agent information, 
        and bounce status for tracking and follow-up purposes.
      </AlertDescription>
    </Alert>
  )
} 