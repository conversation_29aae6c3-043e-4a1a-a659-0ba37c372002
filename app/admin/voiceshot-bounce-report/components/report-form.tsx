"use client"

import { useState } from "react"
import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { SearchIcon } from "lucide-react"

interface ReportFormProps {
  onSubmit: (startDate: string, endDate: string) => Promise<void>
}

export function ReportForm({ onSubmit }: ReportFormProps) {
  const [startDate, setStartDate] = useState("")
  const [endDate, setEndDate] = useState("")

  return (
    <form 
      className="flex items-center gap-4" 
      onSubmit={(e) => {
        e.preventDefault()
        onSubmit(startDate, endDate)
      }}
    >
      <Input
        type="date"
        value={startDate}
        onChange={(e) => setStartDate(e.target.value)}
        className="rounded-none"
      />
      <Input
        type="date"
        value={endDate}
        onChange={(e) => setEndDate(e.target.value)}
        className="rounded-none"
      />
      <Button 
        type="submit" 
        className="rounded-none bg-[#3C36A9] hover:bg-[#3C36A9]/90 text-white flex items-center gap-2 whitespace-nowrap"
      >
        <SearchIcon className="h-4 w-4" />
        Generate Report
      </Button>
    </form>
  )
} 