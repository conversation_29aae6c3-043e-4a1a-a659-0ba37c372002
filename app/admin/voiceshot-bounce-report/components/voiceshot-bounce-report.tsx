"use client"

import { useRouter } from "next/navigation"
import { ReportTable } from "./report-table"
import { ReportForm } from "./report-form"
import { ReportDescription } from "./report-description"
import { Button } from "@/app/components/ui/button"
import { useVoiceShotReport } from "../hooks/use-voiceshot-report"
import { StatusLegend } from "./status-legend"

export function VoiceShotBounceReport() {
  const router = useRouter()
  const { data, isLoading, error, fetchReport } = useVoiceShotReport()

  return (
    <div className="space-y-6">
      <div className="flex gap-4 mb-4">
        <Button
          variant="outline"
          className="rounded-none"
          onClick={() => {
            const today = new Date().toISOString().split('T')[0]
            fetchReport(today, today)
          }}
        >
          Today's Report
        </Button>
        <Button
          variant="outline"
          className="rounded-none"
          onClick={() => {
            const yesterday = new Date(Date.now() - 86400000).toISOString().split('T')[0]
            fetchReport(yesterday, yesterday)
          }}
        >
          Yesterday's Report
        </Button>
      </div>

      <ReportForm onSubmit={fetchReport} />
      
      <ReportDescription />

      {error && (
        <div className="bg-destructive/15 text-destructive p-4">
          {error.message}
        </div>
      )}

      <div className="border">
        <ReportTable data={data} isLoading={isLoading} />
        <div className="p-4 border-t">
          <StatusLegend />
        </div>
      </div>

      <div className="text-center text-sm text-muted-foreground">
        <a href="/" className="hover:underline">InteleTravel Homepage</a>
        {' / '}
        <a href="/admin/mainmenu" className="hover:underline">Administrator Main Menu</a>
      </div>
    </div>
  )
} 