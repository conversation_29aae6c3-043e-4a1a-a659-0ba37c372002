"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { VoiceShotBounceRecord } from "../types"

interface ReportTableProps {
  data: VoiceShotBounceRecord[]
  isLoading?: boolean
}

function StatusBar({ record, total }: { record: VoiceShotBounceRecord; total: number }) {
  return (
    <div className="h-5 w-full flex overflow-hidden">
      <div style={{ width: `${(record.scheduled/total * 100).toFixed(2)}%` }} className="bg-blue-500" />
      <div style={{ width: `${(record.noCall/total * 100).toFixed(2)}%` }} className="bg-orange-500" />
      <div style={{ width: `${(record.failed/total * 100).toFixed(2)}%` }} className="bg-red-500" />
      <div style={{ width: `${(record.humanAnswer/total * 100).toFixed(2)}%` }} className="bg-green-500" />
      <div style={{ width: `${(record.machineAnswer/total * 100).toFixed(2)}%` }} className="bg-emerald-500" />
    </div>
  )
}

export function ReportTable({ data, isLoading }: ReportTableProps) {
  if (isLoading) {
    return <div className="text-center py-4">Loading report data...</div>
  }

  if (!data?.length) {
    return <div className="text-center py-4">No records found for the selected criteria.</div>
  }

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Interval</TableHead>
          <TableHead>Total</TableHead>
          <TableHead>Status</TableHead>
          <TableHead>Scheduled</TableHead>
          <TableHead>No Call</TableHead>
          <TableHead>Failed</TableHead>
          <TableHead>Success (H/M)</TableHead>
          <TableHead>Still Empty</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {data.map((record) => {
          const total = Number(record.scheduled) + 
                       Number(record.noCall) + 
                       Number(record.failed) + 
                       Number(record.humanAnswer) + 
                       Number(record.machineAnswer)
          
          const successTotal = Number(record.humanAnswer) + Number(record.machineAnswer)
          const emptyPercentage = successTotal > 0 
            ? ((Number(record.stillEmpty) / successTotal) * 100).toFixed(1)
            : "0.0"

          return (
            <TableRow key={record.interval}>
              <TableCell>{record.interval}</TableCell>
              <TableCell>{total}</TableCell>
              <TableCell className="w-48">
                <StatusBar record={record} total={total} />
              </TableCell>
              <TableCell>{record.scheduled}</TableCell>
              <TableCell>{record.noCall}</TableCell>
              <TableCell>{record.failed}</TableCell>
              <TableCell>{`${successTotal} (${record.humanAnswer}/${record.machineAnswer})`}</TableCell>
              <TableCell>{`${record.stillEmpty} (${emptyPercentage}%)`}</TableCell>
            </TableRow>
          )
        })}
      </TableBody>
    </Table>
  )
} 