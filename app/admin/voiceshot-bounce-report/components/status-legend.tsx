interface LegendItem {
  color: string
  label: string
}

const legendItems: LegendItem[] = [
  { color: "bg-blue-500", label: "Scheduled" },
  { color: "bg-orange-500", label: "No Call" },
  { color: "bg-red-500", label: "Failed" },
  { color: "bg-green-500", label: "Human Answer" },
  { color: "bg-emerald-500", label: "Machine Answer" },
]

export function StatusLegend() {
  return (
    <div className="flex flex-wrap gap-4 mt-6">
      {legendItems.map((item) => (
        <div key={item.label} className="flex items-center gap-2">
          <div className={`w-5 h-5 rounded ${item.color}`} />
          <span className="text-sm text-muted-foreground">{item.label}</span>
        </div>
      ))}
    </div>
  )
} 