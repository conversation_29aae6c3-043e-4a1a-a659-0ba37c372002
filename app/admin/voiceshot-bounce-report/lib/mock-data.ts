import { VoiceShotBounceRecord } from "../types"

// Helper to get formatted date string
function getFormattedDate(daysAgo: number) {
  const date = new Date()
  date.setDate(date.getDate() - daysAgo)
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

// Fixed mock data with dynamic dates
export const mockData: VoiceShotBounceRecord[] = Array.from({ length: 7 }, (_, i) => ({
  interval: getFormattedDate(i),
  scheduled: 15 + Math.floor(Math.random() * 5),
  noCall: 5 + Math.floor(Math.random() * 5),
  failed: 8 + Math.floor(Math.random() * 5),
  humanAnswer: 40 + Math.floor(Math.random() * 15),
  machineAnswer: 30 + Math.floor(Math.random() * 15),
  stillEmpty: 30 + Math.floor(Math.random() * 10)
})) 