"use client"

import { useState } from "react"
import { VoiceShotBounceRecord } from "../types"
import { mockData } from "../lib/mock-data"

interface UseVoiceShotReportReturn {
  data: VoiceShotBounceRecord[]
  isLoading: boolean
  error: Error | null
  fetchReport: (startDate: string, endDate: string) => Promise<void>
}

export const useVoiceShotReport = (): UseVoiceShotReportReturn => {
  const [data, setData] = useState<VoiceShotBounceRecord[]>(mockData) // Initialize with all data
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)

  async function fetchReport(startDate: string, endDate: string) {
    try {
      setIsLoading(true)
      setError(null)

      // If no dates provided, show all data
      if (!startDate || !endDate) {
        setData(mockData)
        return
      }

      // Parse dates and set to start/end of day
      const start = new Date(startDate)
      start.setHours(0, 0, 0, 0)
      const end = new Date(endDate)
      end.setHours(23, 59, 59, 999)

      // Filter data based on date range
      const filteredData = mockData.filter(record => {
        const recordDate = new Date(record.interval)
        recordDate.setHours(12, 0, 0, 0) // Set to noon to avoid timezone issues
        return recordDate >= start && recordDate <= end
      })

      setData(filteredData.length ? filteredData : mockData)
    } catch (err) {
      console.error('Error fetching report:', err)
      setError(err instanceof Error ? err : new Error('An error occurred while fetching the report'))
    } finally {
      setIsLoading(false)
    }
  }

  return {
    data,
    isLoading,
    error,
    fetchReport,
  }
} 