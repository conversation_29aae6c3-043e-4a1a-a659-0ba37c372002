import { Metadata } from "next"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"

export const metadata: Metadata = {
  title: "Error Management | InteleTravel Admin",
  description: "Error code and log management interface for administrators",
}

const errorCodes = [
  { code: "ERR_001", description: "Invalid API Key", severity: "High" },
  { code: "ERR_002", description: "Database Connection Failed", severity: "Critical" },
  { code: "ERR_003", description: "Payment Processing Error", severity: "High" },
  { code: "ERR_004", description: "Rate Limit Exceeded", severity: "Medium" },
  { code: "ERR_005", description: "Invalid Request Format", severity: "Low" },
]

const recentErrorLogs = [
  {
    timestamp: "2024-03-10 14:23:45",
    errorCode: "ERR_001",
    message: "API Key validation failed for request xyz",
    source: "API Gateway",
    status: "Resolved",
  },
  {
    timestamp: "2024-03-10 14:20:30",
    errorCode: "ERR_002",
    message: "Connection timeout after 30s",
    source: "Database Server",
    status: "Pending",
  },
  {
    timestamp: "2024-03-10 14:15:22",
    errorCode: "ERR_003",
    message: "Payment gateway timeout",
    source: "Payment Service",
    status: "Investigating",
  },
  {
    timestamp: "2024-03-10 14:10:15",
    errorCode: "ERR_004",
    message: "Too many requests from IP ***********",
    source: "Rate Limiter",
    status: "Resolved",
  },
]

function getSeverityBadge(severity: string) {
  const styles = {
    Critical: "bg-destructive text-destructive-foreground hover:bg-destructive",
    High: "bg-orange-500 text-white hover:bg-orange-500",
    Medium: "bg-yellow-500 text-white hover:bg-yellow-500",
    Low: "bg-green-500 text-white hover:bg-green-500",
  }
  return styles[severity as keyof typeof styles] || "bg-secondary"
}

function getStatusBadge(status: string) {
  const styles = {
    Resolved: "bg-green-500 text-white hover:bg-green-500",
    Pending: "bg-yellow-500 text-white hover:bg-yellow-500",
    Investigating: "bg-blue-500 text-white hover:bg-blue-500",
  }
  return styles[status as keyof typeof styles] || "bg-secondary"
}

export default function ErrorManagementPage() {
  return (
    <div className="p-8"> 
    <Breadcrumb/> 
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Error Management</h1>
      </div>
      
      {/* Add New Error Code Section */}
      <Card className="mb-8 bg-white rounded-none">
        <CardHeader>
          <CardTitle>Add New Error Code</CardTitle>
        </CardHeader>
        <CardContent>
          <form className="space-y-4 max-w-2xl">
            <div className="space-y-2">
              <label className="text-sm font-medium">Error Code</label>
              <Input 
                type="text"
                className="rounded-none"
                placeholder="Enter error code"
              />
            </div>
            <div className="space-y-2">
              <label className="text-sm font-medium">Error Description</label>
              <Input 
                type="text"
                className="rounded-none"
                placeholder="Enter error description"
              />
            </div>
            <Button 
              type="submit"
              className="rounded-none"
            >
              Add Error Code
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Error Codes Table */}
      <div className="mb-8">
        <h2 className="text-lg font-semibold mb-4">All Error Codes</h2>
        <div className="rounded-none border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50 hover:bg-muted/50">
                <TableHead className="h-10">Error Code</TableHead>
                <TableHead className="h-10">Description</TableHead>
                <TableHead className="h-10">Severity</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {errorCodes.map((error) => (
                <TableRow key={error.code}>
                  <TableCell className="py-2.5">{error.code}</TableCell>
                  <TableCell className="py-2.5">{error.description}</TableCell>
                  <TableCell className="py-2.5">
                    <Badge className={`rounded-none ${getSeverityBadge(error.severity)}`}>
                      {error.severity}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Error Logs Table */}
      <div>
        <h2 className="text-lg font-semibold mb-4">Recent Error Logs</h2>
        <div className="rounded-none border">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/50 hover:bg-muted/50">
                <TableHead className="h-10">Timestamp</TableHead>
                <TableHead className="h-10">Error Code</TableHead>
                <TableHead className="h-10">Message</TableHead>
                <TableHead className="h-10">Source</TableHead>
                <TableHead className="h-10">Status</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {recentErrorLogs.map((log, index) => (
                <TableRow key={index}>
                  <TableCell className="py-2.5">{log.timestamp}</TableCell>
                  <TableCell className="py-2.5">{log.errorCode}</TableCell>
                  <TableCell className="py-2.5 max-w-[300px] truncate">
                    {log.message}
                  </TableCell>
                  <TableCell className="py-2.5">{log.source}</TableCell>
                  <TableCell className="py-2.5">
                    <Badge className={`rounded-none ${getStatusBadge(log.status)}`}>
                      {log.status}
                    </Badge>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
} 