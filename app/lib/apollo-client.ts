import { 
  Apollo<PERSON><PERSON>, 
  InMemoryCache, 
  createHttpLink,
  type NormalizedCacheObject,
  HttpLink
} from '@apollo/client';

const httpLink = createHttpLink({
  uri: process.env.NEXT_PUBLIC_GRAPHQL_URL,
});

let client: ApolloClient<any> | null = null;

export function getClient() {
  if (!client) {
    client = new ApolloClient({
      link: new HttpLink({
        uri: process.env.NEXT_PUBLIC_GRAPHQL_URL || '/api/graphql',
      }),
      cache: new InMemoryCache(),
    });
  }
  return client;
}
