import axios, { AxiosInstance, InternalAxiosRequestConfig, AxiosResponse, AxiosError } from "axios";

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || "https://qa-cc-api.inteletravel.com";

console.log("Environment variables:", process.env);
console.log("API_BASE_URL:", API_BASE_URL);

const createAxiosInstance = (baseURL: string): AxiosInstance => {
  console.log("Creating Axios instance with baseURL:", baseURL);

  const instance = axios.create({
    baseURL,
    headers: {
      'accept': 'application/json, text/plain, */*', 
      'content-type': 'application/json',
    },
    timeout: 600000, // 30 seconds
  });

  instance.interceptors.request.use(
    (config: InternalAxiosRequestConfig) => {
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('access_token');
        if (token) {
          config.headers['Authorization'] = `Bearer ${token}`;
        }
      }

      console.log("Full request config:", config);
      console.log("Resolved request URL:", `${config.baseURL}${config.url}`);
      console.log("Request Method:", config.method);
      console.log("Request Headers:", config.headers);
      console.log("Request Data:", config.data);

      return config;
    },
    (error: AxiosError) => {
      console.error("Error in request interceptor:", error);
      return Promise.reject(error);
    }
  );

  instance.interceptors.response.use(
    (response: AxiosResponse) => response,
    (error: AxiosError) => {
      if (error.response?.status === 401) {
        // if (typeof window !== 'undefined') {
        //   window.location.href = '/login';
        // }
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

export const backendAPI = createAxiosInstance(API_BASE_URL);

