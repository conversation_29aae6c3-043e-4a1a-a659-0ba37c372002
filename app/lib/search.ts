import { SearchCriterion } from "@/app/types/search"

export function generateGraphQLQuery(criteria: SearchCriterion[]) {
  const filters = criteria.reduce((acc, criterion) => {
    if (criterion.value !== null && 
        criterion.value !== undefined && 
        criterion.value !== '' &&
        criterion.field &&
        criterion.operator) {
      const fieldName = criterion.field.name
      let value = criterion.value

      // Convert value based on field type
      if (criterion.field.type === 'number') {
        const numValue = Number(value)
        if (!isNaN(numValue)) {
          value = numValue
        } else {
          return acc // Skip if number conversion fails
        }
      }

      acc[fieldName] = {
        operator: criterion.operator.value,
        value
      }
    }
    return acc
  }, {} as Record<string, { operator: string; value: string | number | boolean }>)

  // For initial load or when no filters are applied
  if (Object.keys(filters).length === 0) {
    return {
      query: `
        query BookingSearchContentQuery(
          $page: Int!
          $pageSize: Int!
        ) {
          getAllBookings(
            page: $page
            pageSize: $pageSize
          ) {
            items {
              iBookingID
              vcPIN
              vcConfirmationID
              vcClientName
              dTravelStartDate
              dTravelEndDate
              vcFinalDestination
              nEngineID
              vcTripID
              nSalesContactID
              nBookingStatusID
            }
            total
            page
            pageSize
            totalPages
          }
        }
      `,
      variables: {
        page: 1,
        pageSize: 10
      }
    }
  }

  // For filtered search
  return {
    query: `
      query BookingSearchContentQuery(
        $page: Int!
        $pageSize: Int!
        $filters: BookingFiltersInput!
      ) {
        searchBookings(
          page: $page
          pageSize: $pageSize
          filters: $filters
        ) {
          items {
            iBookingID
            vcPIN
            vcConfirmationID
            vcClientName
            dTravelStartDate
            dTravelEndDate
            vcFinalDestination
            nEngineID
            vcTripID
            nSalesContactID
            nBookingStatusID
          }
          total
          page
          pageSize
          totalPages
        }
      }
    `,
    variables: {
      page: 1,
      pageSize: 10,
      filters
    }
  }
} 