'use client'

import { useRouter, useSearchParams, usePathname } from 'next/navigation'

export const adapter = {
  useRouter() {
    const router = useRouter()
    const pathname = usePathname()
    const searchParams = useSearchParams()

    return {
      replace(href: string) {
        router.replace(href)
      },
      push(href: string) {
        router.push(href)
      },
      getCurrentUrl() {
        const url = new URL(pathname, window.location.origin)
        url.search = searchParams.toString()
        return url.toString()
      },
      searchParams,
      pathname
    }
  }
} 