import { AgentResponse } from "@/types/agent"

interface FilterCriteria {
  operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'greaterThan' | 'lessThan'
  value: string
}

interface DateFilterCriteria {
  operator: 'equals' | 'before' | 'after'
  value: string
}

interface AdvancedAgentFilters {
  page?: number
  pageSize?: number
  orderBy?: string
  sortDirection?: 'ASC' | 'DESC'
  
  // Basic Information Filters
  pin?: FilterCriteria
  name?: FilterCriteria
  email?: FilterCriteria
  phone?: FilterCriteria
  
  // Status and Type Filters
  agentType?: FilterCriteria
  group?: FilterCriteria
  status?: FilterCriteria
  billingStatus?: FilterCriteria
  
  // Location Filters
  country?: FilterCriteria
  state?: FilterCriteria
  city?: FilterCriteria
  zip?: FilterCriteria
  
  // Date Filters
  createdDate?: DateFilterCriteria
  modifiedDate?: DateFilterCriteria
}

interface FetchAgentsParams {
  page?: number
  limit?: number
  orderBy?: string
  sortDirection?: 'ASC' | 'DESC'
  filters?: AdvancedAgentFilters
}

export async function fetchAgents({
  page = 1,
  limit = 10,
  orderBy = "vcFName",
  sortDirection = "ASC",
  filters = {}
}: FetchAgentsParams = {}): Promise<AgentResponse> {
  try {
    const url = process.env.NEXT_PUBLIC_GRAPHQL_URL
    
    if (!url) {
      throw new Error('GraphQL URL is not configured')
    }

    // Build the advanced filters object
    const advancedFilters: AdvancedAgentFilters = {
      page,
      pageSize: limit,
      orderBy,
      sortDirection,
      ...filters
    }

    console.log('Fetching agents with advanced filters:', advancedFilters)
    
    // Try the advanced query first
    let response = await fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        query: `
          query SearchAgentsAdvanced($filters: AdvancedAgentFiltersInput!) {
            searchAgentsAdvanced(filters: $filters) {
              agents {
                iAgentID
                vcPIN
                vcFName
                vcLName
                vcEmail
                status {
                  vcStatus
                }
                group
                referredBy
                offer
                landingPage
                keyword
              }
              total
              pageInfo {
                currentPage
                totalPages
              }
            }
          }
        `,
        variables: {
          filters: advancedFilters
        }
      }),
    })

    console.log('Response status:', response.status)
    
    let result = await response.json()
    console.log('API Response:', result)

    // If advanced query fails, fall back to the old query
    if (result.errors && result.errors.some((error: { message: string }) => error.message.includes('Cannot query field "searchAgentsAdvanced"'))) {
      console.log('Advanced query not available, falling back to searchAgents')
      
      response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            query SearchAgents($page: Int!, $limit: Int!, $orderBy: String!, $sortDirection: String!) {
              searchAgents(
                input: {},
                pagination: { page: $page, limit: $limit, orderBy: $orderBy, sortDirection: $sortDirection }
              ) {
                agents {
                  iAgentID
                  vcPIN
                  vcFName
                  vcLName
                  vcEmail
                  status {
                    vcStatus
                  }
                }
                total
                pageInfo {
                  currentPage
                  totalPages
                  hasNextPage
                  hasPreviousPage
                }
              }
            }
          `,
          variables: {
            page,
            limit,
            orderBy,
            sortDirection
          }
        }),
      })
      
      result = await response.json()
    }

    if (result.errors) {
      console.error('GraphQL Errors:', result.errors)
      throw new Error(result.errors[0].message)
    }

    // Handle both response formats
    const data = result.data?.searchAgentsAdvanced || result.data?.searchAgents
    
    if (!data) {
      console.error('No data in response:', result)
      throw new Error('No data returned from API')
    }
    
    // Transform to match existing interface
    const transformedData = {
      agents: data.agents.map((agent: {
        iAgentID: string
        vcPIN: string
        vcFName: string
        vcLName: string
        vcEmail: string
        status: { vcStatus: string }
        group?: string
        referredBy?: string
        offer?: string
        landingPage?: string
        keyword?: string
      }) => ({
        ...agent,
        group: agent.group || '',
        referredBy: agent.referredBy || '',
        offer: agent.offer || '',
        landingPage: agent.landingPage || '',
        keyword: agent.keyword || ''
      })),
      total: data.total,
      pageInfo: {
        currentPage: data.pageInfo.currentPage,
        totalPages: data.pageInfo.totalPages,
        hasNextPage: data.pageInfo.hasNextPage ?? (data.pageInfo.currentPage < data.pageInfo.totalPages),
        hasPreviousPage: data.pageInfo.hasPreviousPage ?? (data.pageInfo.currentPage > 1)
      }
    }
    
    console.log('Transformed data:', {
      agentCount: transformedData.agents.length,
      total: transformedData.total,
      pageInfo: transformedData.pageInfo,
      firstAgent: transformedData.agents[0]
    })
    
    return transformedData
  } catch (error) {
    console.error('Error fetching agents:', error)
    return {
      agents: [],
      total: 0,
      pageInfo: {
        currentPage: 1,
        totalPages: 1,
        hasNextPage: false,
        hasPreviousPage: false
      }
    }
  }
}

// Export types for use in components
export type { FilterCriteria, DateFilterCriteria, AdvancedAgentFilters } 