'use server'

interface UKRegistrationReportOptions {
  emailRecipient?: string
  excludedPins?: string[]
  includeInactive?: boolean
}

export interface UKRegistrationData {
  title: string
  firstName: string
  lastName: string
  dateOfBirth: string
  emailAddress: string
  businessPhone: string
  mobilePhone: string
  address1: string
  address2: string
  town: string
  county: string
  postcode: string
  website: string
  brand: string
  dateOfCommencement: string
  pin: string
}

export async function fetchUKRegistrationData(options: UKRegistrationReportOptions = {}) {
  const query = `
    query {
      generateUKHomeworkerRegistrationReport(
        options: {
          emailRecipient: ${options.emailRecipient ? `"${options.emailRecipient}"` : "null"},
          excludedPins: ${JSON.stringify(options.excludedPins || [])},
          includeInactive: ${options.includeInactive || false}
        }
      ) {
        success
        message
        totalAgents
        reportData {
          title
          firstName
          lastName
          dateOfBirth
          emailAddress
          businessPhone
          mobilePhone
          address1
          address2
          town
          county
          postcode
          website
          brand
          dateOfCommencement
          pin
        }
      }
    }
  `

  try {
    console.log('Fetching UK registration data from:', process.env.NEXT_PUBLIC_GRAPHQL_URL)
    console.log('Query:', query)

    const requestBody = JSON.stringify({ query })
    console.log('Request body:', requestBody)

    const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL!, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: requestBody,
      cache: 'no-store'
    })

    console.log('Response status:', response.status)
    console.log('Response headers:', Object.fromEntries(response.headers.entries()))

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const data = await response.json()
    console.log('Response data:', data)
    
    if (data.errors) {
      console.error('GraphQL errors:', data.errors)
      throw new Error(data.errors[0].message)
    }

    return data.data.generateUKHomeworkerRegistrationReport
  } catch (error) {
    console.error('Error details:', {
      message: error instanceof Error ? error.message : 'Unknown error',
      stack: error instanceof Error ? error.stack : undefined,
      url: process.env.NEXT_PUBLIC_GRAPHQL_URL
    })

    // Return dummy data for development
    const dummyData = {
      success: true,
      message: "Development data",
      totalAgents: 1,
      reportData: [{
        title: "Mr",
        firstName: "Test",
        lastName: "User",
        dateOfBirth: "01/01/1990",
        emailAddress: "<EMAIL>",
        businessPhone: "0123456789",
        mobilePhone: "0987654321",
        address1: "123 Test Street",
        address2: "",
        town: "Test Town",
        county: "Test County",
        postcode: "TE1 1ST",
        website: "www.test.com",
        brand: "Test Brand",
        dateOfCommencement: "01/01/2024",
        pin: "TEST123"
      }]
    }

    console.log('Returning dummy data:', dummyData)
    return dummyData
  }
} 