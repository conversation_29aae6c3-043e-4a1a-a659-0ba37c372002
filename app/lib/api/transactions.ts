import { TransactionsResponse, TransactionsVariables } from "../types/transaction"

const TRANSACTIONS_QUERY = `
  query GetTransactions($page: Int, $limit: Int) {
    getTransactions(page: $page, limit: $limit) {
      transactions {
        iTransactionID
        nAmount
        dCreated
        transactionType {
          iTransactionTypeID
          vcTransactionType
        }
        paymentMethod {
          iPaymentMethodID
          vcPaymentMethod
          nCashFlowDirection
        }
        agent {
          iAgentID
          vcPIN
          vcFName
          vcLName
        }
        user {
          iUserID
          vcUsername
          vcEmail
        }
      }
      total
      page
      pageSize
      totalPages
    }
  }
`

export async function fetchTransactions(variables: TransactionsVariables = { page: 1, limit: 10 }) {
  const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL!, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: TRANSACTIONS_QUERY,
      variables,
    }),
  })

  if (!response.ok) {
    throw new Error('Failed to fetch transactions')
  }

  const json = await response.json()
  
  if (json.errors) {
    throw new Error(json.errors[0].message)
  }

  return json.data.getTransactions as TransactionsResponse
} 