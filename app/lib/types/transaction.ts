export interface Transaction {
  iTransactionID: string
  nAmount: number
  dCreated: string
  transactionType: {
    iTransactionTypeID: string
    vcTransactionType: string
  }
  paymentMethod: {
    iPaymentMethodID: string
    vcPaymentMethod: string
    nCashFlowDirection: number
  }
  agent: {
    iAgentID: string
    vcPIN: string
    vcFName: string
    vcLName: string
  } | null
  user: {
    iUserID: string
    vcUsername: string
    vcEmail: string | null
  }
}

export interface TransactionsResponse {
  transactions: Transaction[]
  total: number
  page: number
  pageSize: number
  totalPages: number
}

export interface TransactionsVariables {
  page?: number
  limit?: number
} 