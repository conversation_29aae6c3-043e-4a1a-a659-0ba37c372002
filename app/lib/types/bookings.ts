export interface LargeBooking {
  vcPIN: string;
  vcDisplayName: string;
  vcConfirmationID: string;
  vcClientName: string;
  vcVendor: string;
  booked: string;
  travelStartDate: string;
  travelEndDate: string;
  nTotalCharges: number;
  nTotalCommission: number;
}

export interface LargeBookingsResponse {
  largeBookings: LargeBooking[];
}

export interface LargeBookingFilterInput {
  vendorName?: string;
  minTotalCharges?: number;
  startDate?: string;
  endDate?: string;
}

export interface LargeBookingPaginationInput {
  page: number;
  limit: number;
} 