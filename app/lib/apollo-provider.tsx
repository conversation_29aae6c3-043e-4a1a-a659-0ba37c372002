'use client'

import { Apollo<PERSON><PERSON>, InM<PERSON>ory<PERSON>ache, ApolloProvider } from '@apollo/client'
import { ReactNode } from 'react'

const client = new ApolloClient({
  uri: 'http://localhost:4000/graphql', // Your GraphQL endpoint
  cache: new InMemoryCache(),
})

export function ApolloWrapper({ children }: { children: ReactNode }) {
  return (
    <ApolloProvider client={client}>
      {children}
    </ApolloProvider>
  )
} 