import { Environment, Network, RecordSource, Store } from 'relay-runtime'

const HTTP_ENDPOINT = process.env.NEXT_PUBLIC_API_URL || '/api/graphql'

async function fetchGraphQL(params: any, variables: any) {
  console.log('Relay Request:', { params, variables })

  const response = await fetch(HTTP_ENDPOINT, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: params.text,
      variables,
    }),
  })

  const json = await response.json()
  console.log('Relay Response:', json)

  if (json.errors) {
    console.error('Relay Errors:', json.errors)
  }

  return json
}

function createRelayEnvironment() {
  return new Environment({
    network: Network.create(fetchGraphQL),
    store: new Store(new RecordSource()),
  })
}

export const RelayEnvironment = createRelayEnvironment() 