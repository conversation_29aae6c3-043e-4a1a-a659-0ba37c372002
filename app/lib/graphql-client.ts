import { GraphQLClient } from 'graphql-request'

// Temporary debug log
console.log('Available env vars:', {
  NEXT_PUBLIC_GRAPHQL_URL: process.env.NEXT_PUBLIC_GRAPHQL_URL
})

if (!process.env.NEXT_PUBLIC_GRAPHQL_URL) {
  throw new Error('NEXT_PUBLIC_GRAPHQL_URL is not defined')
}

export const graphqlClient = new GraphQLClient(process.env.NEXT_PUBLIC_GRAPHQL_URL, {
  headers: {
    'Content-Type': 'application/json',
  },
})

// Log the endpoint being used (remove in production)
console.log('GraphQL Endpoint:', process.env.NEXT_PUBLIC_GRAPHQL_URL) 