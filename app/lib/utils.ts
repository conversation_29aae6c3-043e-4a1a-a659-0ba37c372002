import { type ClassValue, clsx } from "clsx"
import { twMerge } from "tailwind-merge"
import { ReadonlyURLSearchParams } from "next/navigation"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

export function formatDate(dateString: string): string {
  if (!dateString) return "-"
  
  const date = new Date(dateString)
  return date.toLocaleDateString("en-US", {
    year: "numeric",
    month: "short",
    day: "numeric",
  })
}

export function formatCurrency(amount: number): string {
  if (amount === undefined || amount === null) return "-"
  
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(amount)
}

export function createQueryString(
  searchParams: ReadonlyURLSearchParams,
  params: Record<string, string | null>
) {
  const newSearchParams = new URLSearchParams(searchParams.toString())
  
  Object.entries(params).forEach(([key, value]) => {
    if (value === null) {
      newSearchParams.delete(key)
    } else {
      newSearchParams.set(key, value)
    }
  })
  
  return newSearchParams.toString()
}

export function getTodayBookingUrl() {
  const today = new Date()
  const formattedDate = today.toISOString().split('T')[0]
  return `/bookings?dtBookingDate=${formattedDate}&page=1&pageSize=10`
}
