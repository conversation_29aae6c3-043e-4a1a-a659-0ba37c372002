"use client"

import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { Button } from "@/app/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Card, CardContent } from "@/app/components/ui/card"
import { useState, useRef } from "react"
import { useToast } from "@/app/components/ui/use-toast"

const formSchema = z.object({
  depositDate: z.string().min(1, "Deposit date is required"),
  checkNumber: z.string().min(1, "Check number is required"),
  vendor: z.string().min(1, "Vendor is required"),
  traveler: z.string().min(1, "Traveler name is required"),
  amount: z.string().min(1, "Amount is required"),
  uploadDate: z.string().min(1, "Upload date is required"),
  checkScan: z.any()
})

export function EcsUploadForm() {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()
  const fileInputRef = useRef<HTMLInputElement>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      depositDate: "",
      checkNumber: "",
      vendor: "",
      traveler: "",
      amount: "",
      uploadDate: "",
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsSubmitting(true)
    try {
      // TODO: Implement your form submission logic here
      console.log(values)
      toast({
        title: "Success",
        description: "Check scan uploaded successfully",
      })
      
      // Reset form
      form.reset()
      // Reset file input separately
      if (fileInputRef.current) {
        fileInputRef.current.value = ''
      }
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to upload check scan",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Card className="rounded-none">
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
              <FormField
                control={form.control}
                name="depositDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Check Deposit Date</FormLabel>
                    <FormControl>
                      <Input type="date" className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="checkNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Check Number</FormLabel>
                    <FormControl>
                      <Input className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="vendor"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Vendor</FormLabel>
                    <FormControl>
                      <Input className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="traveler"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Traveler</FormLabel>
                    <FormControl>
                      <Input className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="amount"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Check Amount</FormLabel>
                    <FormControl>
                      <Input type="number" step="0.01" className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="uploadDate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>ECS Date Upload</FormLabel>
                    <FormControl>
                      <Input type="date" className="rounded-none" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="checkScan"
                render={({ field: { value, onChange, ...field } }) => (
                  <FormItem>
                    <FormLabel>ECS Check Scan</FormLabel>
                    <FormControl>
                      <Input 
                        type="file" 
                        accept="image/*,.pdf"
                        className="rounded-none"
                        ref={fileInputRef}
                        onChange={(e) => {
                          const file = e.target.files?.[0]
                          onChange(file)
                        }}
                        name={field.name}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end">
              <Button type="submit" className="rounded-none" disabled={isSubmitting}>
                {isSubmitting ? "Uploading..." : "Upload Check Scan"}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
} 