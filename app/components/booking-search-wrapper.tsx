'use client'

import { Suspense } from 'react'
import { useQuery } from '@apollo/client'
import { BOOKING_SEARCH_QUERY } from '@/app/graphql/queries/booking-search'
import { BookingSearchContent } from '@/app/components/booking-search/booking-search-content'
import { SearchParamsProvider } from '@/app/components/providers/search-params-provider'
import { ApolloProvider } from '@apollo/client'
import { getClient } from '@/app/lib/apollo-client'

export default function BookingSearchWrapper() {
  const client = getClient();
  
  return (
    <ApolloProvider client={client}>
      <SearchParamsProvider>
        <Suspense fallback={<div>Loading...</div>}>
          <BookingSearchContent />
        </Suspense>
      </SearchParamsProvider>
    </ApolloProvider>
  )
} 