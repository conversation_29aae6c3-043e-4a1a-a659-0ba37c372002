"use client"

import { useState, useCallback } from "react"
import { X, ChevronDown, ChevronUp, Filter } from "lucide-react"
import { Button } from "@/app/components/ui/button"
import { SearchCriterionEditor } from "./search-criterion-editor"
import { SearchCriterion, SearchField } from "@/app/types/search-criterion"
import { SEARCH_FIELDS } from "@/app/config/search-fields"
import { v4 as uuidv4 } from 'uuid'
import { Checkbox } from "@/app/components/ui/checkbox"
import { Badge } from "@/app/components/ui/badge"

interface FilterValue {
  operator: string
  value: string | number | boolean
}

interface Filters {
  [key: string]: FilterValue
}

interface SearchPayload {
  page: number
  pageSize: number
  filters: Filters
}

interface MultiFieldSearchProps {
  onSearch: (payload: SearchPayload) => void
  onClear: () => void
  isSearching?: boolean
  page?: number
  pageSize?: number
  initialFilters?: Filters
  onSaveSearch?: (name: string, criteria: SearchCriterion[]) => void
  selectedIds: number[]
  onSelectionChange: (ids: number[]) => void
  availableFields: SearchField[]
}

type EditorCriterion = Omit<SearchCriterion, 'value'> & {
  value?: string | number
}

export function MultiFieldSearch({
  onSearch,
  onClear,
  isSearching = false,
  page = 1,
  pageSize = 10,
  initialFilters,
  onSaveSearch,
  selectedIds,
  onSelectionChange,
  availableFields
}: MultiFieldSearchProps) {
  const [searchCriteria, setSearchCriteria] = useState<SearchCriterion[]>(() => {
    if (!initialFilters) return []
    
    const mapped = Object.entries(initialFilters)
      .map(([key, filter]): SearchCriterion | null => {
        const field = SEARCH_FIELDS.find(f => f.key === key)
        if (!field) return null
        
        const operator = field.operators.find((op: { value: string }) => op.value === filter.operator)
        if (!operator) return null

        return {
          id: uuidv4(),
          field,
          operator,
          value: filter.value
        }
      })
    
    return mapped.filter((c): c is SearchCriterion => c !== null)
  })

  const [editingId, setEditingId] = useState<string | null>(null)

  const handleAddCriterion = useCallback((criterion: SearchCriterion) => {
    setSearchCriteria((prev: SearchCriterion[]) => [...prev, criterion])
  }, [])

  const handleRemoveCriterion = useCallback((id: string) => {
    setSearchCriteria((prev: SearchCriterion[]) => prev.filter((c: SearchCriterion) => c.id !== id))
  }, [])

  const handleUpdateCriterion = useCallback((updatedCriterion: SearchCriterion) => {
    setSearchCriteria((prev: SearchCriterion[]) => 
      prev.map(c => c.id === updatedCriterion.id ? updatedCriterion : c)
    )
    setEditingId(null)
  }, [])

  const transformCriteriaToFilters = useCallback((criteria: SearchCriterion[]): Filters => {
    return criteria.reduce((acc, criterion) => {
      if (criterion.field?.key && criterion.operator?.value && criterion.value !== undefined) {
        const value = criterion.field.type === 'boolean' 
          ? Boolean(criterion.value)
          : criterion.field.type === 'money'
            ? Number(criterion.value)
            : criterion.value;
        
        acc[criterion.field.key] = {
          operator: criterion.operator.value,
          value
        }
      }
      return acc
    }, {} as Filters)
  }, [])

  const handleSearch = useCallback(() => {
    if (!isSearching) {
      const searchPayload: SearchPayload = {
        page,
        pageSize,
        filters: transformCriteriaToFilters(searchCriteria)
      }
      onSearch(searchPayload)
    }
  }, [searchCriteria, onSearch, isSearching, page, pageSize, transformCriteriaToFilters])

  const handleClear = useCallback(() => {
    setSearchCriteria([])
    setEditingId(null)
    onClear()
  }, [onClear])

  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className="w-full border  bg-white">
      <div className="flex items-center justify-between p-4 border-b">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          <h2 className="text-sm font-semibold">Filters</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 rounded-none"
          >
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
            <span className="ml-2">Collapse</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleClear}
            className="h-8 rounded-none"
          >
            Reset
          </Button>
          <Button
            variant="default"
            size="sm"
            onClick={handleSearch}
            disabled={searchCriteria.length === 0 || isSearching}
            className="h-8 rounded-none"
          >
            Apply Filters
          </Button>
        </div>
      </div>

      {!isCollapsed && (
        <div className="space-y-4 p-4">
          {selectedIds.length > 0 && (
            <div className="border-b pb-4">
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">
                  {selectedIds.length} item{selectedIds.length === 1 ? '' : 's'} selected
                </span>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() => onSelectionChange([])}
                  className="rounded-none"
                >
                  Clear selection
                </Button>
              </div>
              <div className="flex items-center gap-2 mt-2">
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => {
                    console.log('Bulk action with IDs:', selectedIds)
                  }}
                  className="rounded-none"
                >
                  Bulk Action
                </Button>
              </div>
            </div>
          )}

          {/* Applied Filters Section */}
          {searchCriteria.length > 0 && (
            <div className="border-b pb-4">
              <h3 className="text-sm font-medium mb-2">Applied Filters</h3>
              <div className="flex flex-wrap gap-2">
                {searchCriteria.map((criterion: SearchCriterion) => (
                  <Badge 
                    key={criterion.id}
                    variant="secondary" 
                    className="h-8 pl-2 pr-1.5 flex items-center gap-1.5 bg-blue-50 hover:bg-blue-100 text-blue-700 border-blue-200"
                  >
                    <button
                      type="button"
                      onClick={() => setEditingId(criterion.id ?? null)}
                      className="flex items-center gap-1.5 text-sm"
                    >
                      <span className="font-medium">{criterion.field?.label}</span>
                      <span className="text-blue-600">
                        {criterion.operator?.label}
                      </span>
                      <span>{criterion.value}</span>
                    </button>
                    <Button
                      type="button"
                      variant="ghost"
                      size="sm"
                      className="h-6 w-6 p-0.5 ml-1 rounded-none hover:bg-transparent"
                      onClick={() => handleRemoveCriterion(criterion.id as string)}
                    >
                      <X className="h-3.5 w-3.5" />
                    </Button>
                  </Badge>
                ))}
              </div>
            </div>
          )}
          
          {/* Filter Editor Section */}
          <div className="bg-background rounded-md">
            {editingId ? (
              <SearchCriterionEditor
                criterion={
                  (searchCriteria.find((c: SearchCriterion) => c.id === editingId) ?? {
                    id: '',
                    field: undefined,
                    operator: undefined,
                    value: undefined
                  }) as unknown as EditorCriterion
                }
                availableFields={availableFields}
                onUpdate={handleUpdateCriterion}
                onCancel={() => setEditingId(null)}
              />
            ) : availableFields.length > 0 && (
              <SearchCriterionEditor
                criterion={{
                  id: '',
                  field: undefined,
                  operator: undefined,
                  value: undefined
                } as EditorCriterion}
                availableFields={availableFields}
                onUpdate={handleAddCriterion}
                onCancel={() => {}}
                autoFocus
              />
            )}
          </div>
        </div>
      )}
    </div>
  )
} 