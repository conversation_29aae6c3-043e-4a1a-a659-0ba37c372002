"use client"

import { useState, useEffect } from "react"
import { X } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { SearchField, SearchCriterion } from "@/app/types/search-criterion"
import type { Operator } from "@/app/types/search"

interface SearchCriterionEditorProps {
  criterion: Partial<SearchCriterion>
  availableFields: SearchField[]
  onUpdate: (criterion: SearchCriterion) => void
  onCancel: () => void
  autoFocus?: boolean
}

export function SearchCriterionEditor({
  criterion,
  availableFields,
  onUpdate,
  onCancel,
  autoFocus = false
}: SearchCriterionEditorProps) {
  const [field, setField] = useState<SearchField | undefined>(criterion.field)
  const [operator, setOperator] = useState<Operator | undefined>(criterion.operator)
  const [value, setValue] = useState<string>(criterion.value?.toString() ?? '')
  const [error, setError] = useState<string | null>(null)

  const validateValue = (fieldType: string, val: string): boolean => {
    if (!val || val.trim() === '') {
      setError('Value is required')
      return false
    }

    switch (fieldType) {
      case 'number':
        const num = Number(val)
        if (isNaN(num)) {
          setError('Please enter a valid number')
          return false
        }
        break
      case 'date':
        // Allow any valid date string for now as it will be formatted by the input
        if (!val) {
          setError('Please select a date')
          return false
        }
        break
    }

    setError(null)
    return true
  }

  const handleComplete = () => {
    if (!field || !operator || !value) {
      setError('All fields are required')
      return
    }

    if (!validateValue(field.type, value)) {
      return
    }

    let preparedValue = field.valueType === 'number' || field.type === 'number' 
      ? Number(value) 
      : value.trim()

    // Handle date values
    if (field.type === 'date') {
      // Format date as YYYY-MM-DD 00:00:00
      const date = new Date(value)
      preparedValue = `${date.toISOString().split('T')[0]} 00:00:00`
    }

    try {
      onUpdate({ 
        id: criterion.id ?? crypto.randomUUID(), 
        field, 
        operator, 
        value: preparedValue
      })
    } catch (error) {
      setError('Invalid search criteria')
      console.error('Search criterion error:', error)
    }
  }

  const handleValueChange = (newValue: string) => {
    setValue(newValue)
    if (field && newValue) {
      validateValue(field.type, newValue)
    } else {
      setError(null)
    }
  }

  const renderEmptyState = () => {
    if (!error) return null

    return (
      <div className="text-sm text-destructive">
        {error}
      </div>
    )
  }

  const renderValueInput = () => {
    if (!field) return null

    const commonProps = {
      className: "w-[200px] rounded-none",
      autoFocus,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => handleValueChange(e.target.value),
      value
    }

    switch (field.type) {
      case 'select':
        return (
          <Select 
            value={value.toString()} 
            onValueChange={handleValueChange}
          >
            <SelectTrigger className="w-[200px] rounded-none">
              <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem 
                  key={option.value} 
                  value={option.value.toString()}
                >
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      
      case 'boolean':
        return (
          <Select value={value} onValueChange={handleValueChange}>
            <SelectTrigger className="w-[200px] rounded-none">
              <SelectValue placeholder="Select value" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        )
      
      case 'date':
        return (
          <Input
            {...commonProps}
            type="date"
            onBlur={(e) => {
              // Validate date format
              if (e.target.value && !isNaN(Date.parse(e.target.value))) {
                setError(null)
              } else {
                setError('Please enter a valid date')
              }
            }}
          />
        )
      
      case 'number':
        return (
          <Input
            {...commonProps}
            type="number"
            placeholder={`Enter ${field.label.toLowerCase()}`}
          />
        )
      
      default:
        return (
          <Input
            {...commonProps}
            type="text"
            placeholder={`Enter ${field.label.toLowerCase()}`}
          />
        )
    }
  }

  return (
    <div className="space-y-2">
      <div className="flex items-center gap-2 h-10">
        <Select
          value={field?.name}
          onValueChange={(value) => {
            const newField = availableFields.find(f => f.name === value)
            setField(newField)
            setOperator(undefined)
            setValue('')
            setError(null)
          }}
        >
          <SelectTrigger className="w-[150px] rounded-none">
            <SelectValue placeholder="Select field" />
          </SelectTrigger>
          <SelectContent>
            {availableFields.map((field) => (
              <SelectItem key={field.name} value={field.name}>
                {field.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        {field && (
          <>
            <Select
              value={operator?.value}
              onValueChange={(value) => {
                setOperator(field.operators.find(o => o.value === value))
                setError(null)
              }}
            >
              <SelectTrigger className="w-[150px] rounded-none">
                <SelectValue placeholder="Select operator" />
              </SelectTrigger>
              <SelectContent>
                {field.operators.map((op) => (
                  <SelectItem key={op.value} value={op.value}>
                    {op.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            {renderValueInput()}

            <Button
              variant="ghost"
              size="sm"
              onClick={handleComplete}
              className="px-2"
              disabled={!field || !operator || !value}
            >
              Add
            </Button>
            <Button
              variant="ghost"
              size="sm"
              onClick={onCancel}
              className="px-2"
            >
              <X className="h-4 w-4" />
            </Button>
          </>
        )}
      </div>
      {renderEmptyState()}
    </div>
  )
} 