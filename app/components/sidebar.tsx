'use client'

import Link from "next/link"
import { usePathname } from "next/navigation"
import { LayoutDashboard, Receipt } from "lucide-react"
import { cn } from "@/lib/utils"

export const sidebarLinks = [
  {
    title: "Dashboardd",
    href: "/dashboard",
    icon: LayoutDashboard,
    variant: "default"
  },
  {
    title: "Transactions",
    href: "/transactions",
    icon: Receipt,
    variant: "default"
  },
]

export function Sidebar() {
  const pathname = usePathname()

  return (
    <div className="flex h-full w-[200px] flex-col border-r bg-background">
      <div className="flex h-14 items-center border-b px-4">
        <span className="font-semibold">Admin Panel</span>
      </div>
      <div className="flex-1 space-y-1 p-2">
        {sidebarLinks.map((link) => (
          <Link
            key={link.href}
            href={link.href}
            className={cn(
              "flex items-center gap-3 rounded-lg px-3 py-2 text-gray-500 transition-all hover:text-gray-900 dark:text-gray-400 dark:hover:text-gray-50",
              pathname === link.href && "bg-gray-100 text-gray-900 dark:bg-gray-800 dark:text-gray-50"
            )}
          >
            <link.icon className="h-4 w-4" />
            {link.title}34343
          </Link>
        ))}
      </div>
    </div>
  )
} 