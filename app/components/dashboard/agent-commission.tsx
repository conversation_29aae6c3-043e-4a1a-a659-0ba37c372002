"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Car<PERSON>ianG<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, ResponsiveContainer } from "recharts"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/app/components/ui/table"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { formatCurrency } from "@/app/lib/utils"

interface AgentCommission {
  name: string
  bookings: number
  commission: number
  performance: number
  totalValue: number
}

const agentData: AgentCommission[] = [
  {
    name: "<PERSON>",
    bookings: 45,
    commission: 4500,
    performance: 95,
    totalValue: 45000,
  },
  {
    name: "<PERSON>",
    bookings: 38,
    commission: 3800,
    performance: 88,
    totalValue: 38000,
  },
  {
    name: "<PERSON>",
    bookings: 52,
    commission: 5200,
    performance: 98,
    totalValue: 52000,
  },
  {
    name: "<PERSON>",
    bookings: 41,
    commission: 4100,
    performance: 92,
    totalValue: 41000,
  },
  {
    name: "<PERSON>",
    bookings: 35,
    commission: 3500,
    performance: 85,
    totalValue: 35000,
  },
]

export function AgentCommission() {
  return (
    <Card className="col-span-7 border-0 bg-background/50 backdrop-blur supports-[backdrop-filter]:bg-background/50">
      <CardHeader className="flex flex-row items-center justify-between border-b">
        <div>
          <CardTitle className="text-xl font-semibold">Agent Commission Report</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Overview of agent performance and commissions
          </p>
        </div>
        <Select defaultValue="monthly">
          <SelectTrigger className="w-[180px] bg-background/50">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="weekly">This Week</SelectItem>
            <SelectItem value="monthly">This Month</SelectItem>
            <SelectItem value="quarterly">This Quarter</SelectItem>
            <SelectItem value="yearly">This Year</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          <div className="h-[300px] bg-background/50 p-4">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={agentData}>
                <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
                <XAxis dataKey="name" stroke="#6B7280" />
                <YAxis yAxisId="left" orientation="left" stroke="#10B981" />
                <YAxis yAxisId="right" orientation="right" stroke="#6366F1" />
                <Tooltip 
                  contentStyle={{ 
                    backgroundColor: '#1F2937',
                    border: 'none',
                    boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
                  }}
                />
                <Legend 
                  wrapperStyle={{
                    paddingTop: '20px'
                  }}
                />
                <Bar
                  yAxisId="left"
                  dataKey="bookings"
                  name="Bookings"
                  fill="#10B981"
                />
                <Bar
                  yAxisId="right"
                  dataKey="commission"
                  name="Commission"
                  fill="#6366F1"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>

          <div className="border-0 bg-background/50">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-gray-800">
                  <TableHead className="font-semibold">Agent</TableHead>
                  <TableHead className="text-right font-semibold">Bookings</TableHead>
                  <TableHead className="text-right font-semibold">Commission</TableHead>
                  <TableHead className="text-right font-semibold">Performance</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {agentData.map((agent) => (
                  <TableRow key={agent.name} className="border-b border-gray-800/50 hover:bg-background/80">
                    <TableCell className="font-medium">{agent.name}</TableCell>
                    <TableCell className="text-right">{agent.bookings}</TableCell>
                    <TableCell className="text-right">
                      {formatCurrency(agent.commission)}
                    </TableCell>
                    <TableCell className="text-right">
                      <span 
                        className={`inline-flex items-center px-3 py-1 text-xs font-medium ${
                          agent.performance >= 90 
                            ? 'bg-green-900/20 text-green-400'
                            : agent.performance >= 80
                            ? 'bg-yellow-900/20 text-yellow-400'
                            : 'bg-red-900/20 text-red-400'
                        }`}
                      >
                        {agent.performance}%
                      </span>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 