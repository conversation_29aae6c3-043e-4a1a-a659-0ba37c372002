"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Users, DollarSign, CreditCard, Activity, ShoppingBag, Clock, UserCheck, TrendingUp } from "lucide-react"

const stats = [
  {
    title: "Total Revenue",
    value: "$82,431.89",
    icon: DollarSign,
    description: "+20.1% from last month",
    trend: "positive",
  },
  {
    title: "Active Users",
    value: "4,350",
    icon: Users,
    description: "+180 new users",
    trend: "positive",
  },
  {
    title: "Active Subscriptions",
    value: "2,400",
    icon: CreditCard,
    description: "+19% from last month",
    trend: "positive",
  },
  {
    title: "Conversion Rate",
    value: "3.2%",
    icon: TrendingUp,
    description: "+0.3% improvement",
    trend: "positive",
  },
  {
    title: "Avg. Session Duration",
    value: "4m 32s",
    icon: Clock,
    description: "12% increase",
    trend: "positive",
  },
  {
    title: "Total Orders",
    value: "1,234",
    icon: ShoppingBag,
    description: "+21% this week",
    trend: "positive",
  },
  {
    title: "Customer Retention",
    value: "84.5%",
    icon: User<PERSON>he<PERSON>,
    description: "+2.5% improvement",
    trend: "positive",
  },
  {
    title: "Active Sessions",
    value: "842",
    icon: Activity,
    description: "Last 24 hours",
    trend: "neutral",
  },
]

export function DashboardStats() {
  return (
    <>
      {stats.map((stat) => (
        <Card key={stat.title} className="hover:shadow-lg transition-shadow">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <div className="rounded-full bg-secondary p-2">
              <stat.icon className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className={`text-xs ${stat.trend === 'positive' ? 'text-green-500' : 'text-muted-foreground'}`}>
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </>
  )
} 