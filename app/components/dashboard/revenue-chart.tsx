"use client"

import { <PERSON><PERSON><PERSON>, <PERSON>, <PERSON>Axis, <PERSON><PERSON><PERSON><PERSON>, CartesianGrid, <PERSON>lt<PERSON>, Legend, ResponsiveContainer } from "recharts"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

interface BookingData {
  name: string
  completed: number
  pending: number
  cancelled: number
  total: number
}

const bookingData: BookingData[] = [
  { name: "<PERSON>", completed: 45, pending: 20, cancelled: 5, total: 70 },
  { name: "<PERSON><PERSON>", completed: 52, pending: 25, cancelled: 8, total: 85 },
  { name: "Wed", completed: 48, pending: 30, cancelled: 10, total: 88 },
  { name: "<PERSON>hu", completed: 61, pending: 28, cancelled: 7, total: 96 },
  { name: "Fr<PERSON>", completed: 55, pending: 35, cancelled: 12, total: 102 },
  { name: "Sat", completed: 67, pending: 40, cancelled: 15, total: 122 },
  { name: "<PERSON>", completed: 58, pending: 32, cancelled: 9, total: 99 },
]

export function BookingStats() {
  return (
    <div className="h-[400px]">
      <CardHeader className="flex flex-row items-center justify-between border-b">
        <div>
          <CardTitle className="text-xl font-semibold">Booking Statistics</CardTitle>
          <p className="text-sm text-muted-foreground mt-1">
            Daily booking overview for the current week
          </p>
        </div>
        <Select defaultValue="weekly">
          <SelectTrigger className="w-[180px] bg-background/50">
            <SelectValue placeholder="Select period" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="daily">Today</SelectItem>
            <SelectItem value="weekly">This Week</SelectItem>
            <SelectItem value="monthly">This Month</SelectItem>
            <SelectItem value="yearly">This Year</SelectItem>
          </SelectContent>
        </Select>
      </CardHeader>
      <CardContent className="bg-background/50 p-4">
        <ResponsiveContainer width="100%" height={300}>
          <BarChart data={bookingData}>
            <CartesianGrid strokeDasharray="3 3" stroke="#374151" />
            <XAxis dataKey="name" stroke="#6B7280" />
            <YAxis stroke="#6B7280" />
            <Tooltip 
              contentStyle={{ 
                backgroundColor: '#1F2937',
                border: 'none',
                boxShadow: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
              }}
            />
            <Legend 
              wrapperStyle={{
                paddingTop: '20px'
              }}
            />
            <Bar 
              dataKey="completed" 
              name="Completed" 
              fill="#10B981" 
            />
            <Bar 
              dataKey="pending" 
              name="Pending" 
              fill="#F59E0B" 
            />
            <Bar 
              dataKey="cancelled" 
              name="Cancelled" 
              fill="#EF4444" 
            />
          </BarChart>
        </ResponsiveContainer>
      </CardContent>
    </div>
  )
} 