"use client"

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>ontent } from "@/components/ui/card"

const activities = [
  {
    user: "John Do<PERSON>",
    action: "Created new booking",
    time: "2 minutes ago",
  },
  {
    user: "<PERSON>",
    action: "Updated profile",
    time: "5 minutes ago",
  },
  {
    user: "<PERSON>",
    action: "Cancelled reservation",
    time: "10 minutes ago",
  },
  {
    user: "<PERSON>",
    action: "Left a review",
    time: "15 minutes ago",
  },
]

export function RecentActivities() {
  return (
    <>
      <CardHeader>
        <CardTitle>Recent Activities</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-8">
          {activities.map((activity, index) => (
            <div key={index} className="flex items-center">
              <div className="ml-4 space-y-1">
                <p className="text-sm font-medium leading-none">{activity.user}</p>
                <p className="text-sm text-muted-foreground">{activity.action}</p>
                <p className="text-xs text-muted-foreground">{activity.time}</p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </>
  )
} 