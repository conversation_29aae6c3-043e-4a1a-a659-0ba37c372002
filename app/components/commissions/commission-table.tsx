"use client"

import { Input } from "@/app/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Badge } from "@/app/components/ui/badge"
import { Button } from "@/app/components/ui/button"
import { Download, MoreHorizontal, Eye, EyeOff, ChevronDown, ChevronUp, Filter, X } from "lucide-react"
import { useState } from "react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { DatePickerWithRange } from "@/app/components/ui/date-range-picker"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { DataTable } from "@/app/components/ui/data-table/data-table"
import React from "react"
import { Checkbox } from "@/app/components/ui/checkbox"
import { cn } from "@/app/lib/utils"
import { BaseTable } from "@/app/components/ui/data-table/base-table"
import { DateRange } from "react-day-picker"

interface Commission {
  id: string
  supplierName: string
  fileDate: string
  checkNumber: string
  totalCount: number
  unmatched: number
  matched: number
  approved: number
  autoApproved: number
  cancelled: number
  uploaded: string
  status: "PROCESSED"
}

const initialCommissions: Commission[] = [
  {
    id: "596",
    supplierName: "Centrav",
    fileDate: "07/03/2024",
    checkNumber: "Test542342",
    totalCount: 80,
    unmatched: 0,
    matched: 0,
    approved: 41,
    autoApproved: 7,
    cancelled: 0,
    uploaded: "07/03/2024",
    status: "PROCESSED",
  },
  {
    id: "580",
    supplierName: "Delta",
    fileDate: "07/01/2024",
    checkNumber: "jh45",
    totalCount: 346,
    unmatched: 0,
    matched: 0,
    approved: 344,
    autoApproved: 3,
    cancelled: 0,
    uploaded: "07/01/2024",
    status: "PROCESSED",
  },
  {
    id: "578",
    supplierName: "Sceptre Vacations",
    fileDate: "06/04/2024",
    checkNumber: "sceptretest123",
    totalCount: 31,
    unmatched: 0,
    matched: 0,
    approved: 31,
    autoApproved: 0,
    cancelled: 0,
    uploaded: "06/03/2024",
    status: "PROCESSED",
  },
  {
    id: "577",
    supplierName: "Vacasa",
    fileDate: "05/15/2024",
    checkNumber: "VACASA051520 24 ACH",
    totalCount: 45,
    unmatched: 0,
    matched: 0,
    approved: 45,
    autoApproved: 2,
    cancelled: 0,
    uploaded: "05/22/2024",
    status: "PROCESSED",
  },
  {
    id: "576",
    supplierName: "RateHawk",
    fileDate: "03/21/2024",
    checkNumber: "RH022024IR",
    totalCount: 125,
    unmatched: 0,
    matched: 0,
    approved: 114,
    autoApproved: 57,
    cancelled: 0,
    uploaded: "05/22/2024",
    status: "PROCESSED",
  },
  {
    id: "575",
    supplierName: "Pegasus",
    fileDate: "05/15/2024",
    checkNumber: "TestPegasus2",
    totalCount: 131,
    unmatched: 0,
    matched: 0,
    approved: 46,
    autoApproved: 34,
    cancelled: 0,
    uploaded: "05/15/2024",
    status: "PROCESSED",
  },
  {
    id: "567",
    supplierName: "Carnival Cruise",
    fileDate: "05/08/2024",
    checkNumber: "clonetest_latest1",
    totalCount: 111,
    unmatched: 0,
    matched: 0,
    approved: 83,
    autoApproved: 0,
    cancelled: 0,
    uploaded: "05/08/2024",
    status: "PROCESSED",
  },
  {
    id: "566",
    supplierName: "Carnival Cruise",
    fileDate: "05/08/2024",
    checkNumber: "clonetestlatest",
    totalCount: 111,
    unmatched: 0,
    matched: 0,
    approved: 83,
    autoApproved: 0,
    cancelled: 0,
    uploaded: "05/08/2024",
    status: "PROCESSED",
  },
  {
    id: "560",
    supplierName: "Tbo",
    fileDate: "04/04/2024",
    checkNumber: "TBO04042024 test",
    totalCount: 11,
    unmatched: 0,
    matched: 0,
    approved: 7,
    autoApproved: 1,
    cancelled: 0,
    uploaded: "05/02/2024",
    status: "PROCESSED",
  },
  {
    id: "559",
    supplierName: "RedAwning",
    fileDate: "04/09/2024",
    checkNumber: "RA04092024 ACH",
    totalCount: 5,
    unmatched: 0,
    matched: 0,
    approved: 5,
    autoApproved: 0,
    cancelled: 0,
    uploaded: "05/02/2024",
    status: "PROCESSED",
  },
  {
    id: "558",
    supplierName: "London Theatre",
    fileDate: "04/19/2024",
    checkNumber: "LTD-MARCH-2024",
    totalCount: 101,
    unmatched: 0,
    matched: 0,
    approved: 83,
    autoApproved: 73,
    cancelled: 2,
    uploaded: "05/02/2024",
    status: "PROCESSED",
  },
  {
    id: "556",
    supplierName: "RedAwning",
    fileDate: "05/01/2024",
    checkNumber: "sampletest",
    totalCount: 8,
    unmatched: 0,
    matched: 0,
    approved: 8,
    autoApproved: 0,
    cancelled: 0,
    uploaded: "05/01/2024",
    status: "PROCESSED",
  },
  {
    id: "554",
    supplierName: "Expedia Taap",
    fileDate: "04/26/2024",
    checkNumber: "EXP TAAP test",
    totalCount: 44,
    unmatched: 0,
    matched: 0,
    approved: 38,
    autoApproved: 24,
    cancelled: 0,
    uploaded: "04/26/2024",
    status: "PROCESSED",
  },
  {
    id: "551",
    supplierName: "Avis Car Rental",
    fileDate: "03/28/2024",
    checkNumber: "AVIS03282024 ACH-test",
    totalCount: 298,
    unmatched: 0,
    matched: 0,
    approved: 61,
    autoApproved: 47,
    cancelled: 0,
    uploaded: "04/18/2024",
    status: "PROCESSED",
  }
]

// Add this interface for column visibility
interface ColumnVisibility {
  id: boolean
  supplierName: boolean
  fileDate: boolean
  checkNumber: boolean
  totalCount: boolean
  unmatched: boolean
  matched: boolean
  approved: boolean
  autoApproved: boolean
  cancelled: boolean
  uploaded: boolean
  status: boolean
  actions: boolean
}

export function CommissionTable() {
  const [status, setStatus] = useState<string | undefined>()
  const [supplier, setSupplier] = useState<string | undefined>()
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [selectedRows, setSelectedRows] = useState<Set<string>>(new Set())
  const [filteredData, setFilteredData] = useState(initialCommissions)
  const [filter, setFilter] = useState("")

  // Update handleReset to also clear the search filter
  const handleReset = () => {
    setStatus(undefined)
    setSupplier(undefined)
    setDateRange(undefined)
    setFilteredData(initialCommissions)
    setFilter("")
  }

  // Update handleFilters to include the search filter
  const handleFilters = () => {
    let filtered = [...initialCommissions]

    // Apply search filter
    if (filter) {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().includes(filter.toLowerCase())
      )
    }

    if (status && status !== 'all') {
      filtered = filtered.filter(
        item => item.status.toLowerCase() === status
      )
    }

    if (supplier && supplier !== 'all') {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().replace(/\s+/g, '-') === supplier
      )
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.fileDate)
        return itemDate >= dateRange.from! && itemDate <= dateRange.to!
      })
    }

    setFilteredData(filtered)
  }

  // Update the search filter handler to work with other filters
  const handleSearchFilter = (value: string) => {
    setFilter(value)
    let filtered = [...initialCommissions]

    if (value) {
      filtered = filtered.filter(
        commission => commission.supplierName.toLowerCase().includes(value.toLowerCase())
      )
    }

    // Apply other active filters
    if (status && status !== 'all') {
      filtered = filtered.filter(
        item => item.status.toLowerCase() === status
      )
    }

    if (supplier && supplier !== 'all') {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().replace(/\s+/g, '-') === supplier
      )
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.fileDate)
        return itemDate >= dateRange.from! && itemDate <= dateRange.to!
      })
    }

    setFilteredData(filtered)
  }

  const toggleRow = (id: string) => {
    setExpandedRows(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }

  const handleSelectAll = () => {
    if (selectedRows.size === filteredData.length) {
      setSelectedRows(new Set())
    } else {
      setSelectedRows(new Set(filteredData.map(row => row.id)))
    }
  }

  const handleSelectRow = (id: string) => {
    setSelectedRows(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }

  const filterControls = (
    <div className="rounded-lg border bg-white">
      <div className="flex items-center justify-between border-b px-4 py-2.5">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5 text-gray-700" />
          <span className="font-semibold text-base text-gray-900">Filters</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            className="h-8 text-sm font-medium flex items-center gap-1.5"
          >
            <ChevronUp className="h-4 w-4" />
            <span className="text-gray-700">Collapse</span>
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-8 text-sm font-medium flex items-center gap-1.5"
          >
            <X className="h-4 w-4" />
            <span className="text-gray-700">Reset</span>
          </Button>
          <Button
            size="sm"
            onClick={handleFilters}
            className="h-8 bg-[#4F46E5] hover:bg-[#4338CA] text-sm font-medium flex items-center gap-1.5"
          >
            <Filter className="h-4 w-4" />
            <span>Apply Filters</span>
          </Button>
        </div>
      </div>
      
      <div className="p-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Status</label>
            <Select value={status} onValueChange={setStatus}>
              <SelectTrigger>
                <SelectValue placeholder="Select status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="processed">Processed</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="failed">Failed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Supplier</label>
            <Select value={supplier} onValueChange={setSupplier}>
              <SelectTrigger>
                <SelectValue placeholder="Select supplier" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Suppliers</SelectItem>
                {Array.from(new Set(initialCommissions.map(c => c.supplierName)))
                  .sort()
                  .map(name => (
                    <SelectItem 
                      key={name} 
                      value={name.toLowerCase().replace(/\s+/g, '-')}
                    >
                      {name}
                    </SelectItem>
                  ))
                }
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium text-gray-700">Date Range</label>
            <DatePickerWithRange
              value={dateRange}
              onChange={setDateRange}
            />
          </div>
        </div>
      </div>
    </div>
  )

  const topContent = (
    <div className="space-y-4">
      {filterControls}
    </div>
  )

  const mainColumns = [
    {
      id: "id",
      header: "ID",
      cell: (row: Commission) => row.id,
      className: "w-[14.28%]",
    },
    {
      id: "supplierName",
      header: "Supplier Name",
      cell: (row: Commission) => row.supplierName,
      className: "w-[14.28%]",
    },
    {
      id: "fileDate",
      header: "File Date",
      cell: (row: Commission) => row.fileDate,
      className: "w-[14.28%]",
    },
    {
      id: "checkNumber",
      header: "Check #",
      cell: (row: Commission) => (
        <span className="font-mono">{row.checkNumber}</span>
      ),
      className: "w-[14.28%]",
    },
    {
      id: "totalCount",
      header: "Total",
      cell: (row: Commission) => (
        <div className="text-right font-medium">{row.totalCount}</div>
      ),
      className: "w-[14.28%]",
    },
    {
      id: "status",
      header: "Status",
      cell: (row: Commission) => (
        <Badge variant="secondary" className="uppercase bg-green-100 text-green-800 hover:bg-green-100">
          {row.status}
        </Badge>
      ),
      className: "w-[14.28%]",
    }
  ]

  const detailColumns = [
    {
      id: "unmatched",
      header: "Unmatched",
      cell: (row: Commission) => (
        <div className="text-right">{row.unmatched}</div>
      ),
    },
    {
      id: "matched",
      header: "Matched",
      cell: (row: Commission) => (
        <div className="text-right">{row.matched}</div>
      ),
    },
    {
      id: "approved",
      header: "Approved",
      cell: (row: Commission) => (
        <div className="text-right text-green-600 font-medium">
          {row.approved}
        </div>
      ),
    },
    {
      id: "autoApproved",
      header: "Auto Approved",
      cell: (row: Commission) => (
        <div className="text-right text-blue-600 font-medium">
          {row.autoApproved}
        </div>
      ),
    },
    {
      id: "cancelled",
      header: "Cancelled",
      cell: (row: Commission) => (
        <div className="text-right">{row.cancelled}</div>
      ),
    },
    {
      id: "uploaded",
      header: "Uploaded",
      cell: (row: Commission) => row.uploaded,
    },
  ]

  return (
    <BaseTable
      data={filteredData}
      mainColumns={mainColumns}
      detailColumns={detailColumns}
      filterPlaceholder="Filter by supplier name..."
      topContent={topContent}
      onFilter={handleSearchFilter}
    />
  )
} 