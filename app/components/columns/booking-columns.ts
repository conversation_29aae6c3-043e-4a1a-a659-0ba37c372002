import type { BookingSearchResult } from "../../types/booking"

export const bookingColumns = [
  {
    key: "iBookingID",
    header: "Booking ID",
  },
  {
    key: "vcPIN",
    header: "PIN",
  },
  {
    key: "vcConfirmationID",
    header: "Confirmation",
  },
  {
    key: "vcClientName",
    header: "Client Name",
  },
  {
    key: "dTravelStartDate",
    header: "Start Date",
  },
  {
    key: "dTravelEndDate",
    header: "End Date",
  },
  {
    key: "vcFinalDestination",
    header: "Destination",
  }
] 