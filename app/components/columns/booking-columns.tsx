import { format } from "date-fns"

interface BookingSearchResult {
  iBookingID: number
  vcConfirmationID: string
  dTravelStartDate: string
  dTravelEndDate: string
  vcClientName: string
  vcFinalDestination: string
  vcPIN: string
  vcTripID: string
  nEngineID: number
  nSalesContactID: number
  nBookingStatusID: number
  selected?: boolean
  toggleSelected?: (checked: boolean) => void
}

export type { BookingSearchResult } 