"use client"

import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/app/lib/utils"
import { buttonVariants } from "@/app/components/ui/button"

interface SidebarNavProps extends React.HTMLAttributes<HTMLElement> {
  items: {
    title: string
    href: string
    icon?: string
    items?: { title: string; href: string }[]
  }[]
}

export const sidebarNavItems = [
  {
    title: "Admin",
    items: [
      {
        title: "Error Management",
        href: "/admin/error-management",
      },
      {
        title: "Reactivation Report",
        href: "/admin/reactivation-report",
      },
      {
        title: "Voiceshot Bounce Report",
        href: "/admin/voiceshot-bounce-report",
      },
      {
        title: "World View",
        href: "/admin/world-view",
      },
      {
        title: "Ticket Limit Report",
        href: "/admin/ticket-limit-report",
      },
    ],
  },
  {
    title: "Marketing",
    items: [
      {
        title: "Weekly Deals",
        href: "/admin/marketing/weekly-deals",
      },
      // Add other marketing items here
    ],
  },
  {
    title: "Suppliers",
    items: [
      {
        title: "All Suppliers",
        href: "/suppliers",
      },
      {
        title: "New Supplier",
        href: "/suppliers/new",
      },
      {
        title: "MOR",
        href: "/suppliers/mor",
      },
    ],
  },
  {
    title: "Finance",
    items: [
      {
        title: "Transactions",
        href: "/transactions",
      },
      {
        title: "Commission Check Runs",
        href: "/commissions/check-run-amounts",
      },
    ],
  },
]

export function SidebarNav({ className, items, ...props }: SidebarNavProps) {
  const pathname = usePathname()

  return (
    <nav className={cn("flex flex-col space-y-1", className)} {...props}>
      {items.map((item) => {
        const isCurrentPage = pathname === item.href
        
        return (
          <div key={item.href} className="space-y-1">
            <Link
              href={item.href}
              className={cn(
                buttonVariants({ variant: "ghost" }),
                isCurrentPage
                  ? "bg-muted hover:bg-muted"
                  : "hover:bg-transparent hover:underline",
                "justify-start"
              )}
            >
              {item.title}
            </Link>
            
            {item.items?.map((subItem) => {
              const isCurrentSubPage = pathname === subItem.href
              
              return (
                <Link
                  key={subItem.href}
                  href={subItem.href}
                  className={cn(
                    buttonVariants({ variant: "ghost" }),
                    isCurrentSubPage
                      ? "bg-muted hover:bg-muted"
                      : "hover:bg-transparent hover:underline",
                    "justify-start pl-8 text-sm"
                  )}
                >
                  {subItem.title}
                </Link>
              )
            })}
          </div>
        )
      })}
    </nav>
  )
} 