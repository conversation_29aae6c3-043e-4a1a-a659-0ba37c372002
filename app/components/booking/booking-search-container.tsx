'use client'

import { Suspense } from 'react'
import { ApolloProvider } from '@apollo/client'
import { getClient } from '@/app/lib/apollo-client'
import { BookingSearchContent } from '@/app/components/booking-search/booking-search-content'
import { LoadingSpinner } from '@/app/components/ui/loading-spinner'

export function BookingSearchContainer() {
  const client = getClient();
  
  return (
    <ApolloProvider client={client}>
      <Suspense fallback={<div className="flex justify-center items-center min-h-[200px]">
        <LoadingSpinner />
      </div>}>
        <BookingSearchContent />
      </Suspense>
    </ApolloProvider>
  )
} 