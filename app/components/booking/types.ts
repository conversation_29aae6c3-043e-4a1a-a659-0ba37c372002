import { DateRange } from "react-day-picker"

export interface SearchFilters {
  page: number
  pageSize: number
  filters: {
    iBookingID?: { operator: string; value: number }
    vcConfirmationID?: { operator: string; value: string }
    dTravelStartDate?: { operator: string; value: string }
    dTravelEndDate?: { operator: string; value: string }
    [key: string]: { operator: string; value: any } | undefined
  }
} 