"use client"

import { useCallback, useState } from "react"
import { useSearchParams } from "next/navigation"
import { type DateRange as DayPickerDateRange } from "react-day-picker"
import { Input } from "@/components/ui/input"
import { DatePickerWithRange } from "@/components/ui/date-picker-with-range"
import { Button } from "@/components/ui/button"
import { format } from "date-fns"
import { MagnifyingGlassIcon, ResetIcon } from "@radix-ui/react-icons"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { SearchFilters } from "./types"

interface BookingSearchFiltersProps {
  onSearch: (filters: SearchFilters) => void
}

export function BookingSearchFilters({ onSearch }: BookingSearchFiltersProps) {
  const searchParams = useSearchParams()
  
  const [confirmationId, setConfirmationId] = useState(searchParams.get("confirmationId") || "")
  const [advisorPin, setAdvisorPin] = useState(searchParams.get("advisorPin") || "")
  const [dateRange, setDateRange] = useState<DayPickerDateRange | undefined>(() => {
    const dateRangeStr = searchParams.get("dateRange")
    return dateRangeStr ? parseDateRange(dateRangeStr) : undefined
  })

  const handleSearch = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    
    const filters: SearchFilters = {
      page: 1,
      pageSize: 10,
      filters: {}
    }

    if (confirmationId) {
      // Assuming confirmationId could be either a booking ID number or confirmation string
      const isNumeric = /^\d+$/.test(confirmationId)
      
      if (isNumeric) {
        filters.filters.iBookingID = {
          operator: "equals",
          value: parseInt(confirmationId, 10)
        }
      } else {
        filters.filters.vcConfirmationID = {
          operator: "contains",
          value: confirmationId
        }
      }
    }

    if (dateRange?.from) {
      filters.filters.dTravelStartDate = {
        operator: "greaterThanOrEqual",
        value: format(dateRange.from, "yyyy-MM-dd")
      }
    }

    if (dateRange?.to) {
      filters.filters.dTravelEndDate = {
        operator: "lessThanOrEqual",
        value: format(dateRange.to, "yyyy-MM-dd")
      }
    }

    onSearch(filters)
  }, [onSearch, confirmationId, advisorPin, dateRange])

  const handleReset = useCallback(() => {
    setConfirmationId("")
    setAdvisorPin("")
    setDateRange(undefined)
    onSearch({
      page: 1,
      pageSize: 10,
      filters: {}
    })
  }, [onSearch])

  return (
    <Card className="border-0 shadow-none">
      <CardHeader className="px-0">
        <CardTitle className="text-lg font-medium">Search Bookings</CardTitle>
      </CardHeader>
      <CardContent className="px-0">
        <form onSubmit={handleSearch} className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="space-y-2">
              <Label htmlFor="confirmationId">Booking Number</Label>
              <Input
                id="confirmationId"
                value={confirmationId}
                onChange={(e) => setConfirmationId(e.target.value)}
                placeholder="Enter booking number"
                className="w-full"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="advisorPin">Advisor PIN</Label>
              <Input
                id="advisorPin"
                value={advisorPin}
                onChange={(e) => setAdvisorPin(e.target.value)}
                placeholder="Enter advisor PIN"
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>Travel Dates</Label>
              <DatePickerWithRange
                date={dateRange}
                onDateChange={setDateRange}
              />
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <Button 
              type="button"
              variant="outline"
              onClick={handleReset}
              className="w-full sm:w-auto"
            >
              <ResetIcon className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button 
              type="submit"
              className="w-full sm:w-auto"
            >
              <MagnifyingGlassIcon className="mr-2 h-4 w-4" />
              Search
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}

function parseDateRange(value: string): DayPickerDateRange | undefined {
  const [from, to] = value.split(",")
  if (!from || !to) return undefined
  
  return {
    from: new Date(from),
    to: new Date(to)
  }
}

function serializeDateRange(range: DayPickerDateRange): string {
  if (!range?.from || !range?.to) return ""
  return `${format(range.from, "yyyy-MM-dd")},${format(range.to, "yyyy-MM-dd")}`
} 