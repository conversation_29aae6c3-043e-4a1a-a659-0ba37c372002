'use client'

import { useQuery } from '@apollo/client'
import { BOOKING_SEARCH_QUERY } from '@/app/graphql/queries/booking-search'
import { useState } from 'react'
import { LoadingSpinner } from '@/app/components/ui/loading-spinner'

// Add this interface for the booking type
interface Booking {
  iBookingID: string | number
  vcClientName: string
  vcConfirmationID: string
  dTravelStartDate: string
  dTravelEndDate: string
}

export function BookingSearchContent() {
  const [page, setPage] = useState(1)
  const [pageSize, setPageSize] = useState(10)
  const [filters, setFilters] = useState(null)

  const { loading, error, data } = useQuery(BOOKING_SEARCH_QUERY, {
    variables: {
      page,
      pageSize,
      filters
    }
  })

  if (loading) return <LoadingSpinner />
  if (error) return <div className="text-red-500">Error: {error.message}</div>

  const { items, total, totalPages } = data?.searchBookings || { items: [], total: 0, totalPages: 0 }

  return (
    <div className="space-y-4">
      <h2 className="text-2xl font-bold">Booking Search Results</h2>
      <p className="text-muted-foreground">Found {total} bookings</p>
      
      {/* Render your booking results here */}
      <div className="space-y-2">
        {items.map((booking: Booking) => (
          <div key={booking.iBookingID} className="p-4 border rounded-md">
            <h3 className="font-medium">{booking.vcClientName}</h3>
            <p>Confirmation: {booking.vcConfirmationID}</p>
            <p>Travel dates: {new Date(booking.dTravelStartDate).toLocaleDateString()} - {new Date(booking.dTravelEndDate).toLocaleDateString()}</p>
          </div>
        ))}
      </div>

      {/* Pagination controls */}
      {totalPages > 1 && (
        <div className="flex justify-between items-center">
          <button 
            onClick={() => setPage(p => Math.max(1, p - 1))}
            disabled={page === 1}
            className="px-4 py-2 border rounded disabled:opacity-50"
          >
            Previous
          </button>
          <span>Page {page} of {totalPages}</span>
          <button 
            onClick={() => setPage(p => Math.min(totalPages, p + 1))}
            disabled={page === totalPages}
            className="px-4 py-2 border rounded disabled:opacity-50"
          >
            Next
          </button>
        </div>
      )}
    </div>
  )
} 