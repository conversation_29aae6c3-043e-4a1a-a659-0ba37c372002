"use client"

import * as React from "react"
import { useEffect, useRef } from "react"
import { useRout<PERSON>, useSearchParams } from "next/navigation"
import { BOOKING_SEARCH_QUERY } from '@/app/graphql/queries/booking-search'
import { SEARCH_FIELDS } from "@/app/config/search-fields"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { PaginationSection } from "@/app/components/ui/pagination-section"
import { useQuery } from '@apollo/client'
import { Suspense } from 'react'
import { BookingTable } from "@/app/components/booking-table"
import { BookingSearchResult } from "@/app/graphql/queries/booking-search"
import { Checkbox } from "@/app/components/ui/checkbox"
import { Button } from "@/app/components/ui/button"
import { MoreHorizontal, ChevronDown, ChevronRight } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"
import { LoadingSpinner } from '@/app/components/ui/loading-spinner'

interface BookingSearchResultsProps {
  searchParams: { [key: string]: string }
}

const BOOKING_STATUS = {
  1: { label: 'Booked', variant: 'success' },
  2: { label: 'Canceled', variant: 'destructive' },
  3: { label: 'Pending', variant: 'warning' },
  4: { label: 'Needs Attention', variant: 'default' }
} as const

const BOOKING_TYPE = {
  1: { label: 'Air', color: 'sky' },
  2: { label: 'Car', color: 'orange' },
  3: { label: 'Hotel', color: 'indigo' },
  4: { label: 'Cruise', color: 'cyan' },
  5: { label: 'Vacation', color: 'purple' },
  6: { label: 'Insurance', color: 'emerald' },
  7: { label: 'Fee', color: 'gray' },
  8: { label: 'Passport/Visa', color: 'rose' },
  9: { label: 'Events', color: 'pink' },
  10: { label: 'Rail', color: 'amber' },
  11: { label: 'Activities', color: 'lime' }
} as const

function BookingStatusBadge({ status }: { status: number | null }) {
  if (!status) return <div className="inline-flex bg-gray-100 text-gray-800 px-2 py-1 text-xs font-semibold">Unknown</div>
  const statusInfo = BOOKING_STATUS[status as keyof typeof BOOKING_STATUS]
  
  if (!statusInfo) return <div className="inline-flex bg-gray-100 text-gray-800 px-2 py-1 text-xs font-semibold">{status}</div>
  
  return (
    <div className={cn(
      "inline-flex px-3 py-1 text-xs font-medium bg-white border",
      statusInfo.variant === 'success' && "border-green-200 text-green-700",
      statusInfo.variant === 'destructive' && "border-red-200 text-red-700",
      statusInfo.variant === 'warning' && "border-yellow-200 text-yellow-700",
      statusInfo.variant === 'default' && "border-blue-200 text-blue-700"
    )}>
      {statusInfo.label}
    </div>
  )
}

function BookingTypeBadge({ type }: { type: number | null }) {
  if (!type) return <div className="inline-flex bg-gray-100 text-gray-800 px-2 py-1 text-xs font-semibold">Unknown</div>
  const typeInfo = BOOKING_TYPE[type as keyof typeof BOOKING_TYPE]
  
  if (!typeInfo) return <div className="inline-flex bg-gray-100 text-gray-800 px-2 py-1 text-xs font-semibold">{type}</div>
  
  return (
    <div className={cn(
      "inline-flex px-2 py-1 text-xs font-semibold",
      typeInfo.color === 'sky' && "bg-sky-100 text-sky-700",
      typeInfo.color === 'orange' && "bg-orange-100 text-orange-700",
      typeInfo.color === 'indigo' && "bg-indigo-100 text-indigo-700",
      typeInfo.color === 'cyan' && "bg-cyan-100 text-cyan-700",
      typeInfo.color === 'purple' && "bg-purple-100 text-purple-700",
      typeInfo.color === 'emerald' && "bg-emerald-100 text-emerald-700",
      typeInfo.color === 'gray' && "bg-gray-100 text-gray-700",
      typeInfo.color === 'rose' && "bg-rose-100 text-rose-700",
      typeInfo.color === 'pink' && "bg-pink-100 text-pink-700",
      typeInfo.color === 'amber' && "bg-amber-100 text-amber-700",
      typeInfo.color === 'lime' && "bg-lime-100 text-lime-700"
    )}>
      {typeInfo.label}
    </div>
  )
}

// Update the type definition
type BreakpointColumns = {
  sm: string[];
  md: string[];
  lg: string[];
  xl: string[];
}

// Update breakpoint columns with better organization
const BREAKPOINT_COLUMNS: BreakpointColumns = {
  sm: [
    'Client Name',
    'Booking Status',
    'Actions'
  ],
  md: [
    'Client Name',
    'Travel Start Date',
    'Booking Status',
    'Actions'
  ],
  lg: [ // Laptop view (1366x768) - show 5 essential columns
    'Client Name',
    'Travel Start Date',
    'Travel End Date',
    'Booking Status',
    'Total Amount',
    'Actions'
  ],
  xl: [ // Full desktop view - Currency moved to expandable
    'Booking ID',
    'Client Name',
    'PIN',
    'Confirmation ID',
    'Travel Start Date',
    'Travel End Date',
    'Final Destination',
    'Booking Status',
    'Booking Type',
    'Total Amount',
    'Actions'
  ]
}

export function BookingSearchResultsContent({ searchParams }: BookingSearchResultsProps) {
  const router = useRouter()
  const currentSearchParams = useSearchParams()

  // Initialize all state hooks at the top level
  const [page, setPage] = useState(parseInt(searchParams.page ?? '1'))
  const [pageSize, setPageSize] = useState(parseInt(searchParams.pageSize ?? '10'))
  const [selectedRows, setSelectedRows] = useState<(number | null)[]>([])
  const [expandedRows, setExpandedRows] = useState<(number | null)[]>([])
  const [visibleColumns, setVisibleColumns] = useState<string[]>(BREAKPOINT_COLUMNS.lg)
  const [hiddenColumns, setHiddenColumns] = useState<string[]>([])

  // Set up the resize effect before any conditional returns
  useEffect(() => {
    updateVisibleColumns()
    
    const handleResize = () => {
      updateVisibleColumns()
    }

    window.addEventListener('resize', handleResize)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  const updateVisibleColumns = () => {
    const width = window.innerWidth
    let newVisibleColumns: string[]
    
    if (width < 640) { // mobile
      newVisibleColumns = BREAKPOINT_COLUMNS.sm
    } else if (width < 1024) { // tablet
      newVisibleColumns = BREAKPOINT_COLUMNS.md
    } else if (width < 1440) { // laptop (including 1366x768)
      newVisibleColumns = BREAKPOINT_COLUMNS.lg
    } else { // larger desktops
      newVisibleColumns = BREAKPOINT_COLUMNS.xl
    }
    
    setVisibleColumns(newVisibleColumns)
    setHiddenColumns(BREAKPOINT_COLUMNS.xl.filter(col => !newVisibleColumns.includes(col)))
  }

  const parseFiltersFromUrl = () => {
    const filters: Record<string, { operator: string; value: string | number }> = {}
    
    SEARCH_FIELDS.forEach(field => {
      const filterStr = currentSearchParams.get(field.key)
      if (filterStr) {
        try {
          const filter = JSON.parse(filterStr)
          if (field.type === 'number') {
            filter.value = Number(filter.value)
          }
          filters[field.key] = filter
        } catch (e) {
          console.error(`Error parsing filter for ${field.key}:`, e)
        }
      }
    })

    return filters
  }

  const filters = parseFiltersFromUrl()
  const hasFilters = Object.keys(filters).length > 0

  const { loading, error, data } = useQuery(BOOKING_SEARCH_QUERY, {
    variables: {
      page,
      pageSize,
      filters: Object.keys(filters).length > 0 ? filters : undefined
    }
  })

  const handleSelectAll = (checked: boolean | "indeterminate") => {
    if (checked === true) {
      setSelectedRows((data?.searchBookings?.items ?? []).map((booking: BookingSearchResult) => booking.iBookingID))
    } else {
      setSelectedRows([])
    }
  }

  const handleSelectRow = (bookingId: number | null, checked: boolean) => {
    if (!bookingId) return
    if (checked) {
      setSelectedRows([...selectedRows, bookingId])
    } else {
      setSelectedRows(selectedRows.filter(id => id !== bookingId))
    }
  }

  const toggleRowExpansion = (bookingId: number | null) => {
    if (!bookingId) return
    setExpandedRows(prev => 
      prev.includes(bookingId) 
        ? prev.filter(id => id !== bookingId)
        : [...prev, bookingId]
    )
  }

  const isColumnVisible = (columnName: string) => visibleColumns.includes(columnName)

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(currentSearchParams.toString())
    params.set('page', newPage.toString())
    router.push(`/bookings?${params.toString()}`)
  }

  // Now render based on loading/error state
  if (loading) {
    return <LoadingSpinner />
  }
  
  if (error) {
    return <div className="text-red-500">Error: {error.message}</div>
  }

  const bookings = data?.searchBookings?.items ?? []
  const { total, totalPages, page: currentPage } = data?.searchBookings ?? {
    total: 0,
    totalPages: 0,
    page: 1
  }

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">
        {hasFilters ? 'Search Results' : 'All Bookings'}
      </h2>
      {bookings.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No bookings found
        </div>
      ) : (
        <>
          <div className="border border-slate-200">
            <div className="max-w-full overflow-auto">
              <Table>
                <TableHeader>
                  <TableRow className="border-b border-slate-200">
                    <TableHead className="w-16 bg-white">
                      <div className="flex items-center gap-2">
                        <Checkbox 
                          checked={selectedRows.length === bookings.length}
                          onCheckedChange={handleSelectAll}
                          aria-label="Select all"
                        />
                      </div>
                    </TableHead>
                    {isColumnVisible('Booking ID') && <TableHead>Booking ID</TableHead>}
                    {isColumnVisible('Client Name') && <TableHead>Client Name</TableHead>}
                    {isColumnVisible('PIN') && <TableHead>PIN</TableHead>}
                    {isColumnVisible('Confirmation ID') && <TableHead>Confirmation ID</TableHead>}
                    {isColumnVisible('Travel Start Date') && <TableHead>Travel Start Date</TableHead>}
                    {isColumnVisible('Travel End Date') && <TableHead>Travel End Date</TableHead>}
                    {isColumnVisible('Final Destination') && <TableHead>Final Destination</TableHead>}
                    <TableHead>Booking Status</TableHead>
                    {isColumnVisible('Booking Type') && <TableHead>Booking Type</TableHead>}
                    {isColumnVisible('Total Amount') && <TableHead>Total Amount</TableHead>}
                    <TableHead>Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {bookings.map((booking: BookingSearchResult) => (
                    <React.Fragment key={booking.iBookingID}>
                      <TableRow className="border-b border-slate-100 hover:bg-slate-50/50 transition-colors">
                        <TableCell className="font-medium">
                          <div className="flex items-center gap-2">
                            <Checkbox 
                              checked={selectedRows.includes(booking.iBookingID)}
                              onCheckedChange={(checked) => handleSelectRow(booking.iBookingID, checked as boolean)}
                              aria-label={`Select booking ${booking.iBookingID}`}
                            />
                            <Button
                              variant="ghost"
                              size="sm"
                              className="p-0 h-8 w-8 hover:bg-slate-100"
                              onClick={() => toggleRowExpansion(booking.iBookingID)}
                            >
                              {expandedRows.includes(booking.iBookingID) 
                                ? <ChevronDown className="h-4 w-4 text-slate-500" />
                                : <ChevronRight className="h-4 w-4 text-slate-500" />
                              }
                            </Button>
                          </div>
                        </TableCell>
                        {isColumnVisible('Booking ID') && <TableCell>{booking.iBookingID}</TableCell>}
                        {isColumnVisible('Client Name') && <TableCell>{booking.vcClientName}</TableCell>}
                        {isColumnVisible('PIN') && <TableCell>{booking.vcPIN}</TableCell>}
                        {isColumnVisible('Confirmation ID') && <TableCell>{booking.vcConfirmationID}</TableCell>}
                        {isColumnVisible('Travel Start Date') && <TableCell>{booking.dTravelStartDate ? new Date(booking.dTravelStartDate).toLocaleDateString() : '-'}</TableCell>}
                        {isColumnVisible('Travel End Date') && <TableCell>{booking.dTravelEndDate ? new Date(booking.dTravelEndDate).toLocaleDateString() : '-'}</TableCell>}
                        {isColumnVisible('Final Destination') && <TableCell>{booking.vcFinalDestination}</TableCell>}
                        <TableCell>
                          <BookingStatusBadge status={booking.nBookingStatusID ?? null} />
                        </TableCell>
                        {isColumnVisible('Booking Type') && <TableCell><BookingTypeBadge type={booking.nBookingTypeID} /></TableCell>}
                        {isColumnVisible('Total Amount') && <TableCell>{booking.nTotalCharges?.toFixed(2)}</TableCell>}
                        <TableCell>
                          <DropdownMenu>
                            <DropdownMenuTrigger asChild>
                              <Button variant="ghost" className="h-8 w-8 p-0">
                                <MoreHorizontal className="h-4 w-4" />
                              </Button>
                            </DropdownMenuTrigger>
                            <DropdownMenuContent align="end">
                              <DropdownMenuItem onClick={() => console.log('View', booking.iBookingID)}>
                                View Details
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => router.push(`/bookings/${booking.iBookingID}/edit`)}>
                                Edit Booking
                              </DropdownMenuItem>
                              <DropdownMenuItem onClick={() => console.log('Cancel', booking.iBookingID)}>
                                Cancel Booking
                              </DropdownMenuItem>
                            </DropdownMenuContent>
                          </DropdownMenu>
                        </TableCell>
                      </TableRow>
                      {expandedRows.includes(booking.iBookingID) && (
                        <TableRow>
                          <TableCell colSpan={visibleColumns.length + 2} className="p-0">
                            <div className="p-6 bg-white border-b border-slate-100">
                              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                                <div className="bg-slate-50/50 p-6 border border-slate-200">
                                  <h4 className="text-sm font-semibold text-slate-900 mb-4">Vendor Details</h4>
                                  <div className="space-y-1">
                                    <p><span className="font-medium">Vendor:</span> {booking.vcVendor}</p>
                                    <p><span className="font-medium">GDS:</span> {booking.vcGDS}</p>
                                    <p><span className="font-medium">Address:</span> {booking.vcVendorAddress}</p>
                                    <p><span className="font-medium">City/State:</span> {booking.vcVendorCityState}</p>
                                    <p><span className="font-medium">Postal:</span> {booking.vcVendorPostalCode}</p>
                                    <p><span className="font-medium">Number:</span> {booking.vcVendorNumber}</p>
                                  </div>
                                </div>
                                <div className="bg-slate-50/50 p-6 border border-slate-200">
                                  <h4 className="text-sm font-semibold text-slate-900 mb-4">Booking Details</h4>
                                  <div className="space-y-1">
                                    <p><span className="font-medium">Engine ID:</span> {booking.nEngineID}</p>
                                    <p><span className="font-medium">Lift City:</span> {booking.vcLiftCity}</p>
                                    <p><span className="font-medium">Passengers:</span> {booking.nNumberOfPsgrs}</p>
                                    <p><span className="font-medium">Booked Date:</span> {booking.dBooked ? new Date(Number(booking.dBooked)).toLocaleDateString() : '-'}</p>
                                    <p><span className="font-medium">Trip ID:</span> {booking.vcTripID}</p>
                                    <p><span className="font-medium">Sales Contact ID:</span> {booking.nSalesContactID}</p>
                                  </div>
                                </div>
                                <div className="bg-slate-50/50 p-6 border border-slate-200">
                                  <h4 className="text-sm font-semibold text-slate-900 mb-4">Financial Details</h4>
                                  <div className="space-y-1">
                                    <p><span className="font-medium">Currency:</span> {booking.totalChargesCurrency}</p>
                                    <p><span className="font-medium">Commission Rate:</span> {((booking.nCommissionRate ?? 0) * 100).toFixed(1)}%</p>
                                    <p><span className="font-medium">Admin Fee:</span> {booking.nAdminFee?.toFixed(2) ?? '-'}</p>
                                    <p><span className="font-medium">Total Commission:</span> {booking.nTotalCommission?.toFixed(2) ?? '-'}</p>
                                    <p><span className="font-medium">Agent Commission:</span> {booking.nAgentCommission?.toFixed(2) ?? '-'}</p>
                                  </div>
                                </div>
                              </div>
                            </div>
                          </TableCell>
                        </TableRow>
                      )}
                    </React.Fragment>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
          
          {selectedRows.length > 0 && (
            <div className="border border-slate-200 bg-white p-3 flex items-center justify-between">
              <span className="text-sm text-slate-600">{selectedRows.length} items selected</span>
              <Button
                variant="outline"
                size="sm"
                className="hover:bg-slate-50"
                onClick={() => console.log('Bulk action for', selectedRows)}
              >
                Bulk Action
              </Button>
            </div>
          )}
          
          <PaginationSection
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={total}
            pageSize={pageSize}
            onPageChange={handlePageChange}
          />
        </>
      )}
    </div>
  )
}

// Helper function to render column values
function renderColumnValue(column: string, booking: any) {
  switch (column) {
    case 'Booking ID':
      return booking.iBookingID
    case 'Client Name':
      return booking.vcClientName
    case 'PIN':
      return booking.vcPIN
    case 'Travel Start Date':
      return booking.dTravelStartDate ? new Date(booking.dTravelStartDate).toLocaleDateString() : '-'
    // ... add cases for other columns
    default:
      return '-'
  }
}

interface ResultsClientProps {
  data: BookingSearchResult[]
}

export function ResultsClient({ data }: ResultsClientProps) {
  return (
    <div className="container mx-auto py-10">
      <BookingTable data={data} />
    </div>
  )
}

export function BookingSearchResultsClient(props: BookingSearchResultsProps) {
  return (
    <Suspense fallback={
      <div className="text-center py-8">Loading...</div>
    }>
      <BookingSearchResultsContent {...props} />
    </Suspense>
  )
} 