import { Suspense } from "react"
import { ResultsContainer } from "./results-container"

interface SearchWrapperProps {
  searchParams: { [key: string]: string }
}

export function SearchWrapper({ searchParams }: SearchWrapperProps) {
  return (
    <Suspense fallback={<div className="p-8 text-center">Loading search results...</div>}>
      <ResultsContainer searchParams={searchParams} />
    </Suspense>
  )
} 