"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Checkbox } from "@/app/components/ui/checkbox"
import { formatCurrency } from "@/app/lib/utils"

interface BankBookingsTableProps {
  searchParams: { [key: string]: string }
}

export function BankBookingsTable({ searchParams }: BankBookingsTableProps) {
  const bookings = [
    {
      agentPin: "123456",
      bookingId: "BK001234",
      bookingEngine: "Sabre",
      travelerName: "<PERSON> Smith",
      bookingDate: "01/15/24",
      vendor: "Delta Airlines",
      travelDates: "02/01/24 to 02/08/24",
      totalCharge: 1250.00,
      agentCommission: 125.00,
      destination: "Miami, FL"
    }
    // Add more dummy data as needed
  ]

  return (
    <div className="rounded-none border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Agent Pin</TableHead>
            <TableHead>Booking ID</TableHead>
            <TableHead>Booking Engine</TableHead>
            <TableHead>Traveler's Name</TableHead>
            <TableHead>Booking Date</TableHead>
            <TableHead>Vendor</TableHead>
            <TableHead>Travel Dates</TableHead>
            <TableHead className="text-right">Total Charge</TableHead>
            <TableHead className="text-right">Commission</TableHead>
            <TableHead>Destination</TableHead>
            <TableHead className="w-[50px]">Select</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {bookings.map((booking) => (
            <TableRow key={booking.bookingId}>
              <TableCell className="font-medium">{booking.agentPin}</TableCell>
              <TableCell>{booking.bookingId}</TableCell>
              <TableCell>{booking.bookingEngine}</TableCell>
              <TableCell>{booking.travelerName}</TableCell>
              <TableCell>{booking.bookingDate}</TableCell>
              <TableCell>{booking.vendor}</TableCell>
              <TableCell>{booking.travelDates}</TableCell>
              <TableCell className="text-right">{formatCurrency(booking.totalCharge)}</TableCell>
              <TableCell className="text-right">{formatCurrency(booking.agentCommission)}</TableCell>
              <TableCell>{booking.destination}</TableCell>
              <TableCell>
                <Checkbox name={`BK_${booking.bookingId}`} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 