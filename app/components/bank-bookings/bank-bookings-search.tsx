"use client"

import { Input } from "@/components/ui/input"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Search } from "lucide-react"

export function BankBookingsSearch() {
  return (
    <div className="flex flex-col gap-4 md:flex-row">
      <div className="flex items-center gap-2">
        <Input
          placeholder="Search by PIN"
          className="h-8 w-[150px]"
        />
        <Button size="sm" variant="ghost">
          <Search className="h-4 w-4" />
        </Button>
      </div>
      <div className="flex items-center gap-2">
        <Input
          placeholder="Search by Booking ID"
          className="h-8 w-[150px]"
        />
        <Button size="sm" variant="ghost">
          <Search className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
} 