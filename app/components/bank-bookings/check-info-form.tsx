"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function CheckInfoForm() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-2">
        <Label htmlFor="checkFrom">Check From</Label>
        <Input id="checkFrom" name="checkfrom" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="checkNumber">Check Number</Label>
        <Input id="checkNumber" name="checknum" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="dateDeposited">Date Deposited</Label>
        <Input id="dateDeposited" name="datedeposit" type="date" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="checkDate">Check Date</Label>
        <Input id="checkDate" name="checkdate" type="date" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="checkAmount">Check Amount</Label>
        <Input id="checkAmount" name="checkamount" type="number" step="0.01" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="bookingPin">Find Booking by PIN</Label>
        <Input id="bookingPin" placeholder="Enter PIN" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="bookingId">Find Booking by Booking ID</Label>
        <Input id="bookingId" placeholder="Enter Booking ID" className="rounded-none" />
      </div>
    </div>
  )
} 