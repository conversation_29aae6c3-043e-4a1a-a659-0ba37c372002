"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

export function BankBookingsFilters() {
  return (
    <div className="flex items-center gap-2">
      <Select defaultValue="all">
        <SelectTrigger className="w-[180px]">
          <SelectValue placeholder="Filter by type" />
        </SelectTrigger>
        <SelectContent>
          <SelectItem value="all">All Bookings</SelectItem>
          <SelectItem value="air">Air Bookings</SelectItem>
          <SelectItem value="hotel">Hotel Bookings</SelectItem>
          <SelectItem value="car">Car Bookings</SelectItem>
        </SelectContent>
      </Select>
      <Button variant="outline" size="sm">Reset Filters</Button>
    </div>
  )
} 