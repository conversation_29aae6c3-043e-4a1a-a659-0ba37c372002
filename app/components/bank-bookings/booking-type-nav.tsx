"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter, useSearchParams } from "next/navigation"

const bookingTypes = [
  { title: "Air Bookings", id: "1" },
  { title: "Car Bookings", id: "2" },
  { title: "Hotel Bookings", id: "3" },
  { title: "Cruise Bookings", id: "4" },
  { title: "Vacation Bookings", id: "5" },
  { title: "Insurance Bookings", id: "6" },
] as const

export function BookingTypeNav() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const currentType = searchParams.get("BookingTypeID")

  const handleTypeChange = (typeId: string) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set("BookingTypeID", typeId)
    router.push(`?${params.toString()}`)
  }

  return (
    <div className="flex flex-wrap gap-2">
      {bookingTypes.map((type) => (
        <Button
          key={type.id}
          variant={currentType === type.id ? "default" : "outline"}
          onClick={() => handleTypeChange(type.id)}
          className="rounded-none"
        >
          {type.title}
        </Button>
      ))}
    </div>
  )
} 