"use client"

import * as React from "react"
import { useEffect, useRef, useState } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { BOOKING_SEARCH_QUERY } from '@/app/graphql/queries/booking-search'
import { SEARCH_FIELDS } from "@/app/config/search-fields"
import { BookingTable } from "@/app/components/booking-table"
import { PaginationSection } from "@/app/components/ui/pagination-section"
import type { BookingSearchResult } from '../../types/booking'
import { getClient } from '@/app/lib/apollo-client'

interface BookingSearchResponse {
  searchBookings: {
    items: BookingSearchResult[]
    total: number
    totalPages: number
    page: number
    pageSize: number
  }
}

interface FilterValue {
  operator: string
  value: string | number
}

interface Filters {
  [key: string]: FilterValue
}

interface SearchResultsProps {
  searchParams: { [key: string]: string }
}

export function SearchResultsClient({ searchParams }: SearchResultsProps) {
  const router = useRouter()
  const currentSearchParams = useSearchParams()
  const [isLoading, setIsLoading] = useState(false)
  const [data, setData] = useState<BookingSearchResponse | null>(null)
  const previousVariables = useRef<string>('')
  const isInitialMount = useRef(true)

  const currentPage = parseInt(searchParams.page ?? '1')
  const currentPageSize = parseInt(searchParams.pageSize ?? '10')

  // Derive hasFilters from searchParams
  const hasFilters = Object.keys(searchParams).some(key => 
    SEARCH_FIELDS.some(field => key.startsWith(field.key))
  )

  // Extract data from the response
  const bookings = data?.searchBookings.items ?? []
  const total = data?.searchBookings.total ?? 0
  const totalPages = data?.searchBookings.totalPages ?? 1
  const page = data?.searchBookings.page ?? 1

  const handlePageChange = (newPage: number) => {
    const params = new URLSearchParams(currentSearchParams.toString())
    params.set('page', newPage.toString())
    router.push(`?${params.toString()}`)
  }

  useEffect(() => {
    async function fetchData() {
      setIsLoading(true);
      try {
        const client = getClient();
        const result = await client.query({
          query: BOOKING_SEARCH_QUERY,
          variables: { /* your variables */ }
        });
        setData(result.data);
      } catch (error) {
        console.error(error);
      } finally {
        setIsLoading(false);
      }
    }
    
    fetchData();
  }, [searchParams]);

  return (
    <div className="space-y-4">
      <h2 className="text-xl font-semibold">
        {hasFilters ? 'Search Results' : 'All Bookings'}
      </h2>
      {bookings.length === 0 ? (
        <div className="text-center py-8 text-muted-foreground">
          No bookings found
        </div>
      ) : (
        <>
          <div className="rounded-md border">
            <BookingTable data={bookings} />
          </div>
          
          <PaginationSection
            currentPage={page}
            totalPages={totalPages}
            totalItems={total}
            pageSize={currentPageSize}
            onPageChange={handlePageChange}
          />
        </>
      )}
    </div>
  )
} 