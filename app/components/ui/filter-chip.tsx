'use client'

import { X } from 'lucide-react'
import { But<PERSON> } from "@/components/ui/button"

interface FilterChipProps {
  label: string
  operator: string
  value: string
  onRemove: () => void
}

export function FilterChip({ label, operator, value, onRemove }: FilterChipProps) {
  return (
    <div className="flex items-center gap-2 rounded-md border bg-background px-3 py-1.5 text-sm">
      <span className="font-medium text-muted-foreground">{label}</span>
      <span className="text-muted-foreground">{operator}</span>
      <span>{value}</span>
      <Button
        variant="ghost"
        size="sm"
        className="h-auto p-0 hover:bg-transparent"
        onClick={onRemove}
      >
        <X className="h-4 w-4" />
        <span className="sr-only">Remove filter</span>
      </Button>
    </div>
  )
} 