import * as React from "react"
import { cn } from "@/lib/utils"
import { Button, ButtonProps } from "@/components/ui/button"

export interface ShimmerButtonProps extends ButtonProps {
  shimmerColor?: string
}

export const ShimmerButton = React.forwardRef<HTMLButtonElement, ShimmerButtonProps>(
  ({ className, shimmerColor = "#ffffff", children, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(
          "relative overflow-hidden bg-red-500 text-white",
          "before:absolute before:inset-0",
          "before:translate-x-[-100%]",
          "before:animate-[shimmer_2s_infinite]",
          "before:bg-gradient-to-r",
          `before:from-transparent before:via-${shimmerColor}/10 before:to-transparent`,
          className
        )}
        {...props}
      >
        {children}
      </Button>
    )
  }
)

ShimmerButton.displayName = "ShimmerButton"