import { useState } from "react"

type ToastProps = {
  title: string
  description?: string
  variant?: "default" | "destructive"
}

export const useToast = () => {
  const [toasts, setToasts] = useState<ToastProps[]>([])

  const toast = (props: ToastProps) => {
    setToasts((prevToasts) => [...prevToasts, props])
  }

  const dismissToast = (index: number) => {
    setToasts((prevToasts) => prevToasts.filter((_, i) => i !== index))
  }

  return { toast, toasts, dismissToast }
}

export const toast = (props: ToastProps) => {
  if (typeof window !== "undefined") {
    const event = new CustomEvent("toast", { detail: props })
    window.dispatchEvent(event)
  }
}