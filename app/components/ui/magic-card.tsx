'use client';

import React, { useState, useRef, useEffect } from 'react';

interface MagicCardProps {
  children: React.ReactNode;
  className?: string;
}

export const MagicCard: React.FC<MagicCardProps> = ({ children, className = '' }) => {
  const [{ x, y }, setPosition] = useState({ x: 0, y: 0 });
  const [opacity, setOpacity] = useState(0);
  const cardRef = useRef<HTMLDivElement>(null);

  const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    if (cardRef.current) {
      const rect = cardRef.current.getBoundingClientRect();
      setPosition({ x: e.clientX - rect.left, y: e.clientY - rect.top });
    }
  };

  const handleMouseEnter = () => {
    setOpacity(1);
  };

  const handleMouseLeave = () => {
    setOpacity(0);
  };

  useEffect(() => {
    const card = cardRef.current;
    if (card) {
      card.style.setProperty('--x', `${x}px`);
      card.style.setProperty('--y', `${y}px`);
      card.style.setProperty('--opacity', opacity.toString());
    }
  }, [x, y, opacity]);

  return (
    <div
      ref={cardRef}
      className={`relative overflow-hidden ${className}`}
      onMouseMove={handleMouseMove}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div className="relative z-10">{children}</div>
      <div
        className="absolute inset-0 z-0"
        style={{
          background: 'radial-gradient(600px circle at var(--x) var(--y), rgba(255,255,255,0.4), transparent 40%)',
          opacity: 'var(--opacity)',
          transition: 'opacity 0.2s',
        }}
      />
    </div>
  );
};