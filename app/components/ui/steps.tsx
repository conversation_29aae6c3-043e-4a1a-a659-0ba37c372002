"use client"

import React from "react"
import { cn } from "@/lib/utils"
import { CheckIcon } from "@radix-ui/react-icons"
import { motion } from "framer-motion"

interface StepsProps {
  currentStep: number
  children: React.ReactNode
  className?: string
}

export function Steps({ currentStep, children, className }: StepsProps) {
  const childrenArray = React.Children.toArray(children)
  
  return (
    <div className={cn("w-full", className)}>
      <div className="flex items-center justify-between gap-2">
        {childrenArray.map((child, index) => (
          <React.Fragment key={index}>
            {child}
          </React.Fragment>
        ))}
      </div>
    </div>
  )
}

interface StepProps {
  title: string
  description?: string
  onClick?: () => void
  completed?: boolean
}

export function Step({ title, description, onClick, completed }: StepProps) {
  return (
    <motion.div 
      className={cn(
        "flex flex-col items-center flex-1",
        onClick && "cursor-pointer"
      )}
      onClick={onClick}
      whileHover={{ scale: 1.03 }}
      transition={{ duration: 0.2 }}
    >
      <motion.div 
        className={cn(
          "w-full px-4 py-3 flex flex-col items-center justify-center transition-all duration-300",
          completed ? 
            "bg-[#3C36A9] text-white shadow-md" : 
            onClick ? 
              "bg-indigo-50 text-[#3C36A9] border border-indigo-200" :
              "bg-gray-100 text-gray-500 border border-gray-200"
        )}
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.3 }}
      >
        <div className="flex items-center">
          {completed && <CheckIcon className="w-4 h-4 mr-2" />}
          <span className="font-medium">{title}</span>
        </div>
        
        {description && (
          <div className="text-xs mt-1 text-center opacity-90">
            {description}
          </div>
        )}
      </motion.div>
    </motion.div>
  )
} 