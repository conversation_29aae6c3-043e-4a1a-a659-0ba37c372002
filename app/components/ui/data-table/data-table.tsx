"use client"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { ChevronDown } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { useState } from "react"

interface DataTableProps<T> {
  data: T[]
  columns: ColumnDef<T>[]
  defaultVisibleColumns?: string[]
  filterPlaceholder?: string
  onFilter?: (value: string) => void
  topContent?: React.ReactNode
  hideControls?: boolean
}

interface ColumnDef<T> {
  id: string
  header: string
  cell: (row: T) => React.ReactNode
  defaultVisible?: boolean
  className?: string
}

export function DataTable<T>({
  data,
  columns,
  defaultVisibleColumns,
  filterPlaceholder = "Filter...",
  onFilter,
  topContent,
  hideControls = false,
}: DataTableProps<T>) {
  const [visibleColumns, setVisibleColumns] = useState<Set<string>>(
    new Set(defaultVisibleColumns || columns.map(col => col.id))
  )
  const [filter, setFilter] = useState("")

  const toggleColumn = (columnId: string) => {
    setVisibleColumns(prev => {
      const next = new Set(prev)
      if (next.has(columnId)) {
        next.delete(columnId)
      } else {
        next.add(columnId)
      }
      return next
    })
  }

  const toggleAllColumns = (value: boolean) => {
    setVisibleColumns(
      new Set(value ? columns.map(col => col.id) : [])
    )
  }

  const resetColumns = () => {
    setVisibleColumns(
      new Set(defaultVisibleColumns || columns.map(col => col.id))
    )
  }

  const handleFilterChange = (value: string) => {
    setFilter(value)
    onFilter?.(value)
  }

  return (
    <div className="space-y-4">
      <div className="flex flex-col gap-4">
        {topContent}

        <div className="flex items-center justify-between">
          <Input
            placeholder={filterPlaceholder}
            value={filter}
            onChange={(e) => handleFilterChange(e.target.value)}
            className="max-w-sm"
          />
          {!hideControls && (
            <div className="flex items-center gap-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm">
                    Columns <ChevronDown className="ml-2 h-4 w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-[180px]">
                  <div className="p-2">
                    <div className="flex items-center justify-between pb-4">
                      <div className="text-sm font-medium">Toggle Columns</div>
                      <div className="flex gap-2">
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 px-2 text-xs"
                          onClick={() => toggleAllColumns(true)}
                        >
                          Show all
                        </Button>
                        <Button 
                          variant="ghost" 
                          size="sm"
                          className="h-8 px-2 text-xs"
                          onClick={() => toggleAllColumns(false)}
                        >
                          Hide all
                        </Button>
                      </div>
                    </div>
                    <div className="space-y-1">
                      {columns.map(column => (
                        <DropdownMenuCheckboxItem
                          key={column.id}
                          checked={visibleColumns.has(column.id)}
                          onCheckedChange={() => toggleColumn(column.id)}
                        >
                          {column.header}
                        </DropdownMenuCheckboxItem>
                      ))}
                    </div>
                  </div>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          )}
        </div>
      </div>

      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map(column => 
                  visibleColumns.has(column.id) && (
                    <TableHead 
                      key={column.id}
                      className={column.className}
                    >
                      {column.header}
                    </TableHead>
                  )
                )}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.length === 0 ? (
                <TableRow>
                  <TableCell 
                    colSpan={columns.length} 
                    className="h-24 text-center"
                  >
                    No results found.
                  </TableCell>
                </TableRow>
              ) : (
                data.map((row, i) => (
                  <TableRow key={i}>
                    {columns.map(column => 
                      visibleColumns.has(column.id) && (
                        <TableCell 
                          key={column.id}
                          className={column.className}
                        >
                          {column.cell(row)}
                        </TableCell>
                      )
                    )}
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
} 