import React from 'react';
import { cn } from "@/lib/utils";

interface DotPatternProps {
  className?: string;
  size?: number;
  spacing?: number;
  dotSize?: number;
}

export const DotPattern: React.FC<DotPatternProps> = ({ 
  className = '',
  size = 1,
  spacing = 1,
  dotSize = 1,
}) => {
  return (
    <div className={cn("absolute inset-0 overflow-hidden", className)}>
      <svg
        className="absolute inset-0 h-full w-full"
        xmlns="http://www.w3.org/2000/svg"
        width={size}
        height={size}
        fill="none"
        viewBox={`0 0 ${size} ${size}`}
        style={{
          maskImage: 'linear-gradient(to bottom right, black, transparent)',
          WebkitMaskImage: 'linear-gradient(to bottom right, black, transparent)'
        }}
      >
        <defs>
          <pattern
            id="dot-pattern"
            x="0"
            y="0"
            width={spacing}
            height={spacing}
            patternUnits="userSpaceOnUse"
          >
            <circle cx={spacing / 2} cy={spacing / 2} r={dotSize} fill="rgba(0, 0, 0, 0.1)" />
          </pattern>
        </defs>
        <rect width={size} height={size} fill="url(#dot-pattern)" />
      </svg>
    </div>
  );
};