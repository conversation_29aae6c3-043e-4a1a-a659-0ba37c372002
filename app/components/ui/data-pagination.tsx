"use client"

import { ChevronLeftIcon, ChevronRightIcon } from "lucide-react"
import { Button } from "./button"
import { useRouter } from "next/navigation"

interface DataPaginationProps {
  currentPage: number
  totalPages: number
  pageSize: number
}

export function DataPagination({ currentPage, totalPages, pageSize }: DataPaginationProps) {
  const router = useRouter()

  const handlePageChange = (page: number) => {
    router.push(`/transactions?page=${page}&limit=${pageSize}`)
  }

  return (
    <div className="flex items-center justify-between w-full">
      <div className="text-sm text-muted-foreground">
        Page {currentPage} of {totalPages}
      </div>
      <div className="flex items-center space-x-2">
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage <= 1}
          className="h-9 w-9 p-0 rounded-none"
        >
          <ChevronLeftIcon className="h-4 w-4" />
        </Button>
        <Button
          variant="outline"
          size="sm"
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage >= totalPages}
          className="h-9 w-9 p-0 rounded-none"
        >
          <ChevronRightIcon className="h-4 w-4" />
        </Button>
      </div>
    </div>
  )
} 