"use client"

import Link from "next/link"
import { <PERSON>tonProps, <PERSON><PERSON> } from "./button"
import { cn } from "@/lib/utils"

interface PaginationButtonProps extends ButtonProps {
  href: string
}

export function PaginationButton({
  href,
  className,
  ...props
}: PaginationButtonProps) {
  return (
    <Button
      asChild
      className={cn("h-8 w-8 p-0", className)}
      {...props}
    >
      <Link href={href}>
        {props.children}
      </Link>
    </Button>
  )
} 