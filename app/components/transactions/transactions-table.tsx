'use client'

import { <PERSON>H<PERSON>zon<PERSON> } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Checkbox } from "@/app/components/ui/checkbox"
import { useState } from "react"
import { TransactionsResponse } from "@/app/lib/types/transaction"
import { PaginationSection } from "@/app/components/ui/pagination-section"
import { useRouter, useSearchParams } from "next/navigation"

interface Transaction {
  id: string
  date: string
  pin: string
  name: string
  amount: number
  type: string
  paymentMethod: string
  result: 'Success' | 'Failed'
  user: string
}

interface TransactionsTableProps {
  initialData: TransactionsResponse
}

export function TransactionsTable({ initialData }: TransactionsTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedRows, setSelectedRows] = useState<string[]>([])

  function toggleRow(id: string) {
    setSelectedRows((current) => 
      current.includes(id) 
        ? current.filter((row) => row !== id)
        : [...current, id]
    )
  }

  function toggleAll() {
    setSelectedRows((current) => 
      current.length === initialData.transactions.length 
        ? [] 
        : initialData.transactions.map(t => t.iTransactionID)
    )
  }

  function handlePageChange(page: number) {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    router.push(`?${params.toString()}`)
  }

  return (
    <div className="space-y-4">
      {selectedRows.length > 0 && (
        <div className="flex items-center gap-2">
          <p className="text-sm text-muted-foreground">
            {selectedRows.length} items selected
          </p>
          <Button variant="outline" size="sm" onClick={() => setSelectedRows([])} className="rounded-none">
            Clear selection
          </Button>
        </div>
      )}
      
      <div className="border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-12">
                <Checkbox 
                  checked={selectedRows.length === initialData.transactions.length}
                  onCheckedChange={toggleAll}
                  aria-label="Select all"
                />
              </TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Payment Method</TableHead>
              <TableHead>User</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {initialData.transactions.map((transaction) => (
              <TableRow 
                key={transaction.iTransactionID}
                data-state={selectedRows.includes(transaction.iTransactionID) ? "selected" : ""}
              >
                <TableCell>
                  <Checkbox 
                    checked={selectedRows.includes(transaction.iTransactionID)}
                    onCheckedChange={() => toggleRow(transaction.iTransactionID)}
                    aria-label={`Select transaction ${transaction.iTransactionID}`}
                  />
                </TableCell>
                <TableCell>{new Date(transaction.dCreated).toLocaleDateString()}</TableCell>
                <TableCell>${transaction.nAmount.toFixed(2)}</TableCell>
                <TableCell>
                  {transaction.transactionType?.vcTransactionType || 'N/A'}
                </TableCell>
                <TableCell>
                  {transaction.paymentMethod?.vcPaymentMethod || 'N/A'}
                </TableCell>
                <TableCell>
                  {transaction.user?.vcUsername || 'N/A'}
                </TableCell>
                <TableCell>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuItem>View Details</DropdownMenuItem>
                      <DropdownMenuItem>Download Receipt</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Report Issue</DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <div className="border-t p-2">
          <PaginationSection
            currentPage={initialData.page}
            totalPages={initialData.totalPages}
            totalItems={initialData.total}
            pageSize={initialData.pageSize}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  )
} 