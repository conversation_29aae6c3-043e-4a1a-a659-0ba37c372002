'use client'

import * as React from 'react'
import { Button } from '@/components/ui/button'
import { ChevronDown, X } from 'lucide-react'

interface FiltersHeaderProps {
  isCollapsed: boolean
  onToggleCollapse: () => void
  onReset: () => void
  onApply: () => void
}

export function FiltersHeader({ isCollapsed, onToggleCollapse, onReset, onApply }: FiltersHeaderProps) {
  return (
    <div className="flex items-center justify-between border-b pb-4">
      <div className="flex items-center gap-3">
        <div className="flex h-8 w-8 items-center justify-center rounded-md bg-black/5">
          <svg
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
            className="text-black"
          >
            <path
              d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            />
          </svg>
        </div>
        <span className="text-lg font-semibold text-foreground">Filters</span>
      </div>
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={onToggleCollapse}
          className="h-8 px-2 text-sm"
        >
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              isCollapsed ? 'rotate-180' : ''
            }`}
          />
          {isCollapsed ? 'Expand' : 'Collapse'}
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={onReset}
          className="h-8 px-2 text-sm text-muted-foreground hover:text-foreground"
        >
          <X className="mr-2 h-4 w-4" />
          Reset
        </Button>
        <Button
          size="sm"
          onClick={onApply}
          className="h-8 bg-[#3C36A9] hover:bg-[#3C36A9]/90"
        >
          Apply Filters
        </Button>
      </div>
    </div>
  )
} 