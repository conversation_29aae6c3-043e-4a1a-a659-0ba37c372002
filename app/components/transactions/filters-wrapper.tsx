'use client'

import { useState, useCallback } from "react"
import { FilterSection } from "./filter-section"

interface Filter {
  id: string
  type: string
  operator: string
  value: string
}

export function FiltersWrapper() {
  const [filters, setFilters] = useState<Filter[]>([])

  const handleFiltersChange = useCallback((newFilters: Filter[]) => {
    setFilters(newFilters)
    // Handle filter changes here - e.g., filter the static data
    console.log('Applying filters:', newFilters)
  }, [])

  return (
    <div className="bg-card">
      <FilterSection onFiltersChange={handleFiltersChange} />
    </div>
  )
} 