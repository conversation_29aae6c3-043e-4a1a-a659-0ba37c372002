'use client'

import * as React from 'react'
import { format } from 'date-fns'
import { CalendarIcon, ChevronDown, Plus, X } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Calendar } from '@/components/ui/calendar'
import { Input } from '@/components/ui/input'
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { FilterChip } from '@/components/ui/filter-chip'

interface Filter {
  id: string
  type: string
  operator: string
  value: string
}

interface FilterSectionProps {
  onFiltersChange: (filters: Filter[]) => void
}

type OperatorOption = {
  label: string
  value: string
}

type FilterType = {
  label: string
  value: string
}

type SelectOption = {
  label: string
  value: string
}

export function FilterSection({ onFiltersChange }: FilterSectionProps) {
  const [isCollapsed, setIsCollapsed] = React.useState(false)
  const [filters, setFilters] = React.useState<Filter[]>([])
  const [selectedType, setSelectedType] = React.useState<string>('')
  const [selectedOperator, setSelectedOperator] = React.useState<string>('')
  const [selectedDate, setSelectedDate] = React.useState<Date>()
  const [inputValue, setInputValue] = React.useState<string>('')

  const filterTypes: FilterType[] = React.useMemo(() => [
    { label: 'Confirmation ID', value: 'confirmationId' },
    { label: 'Travel Start Date', value: 'travelStartDate' },
    { label: 'Travel End Date', value: 'travelEndDate' },
    { label: 'Booking Date', value: 'bookingDate' },
    { label: 'PIN', value: 'pin' },
    { label: 'Last Name', value: 'lastName' },
    { label: 'First Name', value: 'firstName' },
    { label: 'Email', value: 'email' },
    { label: 'Phone', value: 'phone' },
    { label: 'Amount', value: 'amount' },
    { label: 'Payment Method', value: 'paymentMethod' },
    { label: 'Status', value: 'status' },
    { label: 'Transaction Type', value: 'transactionType' },
  ], [])

  const operators: Record<string, OperatorOption[]> = React.useMemo(() => ({
    confirmationId: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    travelStartDate: [
      { label: 'Before', value: 'before' },
      { label: 'After', value: 'after' },
      { label: 'Between', value: 'between' },
      { label: 'Equals', value: 'equals' }
    ],
    travelEndDate: [
      { label: 'Before', value: 'before' },
      { label: 'After', value: 'after' },
      { label: 'Between', value: 'between' },
      { label: 'Equals', value: 'equals' }
    ],
    bookingDate: [
      { label: 'Before', value: 'before' },
      { label: 'After', value: 'after' },
      { label: 'Between', value: 'between' },
      { label: 'Equals', value: 'equals' }
    ],
    pin: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    lastName: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    firstName: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    email: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    phone: [{ label: 'Equals', value: 'equals' }, { label: 'Contains', value: 'contains' }],
    amount: [
      { label: 'Equals', value: 'equals' },
      { label: 'Greater Than', value: 'greaterThan' },
      { label: 'Less Than', value: 'lessThan' },
      { label: 'Between', value: 'between' }
    ],
    paymentMethod: [
      { label: 'Equals', value: 'equals' },
      { label: 'In', value: 'in' }
    ],
    status: [
      { label: 'Equals', value: 'equals' },
      { label: 'In', value: 'in' }
    ],
    transactionType: [
      { label: 'Equals', value: 'equals' },
      { label: 'In', value: 'in' }
    ]
  }), [])

  const selectOptions: Record<string, SelectOption[]> = React.useMemo(() => ({
    paymentMethod: [
      { label: 'Credit Card', value: 'creditCard' },
      { label: 'PayPal', value: 'paypal' },
      { label: 'Bank Transfer', value: 'bankTransfer' },
      { label: 'Cash', value: 'cash' }
    ],
    status: [
      { label: 'Completed', value: 'completed' },
      { label: 'Pending', value: 'pending' },
      { label: 'Failed', value: 'failed' },
      { label: 'Refunded', value: 'refunded' }
    ],
    transactionType: [
      { label: 'Purchase', value: 'purchase' },
      { label: 'Refund', value: 'refund' },
      { label: 'Deposit', value: 'deposit' },
      { label: 'Withdrawal', value: 'withdrawal' }
    ]
  }), [])

  const isDateFilter = React.useCallback((type: string): boolean => {
    return ['travelStartDate', 'travelEndDate', 'bookingDate'].includes(type)
  }, [])

  const isSelectFilter = React.useCallback((type: string): boolean => {
    return ['paymentMethod', 'status', 'transactionType'].includes(type)
  }, [])

  const addFilter = React.useCallback(() => {
    if (!selectedType || !selectedOperator) return
    
    let value = ''
    if (isDateFilter(selectedType)) {
      if (!selectedDate) return
      value = format(selectedDate, 'yyyy-MM-dd')
    } else if (isSelectFilter(selectedType)) {
      if (!inputValue) return
      value = inputValue
    } else {
      if (!inputValue) return
      value = inputValue
    }

    const newFilter: Filter = {
      id: Math.random().toString(36).substr(2, 9),
      type: selectedType,
      operator: selectedOperator,
      value,
    }

    const updatedFilters = [...filters, newFilter]
    setFilters(updatedFilters)
    onFiltersChange(updatedFilters)

    // Reset selections
    setSelectedType('')
    setSelectedOperator('')
    setSelectedDate(undefined)
    setInputValue('')
  }, [filters, inputValue, isDateFilter, isSelectFilter, onFiltersChange, selectedDate, selectedOperator, selectedType])

  const removeFilter = React.useCallback((id: string) => {
    const updatedFilters = filters.filter(filter => filter.id !== id)
    setFilters(updatedFilters)
    onFiltersChange(updatedFilters)
  }, [filters, onFiltersChange])

  const clearFilters = React.useCallback(() => {
    setFilters([])
    onFiltersChange([])
  }, [onFiltersChange])

  const getFilterLabel = React.useCallback((type: string): string => {
    return filterTypes.find(t => t.value === type)?.label || type
  }, [filterTypes])

  const getOperatorLabel = React.useCallback((type: string, operator: string): string => {
    return operators[type]?.find(o => o.value === operator)?.label || operator
  }, [operators])

  const renderValueInput = React.useCallback(() => {
    if (!selectedType || !selectedOperator) return null

    if (isDateFilter(selectedType)) {
      return (
        <Popover>
          <PopoverTrigger asChild>
            <Button
              variant="outline"
              className="h-9 w-[180px] justify-start text-left font-normal"
            >
              {selectedDate ? (
                format(selectedDate, "MM/dd/yyyy")
              ) : (
                <span>Pick a date</span>
              )}
              <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0" align="start">
            <Calendar
              mode="single"
              selected={selectedDate}
              onSelect={setSelectedDate}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      )
    }

    if (isSelectFilter(selectedType)) {
      return (
        <Select value={inputValue} onValueChange={setInputValue}>
          <SelectTrigger className="h-9 w-[180px]">
            <SelectValue placeholder="Select value" />
          </SelectTrigger>
          <SelectContent>
            {selectOptions[selectedType]?.map((option) => (
              <SelectItem key={option.value} value={option.value}>
                {option.label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      )
    }

    return (
      <Input
        type={selectedType === 'amount' ? 'number' : 'text'}
        placeholder="Enter value"
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        className="h-9 w-[180px]"
      />
    )
  }, [selectedType, selectedOperator, selectedDate, inputValue, isDateFilter, isSelectFilter, selectOptions])

  const handleReset = React.useCallback(() => {
    setFilters([])
    setSelectedType('')
    setSelectedOperator('')
    setSelectedDate(undefined)
    setInputValue('')
    onFiltersChange([])
  }, [onFiltersChange])

  return (
    <div className="border bg-card p-4 space-y-4 shadow-sm">
      <div className="flex items-center justify-between border-b pb-4">
        <div className="flex items-center gap-3">
          <div className="flex h-8 w-8 items-center justify-center rounded-md bg-black/5">
            <svg
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              xmlns="http://www.w3.org/2000/svg"
              className="text-black"
            >
              <path
                d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              />
            </svg>
          </div>
          <span className="text-lg font-semibold text-foreground">Filters</span>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 px-2 text-sm"
          >
            <ChevronDown
              className={`h-4 w-4 transition-transform ${
                isCollapsed ? 'rotate-180' : ''
              }`}
            />
            {isCollapsed ? 'Expand' : 'Collapse'}
          </Button>
          <Button
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="h-8 px-2 text-sm text-muted-foreground hover:text-foreground"
          >
            <X className="mr-2 h-4 w-4" />
            Reset
          </Button>
          <Button
            size="sm"
            onClick={() => onFiltersChange(filters)}
            className="h-8 bg-[#3C36A9] hover:bg-[#3C36A9]/90"
          >
            Apply Filters
          </Button>
        </div>
      </div>
      {!isCollapsed && (
        <div className="mt-4 space-y-4">
          <div className="flex flex-wrap gap-2">
            {filters.map((filter) => (
              <FilterChip
                key={filter.id}
                label={getFilterLabel(filter.type)}
                operator={getOperatorLabel(filter.type, filter.operator)}
                value={filter.value}
                onRemove={() => removeFilter(filter.id)}
              />
            ))}
            <div className="flex items-center gap-2">
              <Select value={selectedType} onValueChange={setSelectedType}>
                <SelectTrigger className="h-9 w-[200px]">
                  <SelectValue placeholder="Select filter" />
                </SelectTrigger>
                <SelectContent>
                  {filterTypes.map((type) => (
                    <SelectItem key={type.value} value={type.value}>
                      {type.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {selectedType && (
                <Select value={selectedOperator} onValueChange={setSelectedOperator}>
                  <SelectTrigger className="h-9 w-[140px]">
                    <SelectValue placeholder="Operator" />
                  </SelectTrigger>
                  <SelectContent>
                    {operators[selectedType]?.map((op) => (
                      <SelectItem key={op.value} value={op.value}>
                        {op.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}

              {selectedType && selectedOperator && renderValueInput()}

              {selectedType && selectedOperator && (
                <Button 
                  variant="ghost" 
                  size="sm" 
                  className="h-9 px-2"
                  onClick={addFilter}
                >
                  <Plus className="h-4 w-4" />
                  <span className="sr-only">Add filter</span>
                </Button>
              )}
            </div>
          </div>
          <div className="flex gap-2">
            <Button 
              variant="outline" 
              size="sm"
              className="rounded-none"
            >
              Add Filter
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              className="rounded-none"
            >
              Clear All
            </Button>
          </div>
        </div>
      )}
    </div>
  )
} 