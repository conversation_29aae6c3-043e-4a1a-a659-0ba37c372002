"use client"

import { useState } from "react"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { BaseTable } from "@/components/ui/data-table/base-table"
import { ChevronDown, ChevronUp } from "lucide-react"
import { DatePickerWithRange } from "@/components/ui/date-range-picker"
import { DateRange } from "react-day-picker"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { cn } from "@/lib/utils"

interface Transaction {
  id: string
  bookingId: string
  supplierName: string
  bookingDate: string
  amount: number
  status: "completed" | "pending" | "failed"
  paymentMethod: string
  customerName: string
  agentName: string
  commission: number
}

// Sample data
const initialTransactions: Transaction[] = [
  {
    id: "1",
    bookingId: "BK001",
    supplierName: "Delta Airlines",
    bookingDate: "2024-03-15",
    amount: 1250.00,
    status: "completed",
    paymentMethod: "Credit Card",
    customerName: "<PERSON>",
    agentName: "<PERSON>",
    commission: 125.00,
  },
  {
    id: "2",
    bookingId: "BK002",
    supplierName: "Hilton Hotels",
    bookingDate: "2024-03-16",
    amount: 850.00,
    status: "pending",
    paymentMethod: "PayPal",
    customerName: "Jane Smith",
    agentName: "Mike Johnson",
    commission: 85.00,
  },
  {
    id: "3",
    bookingId: "BK003",
    supplierName: "United Airlines",
    bookingDate: "2024-03-14",
    amount: 1500.00,
    status: "completed",
    paymentMethod: "Credit Card",
    customerName: "Robert Brown",
    agentName: "Sarah Smith",
    commission: 150.00,
  },
  {
    id: "4",
    bookingId: "BK004",
    supplierName: "Marriott",
    bookingDate: "2024-03-17",
    amount: 650.00,
    status: "failed",
    paymentMethod: "Debit Card",
    customerName: "Alice Johnson",
    agentName: "Tom Wilson",
    commission: 65.00,
  },
  {
    id: "5",
    bookingId: "BK005",
    supplierName: "American Airlines",
    bookingDate: "2024-03-15",
    amount: 1100.00,
    status: "completed",
    paymentMethod: "Credit Card",
    customerName: "Michael Davis",
    agentName: "Mike Johnson",
    commission: 110.00,
  },
  {
    id: "6",
    bookingId: "BK006",
    supplierName: "Hyatt",
    bookingDate: "2024-03-18",
    amount: 750.00,
    status: "pending",
    paymentMethod: "PayPal",
    customerName: "Emma Wilson",
    agentName: "Sarah Smith",
    commission: 75.00,
  }
]

export function TransactionTable() {
  const [isFiltersVisible, setIsFiltersVisible] = useState(false)
  const [status, setStatus] = useState<string | undefined>()
  const [supplier, setSupplier] = useState<string | undefined>()
  const [dateRange, setDateRange] = useState<DateRange | undefined>()
  const [filteredData, setFilteredData] = useState(initialTransactions)
  const [filter, setFilter] = useState("")

  const handleReset = () => {
    setStatus(undefined)
    setSupplier(undefined)
    setDateRange(undefined)
    setFilteredData(initialTransactions)
    setFilter("")
  }

  const handleFilters = () => {
    let filtered = [...initialTransactions]

    if (filter) {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().includes(filter.toLowerCase())
      )
    }

    if (status && status !== 'all') {
      filtered = filtered.filter(
        item => item.status.toLowerCase() === status
      )
    }

    if (supplier && supplier !== 'all') {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().replace(/\s+/g, '-') === supplier
      )
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.bookingDate)
        return itemDate >= dateRange.from! && itemDate <= dateRange.to!
      })
    }

    setFilteredData(filtered)
  }

  const handleSearchFilter = (value: string) => {
    setFilter(value)
    let filtered = [...initialTransactions]

    if (value) {
      filtered = filtered.filter(
        transaction => transaction.supplierName.toLowerCase().includes(value.toLowerCase())
      )
    }

    // Apply other active filters
    if (status && status !== 'all') {
      filtered = filtered.filter(
        item => item.status.toLowerCase() === status
      )
    }

    if (supplier && supplier !== 'all') {
      filtered = filtered.filter(
        item => item.supplierName.toLowerCase().replace(/\s+/g, '-') === supplier
      )
    }

    if (dateRange?.from && dateRange?.to) {
      filtered = filtered.filter(item => {
        const itemDate = new Date(item.bookingDate)
        return itemDate >= dateRange.from! && itemDate <= dateRange.to!
      })
    }

    setFilteredData(filtered)
  }

  const filterControls = (
    <div className="space-y-4 rounded-md border bg-muted/40 p-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
        <div className="space-y-2">
          <label className="text-sm font-medium">Status</label>
          <Select value={status} onValueChange={setStatus}>
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Status</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Supplier</label>
          <Select value={supplier} onValueChange={setSupplier}>
            <SelectTrigger>
              <SelectValue placeholder="Select supplier" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Suppliers</SelectItem>
              {Array.from(new Set(initialTransactions.map(t => t.supplierName)))
                .sort()
                .map(name => (
                  <SelectItem 
                    key={name} 
                    value={name.toLowerCase().replace(/\s+/g, '-')}
                  >
                    {name}
                  </SelectItem>
                ))
              }
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label className="text-sm font-medium">Date Range</label>
          <DatePickerWithRange
            value={dateRange}
            onChange={setDateRange}
          />
        </div>

        <div className="flex items-end gap-2">
          <Button 
            variant="secondary" 
            onClick={handleReset}
            className="w-full"
          >
            Reset
          </Button>
          <Button 
            onClick={handleFilters}
            className="w-full"
          >
            Apply
          </Button>
        </div>
      </div>
    </div>
  )

  const topContent = (
    <div className="space-y-4">
      <Button
        variant="outline"
        onClick={() => setIsFiltersVisible(!isFiltersVisible)}
        className="w-full justify-between"
      >
        Filters
        {isFiltersVisible ? (
          <ChevronUp className="ml-2 h-4 w-4" />
        ) : (
          <ChevronDown className="ml-2 h-4 w-4" />
        )}
      </Button>
      {isFiltersVisible && filterControls}
    </div>
  )

  const mainColumns = [
    {
      id: "bookingId",
      header: "Booking ID",
      cell: (row: Transaction) => row.bookingId,
      className: "w-[16.66%]",
    },
    {
      id: "supplierName",
      header: "Supplier",
      cell: (row: Transaction) => row.supplierName,
      className: "w-[16.66%]",
    },
    {
      id: "bookingDate",
      header: "Date",
      cell: (row: Transaction) => row.bookingDate,
      className: "w-[16.66%]",
    },
    {
      id: "amount",
      header: "Amount",
      cell: (row: Transaction) => (
        <div className="text-right font-medium">
          ${row.amount.toFixed(2)}
        </div>
      ),
      className: "w-[16.66%]",
    },
    {
      id: "commission",
      header: "Commission",
      cell: (row: Transaction) => (
        <div className="text-right font-medium text-green-600">
          ${row.commission.toFixed(2)}
        </div>
      ),
      className: "w-[16.66%]",
    },
    {
      id: "status",
      header: "Status",
      cell: (row: Transaction) => (
        <Badge
          variant={
            row.status === "completed" 
              ? "secondary"
              : row.status === "pending" 
                ? "outline" 
                : "destructive"
          }
          className={cn(
            "uppercase",
            row.status === "completed" && "bg-green-100 text-green-800 hover:bg-green-100",
            row.status === "pending" && "bg-gray-100 text-gray-800 hover:bg-gray-100"
          )}
        >
          {row.status}
        </Badge>
      ),
      className: "w-[16.66%]",
    },
  ]

  const detailColumns = [
    {
      id: "customerName",
      header: "Customer",
      cell: (row: Transaction) => row.customerName,
    },
    {
      id: "agentName",
      header: "Agent",
      cell: (row: Transaction) => row.agentName,
    },
    {
      id: "paymentMethod",
      header: "Payment Method",
      cell: (row: Transaction) => row.paymentMethod,
    },
  ]

  return (
    <div className="space-y-4">
      {topContent}
      <BaseTable
        data={filteredData}
        mainColumns={mainColumns}
        detailColumns={detailColumns}
        filterPlaceholder="Filter by supplier name..."
        onFilter={handleSearchFilter}
      />
    </div>
  )
} 