"use client"

import { useCallback, useState, useMemo } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { MultiFieldSearch } from "./search/multi-field-search"
import { SEARCH_FIELDS } from "@/app/config/search-fields"
import { SearchCriterion } from "@/app/types/search-criterion"

interface FilterValue {
  operator: string
  value: string | number | boolean
}

interface Filters {
  [key: string]: FilterValue
}

interface SearchPayload {
  page: number
  pageSize: number
  filters: Filters
  bookingId?: string
  pin?: string
  bookedStartDate?: string
  bookedEndDate?: string
  enteredStartDate?: string 
  enteredEndDate?: string
  newEngine?: string
  oldEngine?: string
  bookingType?: string
  nBookingStatusID?: string
  commissionable?: string
  active?: string
  travelerName?: string
  totalChargesStart?: string
  totalChargesEnd?: string
  liftCity?: string
  commissionRateStart?: string
  commissionRateEnd?: string
  destination?: string
  totalCommissionStart?: string
  totalCommissionEnd?: string
  vendor?: string
  itCommissionStart?: string
  itCommissionEnd?: string
  gds?: string
  agentCommissionStart?: string
  agentCommissionEnd?: string
  vendorCheckNum?: string
  vendorPaidStartDate?: string
  vendorPaidEndDate?: string
  itCheck?: string
  agentPaidStartDate?: string
  agentPaidEndDate?: string
  inTheBank?: string
  createdById?: string
  agentPaid?: string
  freeUserPin?: string
  modifiedById?: string
  excludeVendorChecks?: string
  groupId?: string
  referredBy?: string
  tripId?: string
  atolTrips?: boolean
}

export function ClientSearch() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [selectedIds, setSelectedIds] = useState<number[]>([])

  const parseFiltersFromUrl = (): Filters => {
    const filters: Filters = {}
    
    // Get all field keys from SEARCH_FIELDS
    const fieldKeys = SEARCH_FIELDS.map(field => field.key)
    
    // Check each potential filter in the URL
    fieldKeys.forEach(key => {
      const filterStr = searchParams.get(key)
      if (filterStr) {
        try {
          const filter = JSON.parse(filterStr) as FilterValue
          filters[key] = filter
        } catch (e) {
          console.error(`Error parsing filter for ${key}:`, e)
        }
      }
    })

    return filters
  }

  const handleSearch = useCallback((searchPayload: SearchPayload) => {
    const params = new URLSearchParams(searchParams.toString())
    
    // Clear existing search params
    const searchFieldKeys = SEARCH_FIELDS.map(field => field.key)

    Array.from(params.keys()).forEach(key => {
      if (searchFieldKeys.includes(key) || key === 'page' || key === 'pageSize') {
        params.delete(key)
      }
    })

    // Add new search filters
    Object.entries(searchPayload.filters).forEach(([key, value]) => {
      const stringValue = typeof value.value === 'boolean' || typeof value.value === 'number'
        ? JSON.stringify({ operator: value.operator, value: String(value.value) })
        : JSON.stringify(value)
      params.set(key, stringValue)
    })

    // Add pagination params
    params.set('page', searchPayload.page.toString())
    params.set('pageSize', searchPayload.pageSize.toString())

    router.push(`?${params.toString()}`)
  }, [searchParams, router])

  const handleClear = useCallback(() => {
    const params = new URLSearchParams(searchParams.toString())
    
    // Clear search field params and pagination
    const searchFieldKeys = SEARCH_FIELDS.map(field => field.key)
    Array.from(params.keys()).forEach(key => {
      if (searchFieldKeys.includes(key) || key === 'page' || key === 'pageSize') {
        params.delete(key)
      }
    })

    router.push(`?${params.toString()}`)
    setSelectedIds([]) // Clear selection when clearing search
  }, [searchParams, router])

  // Get current values from URL
  const currentPage = Number(searchParams.get('page')) || 1
  const currentPageSize = Number(searchParams.get('pageSize')) || 10
  const currentFilters = parseFiltersFromUrl()

  // Get available fields by filtering out already used fields
  const availableFields = useMemo(() => {
    const currentCriteria = Object.keys(currentFilters)
    return SEARCH_FIELDS.filter(
      field => !currentCriteria.includes(field.key)
    )
  }, [currentFilters])

  const formatFilters = (criteria: SearchCriterion[]) => {
    return criteria.reduce((acc, criterion) => {
      const { field, operator, value } = criterion
      
      if (!field?.key) return acc

      let formattedValue: string | number | boolean = value as string

      switch (field.type) {
        case 'date':
          if (value) {
            const date = new Date(value.toString())
            formattedValue = `${date.toISOString().split('T')[0]} 00:00:00`
          }
          break
        
        case 'number':
          if (field.key.startsWith('n') || field.key === 'iBookingID') {
            formattedValue = Number(value)
          }
          break
        
        case 'select':
          if (field.key.startsWith('b')) {
            formattedValue = value === 'true' ? true : value === 'false' ? false : Number(value)
          }
          break

        case 'boolean':
          formattedValue = value === 'true'
          break
      }

      if (field.key.match(/^(iBookingID|vc|n|d|b)/) && operator?.value) {
        return {
          ...acc,
          [field.key]: {
            operator: operator.value,
            value: formattedValue
          }
        }
      }

      return acc
    }, {} as Filters)
  }

  return (
    <MultiFieldSearch
      onSearch={handleSearch}
      onClear={handleClear}
      page={currentPage}
      pageSize={currentPageSize}
      initialFilters={currentFilters}
      selectedIds={selectedIds}
      onSelectionChange={setSelectedIds}
      availableFields={availableFields}
    />
  )
} 