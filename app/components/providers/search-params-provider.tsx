'use client'

import { useRouter, useSearchParams, usePathname } from 'next/navigation'
import { PropsWithChildren, createContext, useContext } from 'react'

interface SearchParamsContextType {
  searchParams: URLSearchParams
  pathname: string
  updateSearchParams: (params: URLSearchParams) => void
}

const SearchParamsContext = createContext<SearchParamsContextType | null>(null)

export function SearchParamsProvider({ children }: PropsWithChildren) {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const updateSearchParams = (params: URLSearchParams) => {
    router.replace(`${pathname}?${params.toString()}`)
  }

  return (
    <SearchParamsContext.Provider 
      value={{ 
        searchParams: new URLSearchParams(searchParams.toString()),
        pathname,
        updateSearchParams
      }}
    >
      {children}
    </SearchParamsContext.Provider>
  )
}

export function useSearchParamsContext() {
  const context = useContext(SearchParamsContext)
  if (!context) {
    throw new Error('useSearchParamsContext must be used within SearchParamsProvider')
  }
  return context
} 