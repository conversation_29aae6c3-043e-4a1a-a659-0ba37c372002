"use client"

import { <PERSON>actN<PERSON>, useEffect, useRef, useState, useCallback } from "react"
import Image from "next/image"
import Link from "next/link"
import { But<PERSON> } from "@/app/components/ui/button"
import { User, Settings, Home, Menu, ChevronLeft, ChevronRight, PenTool, FileText, Facebook, Instagram, LogOut } from 'lucide-react'
import { useSidebar } from "@/app/contexts/SidebarContext"
import { usePathname, useRouter } from 'next/navigation'
import { cn } from "@/app/lib/utils"

interface SidebarWrapperProps {
  children: ReactNode
}

export default function SidebarWrapper({ children }: SidebarWrapperProps) {
  const { showSidebar, toggleSidebar } = useSidebar()
  const headerRef = useRef<HTMLElement>(null)
  const contentRef = useRef<HTMLDivElement>(null)
  const pathname = usePathname()
  const router = useRouter()

  useEffect(() => {
    const sidebarOpened = localStorage.getItem('sidebarOpened')
    const currentSidebarState = localStorage.getItem('sidebarState')
    
    if (!sidebarOpened) {
      // First time visit
      if (pathname === '/') {
        // Base route, close sidebar
        toggleSidebar(false)
      } else {
        // Non-base route, open sidebar
        toggleSidebar(true)
      }
      localStorage.setItem('sidebarOpened', 'true')
    } else if (currentSidebarState) {
      // Not first time, respect the saved state
      toggleSidebar(currentSidebarState === 'open')
    }
  }, [pathname, toggleSidebar])

  const handleToggleSidebar = useCallback(() => {
    toggleSidebar()
    localStorage.setItem('sidebarState', showSidebar ? 'closed' : 'open')
  }, [toggleSidebar, showSidebar])

  const renderNavLink = (href: string, icon: ReactNode, text: string) => (
    <Link href={href} passHref key={href}>
      <Button
        variant={pathname === href ? 'ghost' : 'ghost'}
        className="w-full justify-start text-[#333]"
      >
        {icon}
        <span className="ml-2">{text}</span>
      </Button>
    </Link>
  )

  const handleLogout = useCallback(() => {
    localStorage.removeItem('access_token')
    router.push('/login')
  }, [router])

  const handleSettingsClick = useCallback(() => {
    router.push('/settings')
  }, [router])

  return (
    <div className="flex flex-col min-h-screen bg-white">
      {/* Header */}
      <header ref={headerRef} className="bg-white shadow-md sticky top-0 z-20 text-[#333]">
        <div className="px-4 py-2 flex items-center justify-between">
          <div className="flex items-center">
            <Button variant="ghost" size="icon" onClick={handleToggleSidebar} className="mr-2 text-[#333]">
              <Menu className="h-6 w-6" />
            </Button>
            <Image 
              src="https://www2.inteletravel.com/hubfs/Style%20Guide/InT_StyleGuide_LogoHeader.svg" 
              alt="InteleTravel Logo" 
              width={200} 
              height={50} 
            />
          </div>
          <div className="flex items-center space-x-2">
           
            <Link href="/profile" passHref>
              <Button variant="ghost" size="icon" className="text-[#333]">
                <User className="h-4 w-4" />
              </Button>
            </Link>
            <Button 
              variant="ghost" 
              size="icon" 
              className="text-[#333]"
              onClick={handleSettingsClick}
            >
              <Settings className="h-4 w-4" />
            </Button>
            <Button variant="ghost" size="icon" className="text-[#333]" onClick={handleLogout}>
              <LogOut className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </header>
 
      {/* Main content area with sidebar */}
      <div ref={contentRef} className="flex flex-grow relative">
        <aside 
          className={`
            absolute left-0 top-0 w-72 bg-white
            transition-transform duration-300 ease-in-out overflow-y-auto 
            ${showSidebar ? 'translate-x-0' : '-translate-x-full'}
            h-full
            [box-shadow:4px_0_10px_rgba(0,0,0,0.05),_0_1px_3px_rgba(0,0,0,0.1)]
            [background-image:linear-gradient(to_left,rgba(0,0,0,0.05),rgba(0,0,0,0.05)_1px,transparent_1px,transparent)]
          `}
        >
         
          <div className="p-4">
            <Button variant="ghost" className="w-full justify-start mb-4 text-[#333]" onClick={handleToggleSidebar}>
              <ChevronLeft className="mr-2 h-4 w-4" /> Hide Sidebar
            </Button>
            <nav className="space-y-2">
            <h5 >Content Concierge</h5>
              {renderNavLink("/", <Home className="h-4 w-4" />, "Home")}
              <h6 >Writing Tools</h6>
              {renderNavLink("/profile-generator", <Home className="h-4 w-4" />, "Bio Profile Generator")}
              {renderNavLink("/writing-tools", <PenTool className="h-4 w-4" />, "Writing Tools")}
              <h6 >Content Tools</h6>
              {renderNavLink("/ai-blog-writer", <FileText className="h-4 w-4" />, "Blog Post Writer")}
              {renderNavLink("/advanced-post-generator", <FileText className="h-4 w-4" />, "Advanced Post Generator")}
              {renderNavLink("/fb-post-writer", <Facebook className="h-4 w-4" />, "FB Post Writer")}
              {renderNavLink("/instagram-captions", <Instagram className="h-4 w-4" />, "Instagram Captions")}
            </nav>
          </div>
        </aside>

        <main className={`flex-grow transition-all duration-300 ease-in-out ${showSidebar ? 'ml-72' : 'ml-0'} p-4 bg-white relative`}>
          {!showSidebar && (
            <Button
              variant="outline"
              size="icon"
              onClick={handleToggleSidebar}
              className="
                absolute left-0 top-0 z-50 
                bg-white text-[#333] hover:bg-gray-100
                transition-all duration-300
                flex items-center justify-center
                w-8 h-8 p-0 shadow-md
              "
            >
              <ChevronRight className="h-5 w-5" />
            </Button>
          )}
          {children}
        </main>
      </div>

      {/* Footer */}
      <footer className="bg-white py-4 text-center text-[#333] border-t border-gray-200">
        <p>&copy; Copyright 2024 InteleTravel.com. All rights reserved. </p>
      </footer>
    </div>
  )
}
