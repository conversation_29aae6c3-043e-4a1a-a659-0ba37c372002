"use client"

import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Search, Download } from "lucide-react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

const checks = [
  { id: 'CK100234', date: '2024-02-01', amount: '$1,234.56' },
  { id: 'CK100235', date: '2024-02-01', amount: '$2,567.89' },
  { id: 'CK100236', date: '2024-02-02', amount: '$3,456.78' },
  { id: 'CK100237', date: '2024-02-02', amount: '$1,987.65' },
  { id: 'CK100238', date: '2024-02-03', amount: '$2,345.67' },
  { id: 'CK100239', date: '2024-02-03', amount: '$4,567.89' },
  { id: 'CK100240', date: '2024-02-04', amount: '$3,234.56' },
]

export function CheckReconciliationTable() {
  return (
    <Card className="rounded-none">
      <CardHeader className="border-b">
        <div className="flex items-center justify-between">
          <CardTitle>Pending Checks</CardTitle>
          <div className="flex gap-2">
            <Button variant="outline" className="rounded-none" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline" className="rounded-none" size="sm">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Check Number</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {checks.map((check) => (
              <TableRow key={check.id}>
                <TableCell>{check.id}</TableCell>
                <TableCell>{check.date}</TableCell>
                <TableCell>{check.amount}</TableCell>
                <TableCell>
                  <Button 
                    variant="link" 
                    className="p-0 h-auto font-normal"
                    onClick={() => {/* TODO: Implement view details */}}
                  >
                    View Details
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  )
} 