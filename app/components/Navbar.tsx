'use client'

import { cn } from '@/lib/utils'
import Link from 'next/link'
import { usePathname } from 'next/navigation'
import { Sheet, <PERSON><PERSON><PERSON>ontent, SheetTrigger } from "@/app/components/ui/sheet"
import { Button } from "@/app/components/ui/button"
import { Menu } from "lucide-react"
import { useSidebar } from '../contexts/SidebarContext'
import { ModeToggle } from "./mode-toggle"
import { UserNav } from "./user-nav"

interface NavbarProps {
  items?: {
    title: string
    href: string
    icon?: any
  }[]
}

export function Navbar({ items }: NavbarProps) {
  const pathname = usePathname()
  const { toggleSidebar } = useSidebar()

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="mr-4 flex">
          <a href="/" className="mr-6 flex items-center space-x-2">
            <span className="font-bold">Travel Dashboard</span>
          </a>
        </div>
        <div className="flex flex-1 items-center justify-between space-x-2 md:justify-end">
          <div className="w-full flex-1 md:w-auto md:flex-none">
            <Button
              variant="ghost"
              size="icon"
              className="mr-4 md:hidden"
              onClick={() => toggleSidebar()}
            >
              <Menu className="h-5 w-5" />
            </Button>
            <Sheet>
              <SheetTrigger asChild>
                <Button
                  variant="ghost"
                  size="icon"
                  className="mr-2 md:hidden"
                >
                  <Menu className="h-6 w-6" />
                  <span className="sr-only">Toggle Menu</span>
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-[80%] max-w-[400px] p-0">
                <nav className="flex flex-col gap-4 p-4">
                  {items?.map((item) => (
                    <Link
                      key={item.href}
                      href={item.href}
                      className={cn(
                        "flex items-center gap-2 text-lg font-medium",
                        pathname === item.href
                          ? "text-primary"
                          : "text-muted-foreground"
                      )}
                    >
                      {item.icon && <item.icon className="h-5 w-5" />}
                      {item.title}
                    </Link>
                  ))}
                </nav>
              </SheetContent>
            </Sheet>
          </div>
          <nav className="flex items-center space-x-2">
            <ModeToggle />
            <UserNav />
          </nav>
        </div>
      </div>
    </header>
  )
}
