"use client"

import { useState, useEffect } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { useMutation, useQuery } from "@apollo/client"
import { GET_BOOKING_BY_ID } from "@/app/graphql/queries/get-booking-by-id"
import { UPDATE_BOOKING } from "@/app/graphql/mutations/update-booking"
import { Button } from "@/app/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Textarea } from "@/app/components/ui/textarea"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/app/components/ui/select"
import { DatePicker } from "@/app/components/ui/date-picker"
import { <PERSON>, Card<PERSON>ontent, CardDescription, Card<PERSON>ooter, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/app/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/app/components/ui/tabs"
import { LoadingSpinner } from "@/app/components/ui/loading-spinner"
import { toast } from "@/app/components/ui/use-toast"
import { Checkbox } from "@/app/components/ui/checkbox"

// Define booking status options
const BOOKING_STATUS = {
  1: { label: 'Booked', value: 1 },
  2: { label: 'Canceled', value: 2 },
  3: { label: 'Pending', value: 3 },
  4: { label: 'Needs Attention', value: 4 }
}

// Define booking type options
const BOOKING_TYPE = {
  1: { label: 'Air', value: 1 },
  2: { label: 'Car', value: 2 },
  3: { label: 'Hotel', value: 3 },
  4: { label: 'Cruise', value: 4 },
  5: { label: 'Vacation', value: 5 },
  6: { label: 'Insurance', value: 6 },
  7: { label: 'Fee', value: 7 },
  8: { label: 'Passport/Visa', value: 8 },
  9: { label: 'Events', value: 9 },
  10: { label: 'Rail', value: 10 },
  11: { label: 'Activities', value: 11 }
}

// Form schema
const bookingFormSchema = z.object({
  vcClientName: z.string().min(1, "Client name is required"),
  vcPIN: z.string().optional(),
  vcConfirmationID: z.string().optional(),
  dTravelStartDate: z.date().optional().nullable(),
  dTravelEndDate: z.date().optional().nullable(),
  vcFinalDestination: z.string().optional(),
  nBookingStatusID: z.number().optional(),
  nBookingTypeID: z.number().optional(),
  nTotalCharges: z.number().optional(),
  totalChargesCurrency: z.string().optional(),
  vcVendor: z.string().optional(),
  vcGDS: z.string().optional(),
  vcVendorAddress: z.string().optional(),
  vcVendorCityState: z.string().optional(),
  vcVendorPostalCode: z.string().optional(),
  vcVendorNumber: z.string().optional(),
  nEngineID: z.number().optional(),
  vcLiftCity: z.string().optional(),
  nNumberOfPsgrs: z.number().optional(),
  dBooked: z.date().optional().nullable(),
  vcTripID: z.string().optional(),
  nSalesContactID: z.number().optional(),
  nCommissionRate: z.number().optional(),
  nAdminFee: z.number().optional(),
  nTotalCommission: z.number().optional(),
  nAgentCommission: z.number().optional(),
  bActive: z.boolean().optional(),
  bNonCommissionable: z.boolean().optional(),
  bOnHold: z.boolean().optional(),
  txtComments: z.string().optional(),
  vcCancelConfirmation: z.string().optional(),
  vcVendorFax: z.string().optional(),
  vcRegBookingID: z.string().optional(),
  vcBookingReference: z.string().optional(),
  vcSupplierReference: z.string().optional(),
  vcItineraryID: z.string().optional(),
  vcRecordLocator: z.string().optional(),
  vcOtherID: z.string().optional(),
  vcGroupID: z.string().optional(),
  vcEventType: z.string().optional(),
  vcShipName: z.string().optional(),
  nConsolidatorID: z.number().optional(),
  vcABTACode: z.string().optional(),
  nAcceptPayment: z.number().optional(),
  nProcessingFee: z.number().optional(),
})

type BookingFormValues = z.infer<typeof bookingFormSchema>

interface BookingEditFormProps {
  bookingId: number
}

function logFormEvent(message: string, data?: any) {
  console.log(`[FORM EVENT] ${message}`, data || '');
}

export function BookingEditForm({ bookingId }: BookingEditFormProps) {
  const router = useRouter()
  const [activeTab, setActiveTab] = useState("general")
  const [updating, setUpdating] = useState(false)
  
  // Fetch booking data
  const { loading, error, data } = useQuery(GET_BOOKING_BY_ID, {
    variables: { bookingId: bookingId },
    fetchPolicy: "network-only"
  })

  // Update booking mutation
  const [updateBooking, { loading: updatingMutation }] = useMutation(UPDATE_BOOKING)

  // Initialize form
  const form = useForm<BookingFormValues>({
    resolver: zodResolver(bookingFormSchema),
    defaultValues: {
      vcClientName: "",
      vcPIN: "",
      vcConfirmationID: "",
      dTravelStartDate: null,
      dTravelEndDate: null,
      vcFinalDestination: "",
      nBookingStatusID: 1,
      nBookingTypeID: 1,
      nTotalCharges: 0,
      totalChargesCurrency: "USD",
      vcVendor: "",
      vcGDS: "",
      vcVendorAddress: "",
      vcVendorCityState: "",
      vcVendorPostalCode: "",
      vcVendorNumber: "",
      nEngineID: 0,
      vcLiftCity: "",
      nNumberOfPsgrs: 1,
      dBooked: null,
      vcTripID: "",
      nSalesContactID: 0,
      nCommissionRate: 0,
      nAdminFee: 0,
      nTotalCommission: 0,
      nAgentCommission: 0,
      bActive: true,
      bNonCommissionable: false,
      bOnHold: false,
      txtComments: "",
      vcCancelConfirmation: "",
      vcVendorFax: "",
      vcRegBookingID: "",
      vcBookingReference: "",
      vcSupplierReference: "",
      vcItineraryID: "",
      vcRecordLocator: "",
      vcOtherID: "",
      vcGroupID: "",
      vcEventType: "",
      vcShipName: "",
      nConsolidatorID: 0,
      vcABTACode: "",
      nAcceptPayment: 0,
      nProcessingFee: 0,
    }
  })

  // Update form values when data is loaded
  useEffect(() => {
    if (data?.getBookingById?.booking) {
      const booking = data.getBookingById.booking
      
      // Parse dates
      const travelStartDate = booking.dTravelStartDate ? new Date(booking.dTravelStartDate) : null
      const travelEndDate = booking.dTravelEndDate ? new Date(booking.dTravelEndDate) : null
      const bookedDate = booking.dBooked ? new Date(Number(booking.dBooked)) : null
      
      form.reset({
        vcClientName: booking.vcClientName || "",
        vcPIN: booking.vcPIN || "",
        vcConfirmationID: booking.vcConfirmationID || "",
        dTravelStartDate: travelStartDate,
        dTravelEndDate: travelEndDate,
        vcFinalDestination: booking.vcFinalDestination || "",
        nBookingStatusID: booking.nBookingStatusID || 1,
        nBookingTypeID: booking.nBookingTypeID || 1,
        nTotalCharges: booking.nTotalCharges || 0,
        totalChargesCurrency: booking.totalChargesCurrency || "USD",
        vcVendor: booking.vcVendor || "",
        vcGDS: booking.vcGDS || "",
        vcVendorAddress: booking.vcVendorAddress || "",
        vcVendorCityState: booking.vcVendorCityState || "",
        vcVendorPostalCode: booking.vcVendorPostalCode || "",
        vcVendorNumber: booking.vcVendorNumber || "",
        nEngineID: booking.nEngineID || 0,
        vcLiftCity: booking.vcLiftCity || "",
        nNumberOfPsgrs: booking.nNumberOfPsgrs || 1,
        dBooked: bookedDate,
        vcTripID: booking.vcTripID || "",
        nSalesContactID: booking.nSalesContactID || 0,
        nCommissionRate: booking.nCommissionRate || 0,
        nAdminFee: booking.nAdminFee || 0,
        nTotalCommission: booking.nTotalCommission || 0,
        nAgentCommission: booking.nAgentCommission || 0,
        bActive: booking.bActive ?? true,
        bNonCommissionable: booking.bNonCommissionable ?? false,
        bOnHold: booking.bOnHold ?? false,
        txtComments: booking.txtComments || "",
        vcCancelConfirmation: booking.vcCancelConfirmation || "",
        vcVendorFax: booking.vcVendorFax || "",
        vcRegBookingID: booking.vcRegBookingID || "",
        vcBookingReference: booking.vcBookingReference || "",
        vcSupplierReference: booking.vcSupplierReference || "",
        vcItineraryID: booking.vcItineraryID || "",
        vcRecordLocator: booking.vcRecordLocator || "",
        vcOtherID: booking.vcOtherID || "",
        vcGroupID: booking.vcGroupID || "",
        vcEventType: booking.vcEventType || "",
        vcShipName: booking.vcShipName || "",
        nConsolidatorID: booking.nConsolidatorID || 0,
        vcABTACode: booking.vcABTACode || "",
        nAcceptPayment: booking.nAcceptPayment || 0,
        nProcessingFee: booking.nProcessingFee || 0,
      })
    }
  }, [data, form])

  // Add a direct submit handler to the form element
  const directSubmit = (e: React.FormEvent) => {
    logFormEvent("Form direct submit event triggered");
    // Don't prevent default - we want the form to submit normally
  };

  // Update the handleSubmit function
  const handleSubmit = async (formData: BookingFormValues) => {
    logFormEvent("Form handleSubmit called with data", formData);
    
    try {
      setUpdating(true);
      
      // Use the exact input structure that works
      const transformedInput = {
        clientName: formData.vcClientName,
        pin: formData.vcPIN,
        confirmationId: formData.vcConfirmationID,
        travelStartDate: formData.dTravelStartDate ? formData.dTravelStartDate.toISOString() : null,
        travelEndDate: formData.dTravelEndDate ? formData.dTravelEndDate.toISOString() : null,
        finalDestination: formData.vcFinalDestination,
        bookingStatusId: formData.nBookingStatusID,
        bookingTypeId: formData.nBookingTypeID,
        totalCharges: formData.nTotalCharges,
        totalChargesCurrency: formData.totalChargesCurrency,
        vendor: formData.vcVendor,
        gds: formData.vcGDS,
        vendorAddress: formData.vcVendorAddress,
        vendorCityState: formData.vcVendorCityState,
        vendorPostalCode: formData.vcVendorPostalCode,
        vendorNumber: formData.vcVendorNumber,
        engineId: formData.nEngineID,
        liftCity: formData.vcLiftCity,
        numberOfPassengers: formData.nNumberOfPsgrs,
        booked: formData.dBooked ? formData.dBooked.getTime().toString() : null,
        vcTripID: formData.vcTripID,
        nSalesContactID: formData.nSalesContactID,
        commissionRate: formData.nCommissionRate,
        adminFee: formData.nAdminFee,
        totalCommission: formData.nTotalCommission,
        agentCommission: formData.nAgentCommission,
        bNonCommissionable: Boolean(formData.bNonCommissionable),
        bOnHold: Boolean(formData.bOnHold),
        comments: formData.txtComments,
        cancelConfirmation: formData.vcCancelConfirmation,
        vendorFax: formData.vcVendorFax,
        nProcessingFee: formData.nProcessingFee
      };
      
      console.log("Submitting booking data:", transformedInput);
      
      const result = await updateBooking({
        variables: {
          bookingId,
          input: transformedInput
        }
      });

      console.log("Update result:", result);

      if (result.data?.updateBooking?.success) {
        toast({
          title: "Success",
          description: "Booking updated successfully",
          variant: "default",
        });
        router.push("/bookings");
      } else {
        toast({
          title: "Error",
          description: result.data?.updateBooking?.message || "Failed to update booking",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating booking:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating the booking",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  // Alternative approach - use a button click handler instead of form submission
  const saveChanges = async () => {
    logFormEvent("Save changes button clicked");
    const formData = form.getValues();
    
    // Ensure boolean fields are actually booleans and remove isActive
    const transformedInput = {
      clientName: formData.vcClientName,
      pin: formData.vcPIN,
      confirmationId: formData.vcConfirmationID,
      travelStartDate: formData.dTravelStartDate ? formData.dTravelStartDate.toISOString() : null,
      travelEndDate: formData.dTravelEndDate ? formData.dTravelEndDate.toISOString() : null,
      finalDestination: formData.vcFinalDestination,
      bookingStatusId: formData.nBookingStatusID,
      bookingTypeId: formData.nBookingTypeID,
      totalCharges: formData.nTotalCharges,
      totalChargesCurrency: formData.totalChargesCurrency,
      vendor: formData.vcVendor,
      gds: formData.vcGDS,
      vendorAddress: formData.vcVendorAddress,
      vendorCityState: formData.vcVendorCityState,
      vendorPostalCode: formData.vcVendorPostalCode,
      vendorNumber: formData.vcVendorNumber,
      engineId: formData.nEngineID,
      liftCity: formData.vcLiftCity,
      numberOfPassengers: formData.nNumberOfPsgrs,
      booked: formData.dBooked ? formData.dBooked.getTime().toString() : null,
      vcTripID: formData.vcTripID,
      nSalesContactID: formData.nSalesContactID,
      commissionRate: formData.nCommissionRate,
      adminFee: formData.nAdminFee,
      totalCommission: formData.nTotalCommission,
      agentCommission: formData.nAgentCommission,
      bNonCommissionable: Boolean(formData.bNonCommissionable),
      bOnHold: Boolean(formData.bOnHold),
      comments: formData.txtComments,
      cancelConfirmation: formData.vcCancelConfirmation,
      vendorFax: formData.vcVendorFax,
      nProcessingFee: formData.nProcessingFee
    };
    
    logFormEvent("Transformed input for API:", transformedInput);
    
    try {
      setUpdating(true);
      
      const result = await updateBooking({
        variables: {
          bookingId,
          input: transformedInput
        }
      });

      logFormEvent("Update result:", result);

      if (result.data?.updateBooking?.success) {
        toast({
          title: "Success",
          description: "Booking updated successfully",
          variant: "default",
        });
        router.push("/bookings");
      } else {
        toast({
          title: "Error",
          description: result.data?.updateBooking?.message || "Failed to update booking",
          variant: "destructive",
        });
      }
    } catch (error) {
      console.error("Error updating booking:", error);
      toast({
        title: "Error",
        description: "An error occurred while updating the booking",
        variant: "destructive",
      });
    } finally {
      setUpdating(false);
    }
  };

  if (loading) {
    return <LoadingSpinner />
  }

  if (error) {
    return <div className="text-red-500">Error loading booking: {error.message}</div>
  }

  return (
    <Form {...form}>
      <form 
        onSubmit={(e) => {
          logFormEvent("Form onSubmit event triggered");
          form.handleSubmit(handleSubmit)(e);
        }}
        className="space-y-6"
        onClick={() => logFormEvent("Form clicked")}
      >
        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 w-full max-w-2xl">
            <TabsTrigger value="general">General Information</TabsTrigger>
            <TabsTrigger value="vendor">Vendor Details</TabsTrigger>
            <TabsTrigger value="financial">Financial Details</TabsTrigger>
            <TabsTrigger value="additional">Additional IDs</TabsTrigger>
          </TabsList>
          
          <TabsContent value="general" className="space-y-6 pt-4">
            <Card className="rounded-none">
              <CardHeader>
                <CardTitle>General Information</CardTitle>
                <CardDescription>
                  Basic booking information and client details
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcClientName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Client Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcPIN"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>PIN</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcConfirmationID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Confirmation ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcTripID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Trip ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="dTravelStartDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Travel Start Date</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="dTravelEndDate"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Travel End Date</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcFinalDestination"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Final Destination</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcLiftCity"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Lift City</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nBookingStatusID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking Status</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select status" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(BOOKING_STATUS).map((status) => (
                              <SelectItem key={status.value} value={status.value.toString()}>
                                {status.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nBookingTypeID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking Type</FormLabel>
                        <Select 
                          onValueChange={(value) => field.onChange(parseInt(value))}
                          defaultValue={field.value?.toString()}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select type" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            {Object.values(BOOKING_TYPE).map((type) => (
                              <SelectItem key={type.value} value={type.value.toString()}>
                                {type.label}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nNumberOfPsgrs"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Number of Passengers</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="dBooked"
                    render={({ field }) => (
                      <FormItem className="flex flex-col">
                        <FormLabel>Booked Date</FormLabel>
                        <DatePicker
                          date={field.value}
                          setDate={field.onChange}
                        />
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bActive"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4">
                        <FormControl>
                          <Checkbox
                            checked={!field.value}
                            onCheckedChange={(checked) => field.onChange(!checked)}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Inactive</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="bOnHold"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>On Hold</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="bNonCommissionable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md p-4">
                        <FormControl>
                          <Checkbox
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                        <div className="space-y-1 leading-none">
                          <FormLabel>Non-Commissionable</FormLabel>
                        </div>
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcCancelConfirmation"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Cancellation Confirmation ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="txtComments"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Comments</FormLabel>
                      <FormControl>
                        <Textarea rows={3} {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="vendor" className="space-y-6 pt-4">
            <Card className="rounded-none">
              <CardHeader>
                <CardTitle>Vendor Details</CardTitle>
                <CardDescription>
                  Information about the vendor and service provider
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcVendor"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vendor Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcGDS"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>GDS</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="vcVendorAddress"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor Address</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcVendorCityState"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>City/State</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcVendorPostalCode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Postal Code</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcVendorNumber"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Vendor Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nEngineID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Engine ID</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <FormField
                  control={form.control}
                  name="nSalesContactID"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Sales Contact ID</FormLabel>
                      <FormControl>
                        <Input 
                          type="number" 
                          {...field}
                          onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
                
                <FormField
                  control={form.control}
                  name="vcVendorFax"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Vendor Fax</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="financial" className="space-y-6 pt-4">
            <Card className="rounded-none">
              <CardHeader>
                <CardTitle>Financial Details</CardTitle>
                <CardDescription>
                  Pricing, commission, and payment information
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nTotalCharges"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total Charges</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="totalChargesCurrency"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Currency</FormLabel>
                        <Select 
                          onValueChange={field.onChange}
                          defaultValue={field.value}
                        >
                          <FormControl>
                            <SelectTrigger>
                              <SelectValue placeholder="Select currency" />
                            </SelectTrigger>
                          </FormControl>
                          <SelectContent>
                            <SelectItem value="USD">USD</SelectItem>
                            <SelectItem value="EUR">EUR</SelectItem>
                            <SelectItem value="GBP">GBP</SelectItem>
                            <SelectItem value="CAD">CAD</SelectItem>
                            <SelectItem value="AUD">AUD</SelectItem>
                          </SelectContent>
                        </Select>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nCommissionRate"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Commission Rate</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormDescription>Enter as decimal (e.g., 0.10 for 10%)</FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nAdminFee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Admin Fee</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nTotalCommission"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Total Commission</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nAgentCommission"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Agent Commission</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
          
          <TabsContent value="additional" className="space-y-6 pt-4">
            <Card className="rounded-none">
              <CardHeader>
                <CardTitle>Additional Identifiers</CardTitle>
                <CardDescription>
                  Reference numbers and additional booking identifiers
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcRegBookingID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking ID (RBS)</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcBookingReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Booking Reference</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcSupplierReference"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Supplier Reference</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcItineraryID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Itinerary ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcRecordLocator"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Record Locator</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcOtherID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Other ID</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcGroupID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Group Number</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="vcEventType"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Event Type</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcShipName"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Ship Name</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nConsolidatorID"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Consolidator ID</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="vcABTACode"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>ABTA Code</FormLabel>
                        <FormControl>
                          <Input {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  
                  <FormField
                    control={form.control}
                    name="nAcceptPayment"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Accept Payment</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            {...field}
                            onChange={(e) => field.onChange(parseInt(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="nProcessingFee"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Processing Fee</FormLabel>
                        <FormControl>
                          <Input 
                            type="number" 
                            step="0.01"
                            {...field}
                            onChange={(e) => field.onChange(parseFloat(e.target.value) || 0)}
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
        
        <div className="flex justify-end gap-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.push("/bookings")}
          >
            Cancel
          </Button>
          <Button 
            type="button"
            disabled={updating}
            onClick={saveChanges}
          >
            {updating ? "Saving..." : "Save Changes"}
          </Button>
        </div>
      </form>
    </Form>
  )
} 