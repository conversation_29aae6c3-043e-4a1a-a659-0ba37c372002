"use client"

import React, { useState, useEffect, useCallback } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, Card<PERSON>ooter, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Textarea } from "@/app/components/ui/textarea"
import { useToast } from "@/app/components/ui/use-toast"
import { CopyIcon, ThumbsDownIcon, ThumbsUpIcon, Loader2 } from "lucide-react"
import { backendAPI } from '@/app/lib/axios-instance'

interface ProofreadItem {
  _id: string
  sessionId: string
  originalText: string
  proofreadText: string
  rating: "like" | "dislike" | null
  createdAt: string
}

export default function Proofreader() {
  const [textToProofread, setTextToProofread] = useState("")
  const [proofreadResult, setProofreadResult] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [proofreadHistory, setProofreadHistory] = useState<ProofreadItem[]>([])
  const [suggestions, setSuggestions] = useState<string>("")

  const { toast } = useToast()

  const loadProofreadHistory = useCallback(async () => {
    const sessionId = ensureSessionId()
    try {
      const response = await backendAPI.get(`/proofreader/${sessionId}`)
      setProofreadHistory(response.data)
    } catch (error) {
      console.error("Error loading proofread history:", error)
      toast({ title: "Error", description: "Failed to load proofread history. Please try again.", variant: "destructive" })
    }
  }, [toast])

  useEffect(() => {
    ensureSessionId()
    loadProofreadHistory()
  }, [loadProofreadHistory])

  const ensureSessionId = () => {
    let sessionId = localStorage.getItem("sessionId")
    if (!sessionId) {
      sessionId = Math.random().toString(36).substring(2, 15)
      localStorage.setItem("sessionId", sessionId)
    }
    return sessionId
  }

  const handleProofread = async () => {
    if (!textToProofread) {
      toast({ title: "Error", description: "Please enter some text to proofread.", variant: "destructive" })
      return
    }

    setIsLoading(true)
    const sessionId = ensureSessionId()
    try {
      const response = await backendAPI.post("/proofreader", { 
        sessionId,
        textToProofread 
      })
      const result = response.data.proofreadText
      setProofreadResult(result)
      toast({ title: "Success", description: "Text proofread successfully!" })
      loadProofreadHistory()
    } catch (error) {
      console.error("Error proofreading text:", error)
      toast({ title: "Error", description: "Failed to proofread text. Please try again.", variant: "destructive" })
    }
    setIsLoading(false)
  }

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text).then(() => {
      toast({ title: "Success", description: "Text copied to clipboard!" })
    })
  }

  const handleRating = async (proofreadId: string, rating: "like" | "dislike") => {
    const sessionId = ensureSessionId()
    try {
      await backendAPI.put(`/proofreader/${proofreadId}/rating`, { rating, sessionId })
      toast({ title: "Success", description: "Rating submitted successfully!" })
      loadProofreadHistory()
    } catch (error) {
      console.error("Error submitting rating:", error)
      toast({ title: "Error", description: "Failed to submit rating. Please try again.", variant: "destructive" })
    }
  }

  const checkGrammar = useCallback(async () => {
    setIsLoading(true)
    try {
      // API call implementation here
      await new Promise(resolve => setTimeout(resolve, 1000)) // Simulated delay
      setSuggestions("No grammar issues found.")
      toast({
        title: "Grammar check complete",
        description: "Your text has been analyzed successfully.",
      })
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to check grammar. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [textToProofread, toast])

  return (
    <div className="space-y-6">
      <Textarea
        value={textToProofread}
        onChange={(e) => setTextToProofread(e.target.value)}
        placeholder="Enter your text here for proofreading..."
        className="min-h-[150px]"
      />

      <Button onClick={handleProofread} disabled={isLoading}>
        {isLoading ? "Proofreading..." : "Proofread"}
      </Button>

      {proofreadResult && (
        <Card>
          <CardHeader>
            <CardTitle>Proofreading Results</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="whitespace-pre-wrap bg-muted p-4 rounded-md mb-4">{proofreadResult}</p>
          </CardContent>
          <CardFooter>
            <Button onClick={() => copyToClipboard(proofreadResult)} variant="outline">
              <CopyIcon className="w-4 h-4 mr-2" />
              Copy Proofread Text
            </Button>
          </CardFooter>
        </Card>
      )}

      <Card>
        <CardHeader>
          <CardTitle>Proofreading History</CardTitle>
        </CardHeader>
        <CardContent>
          {proofreadHistory.length === 0 ? (
            <p className="text-muted-foreground">No proofreading history available.</p>
          ) : (
            <ul className="space-y-6">
              {proofreadHistory.map((item) => (
                <li key={item._id} className="border-b pb-6 last:border-b-0">
                  <h4 className="font-semibold mb-2">Original Text:</h4>
                  <p className="whitespace-pre-wrap bg-muted p-4 rounded-md mb-4">{item.originalText}</p>
                  <h4 className="font-semibold mb-2">Proofread Text:</h4>
                  <p className="whitespace-pre-wrap bg-muted p-4 rounded-md mb-4">{item.proofreadText}</p>
                  <p className="text-sm text-muted-foreground mb-2">Created at: {new Date(item.createdAt).toLocaleString()}</p>
                  <div className="flex items-center space-x-2 mt-2">
                    <Button onClick={() => copyToClipboard(item.proofreadText)} variant="outline" size="sm">
                      <CopyIcon className="w-4 h-4 mr-2" />
                      Copy Proofread Text
                    </Button>
                    <Button
                      onClick={() => handleRating(item._id, "like")}
                      disabled={item.rating === "like"}
                      variant={item.rating === "like" ? "default" : "outline"}
                      size="sm"
                    >
                      <ThumbsUpIcon className="w-4 h-4 mr-2" />
                      Like
                    </Button>
                    <Button
                      onClick={() => handleRating(item._id, "dislike")}
                      disabled={item.rating === "dislike"}
                      variant={item.rating === "dislike" ? "default" : "outline"}
                      size="sm"
                    >
                      <ThumbsDownIcon className="w-4 h-4 mr-2" />
                      Dislike
                    </Button>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </CardContent>
      </Card>

      <Card>
        <CardHeader>
          <CardTitle>Grammar Check</CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            value={textToProofread}
            onChange={(e) => setTextToProofread(e.target.value)}
            placeholder="Enter text to check..."
            className="min-h-[200px]"
          />
        </CardContent>
        <CardFooter className="flex justify-between">
          <Button
            onClick={checkGrammar}
            disabled={isLoading || !textToProofread.trim()}
          >
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            Check Grammar
          </Button>
          {suggestions && (
            <div className="text-sm text-muted-foreground">
              {suggestions}
            </div>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}