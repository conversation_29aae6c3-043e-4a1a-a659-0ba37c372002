"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/app/components/ui/button"
import { formatCurrency } from "@/app/lib/utils"
import Link from "next/link"
import { Card } from "@/app/components/ui/card"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface CheckScan {
  iCheckID: number
  dDeposit: string
  vcChecknum: string
  vcTraveler: string
  nCheckAmt: number
  vcVendor: string
  dUploaded: string
  vcFileName: string
}

interface CheckScansTableProps {
  searchParams: { [key: string]: string }
  checkScans: CheckScan[]
  total: number
}

export function CheckScansTable({ searchParams, checkScans, total }: CheckScansTableProps) {
  const page = searchParams.page ? parseInt(searchParams.page) : 1
  const limit = searchParams.limit ? parseInt(searchParams.limit) : 10
  const totalPages = Math.ceil(total / limit)

  return (
    <Card className="rounded-none shadow-none border">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Check ID</TableHead>
              <TableHead>Deposit Date</TableHead>
              <TableHead>Check Number</TableHead>
              <TableHead>Traveler</TableHead>
              <TableHead>Amount</TableHead>
              <TableHead>Vendor</TableHead>
              <TableHead>Upload Date</TableHead>
              <TableHead>File Name</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {checkScans.length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-8">
                  No check scans found
                </TableCell>
              </TableRow>
            ) : (
              checkScans.map((scan) => (
                <TableRow key={scan.iCheckID}>
                  <TableCell>{scan.iCheckID}</TableCell>
                  <TableCell>{scan.dDeposit}</TableCell>
                  <TableCell>{scan.vcChecknum}</TableCell>
                  <TableCell>{scan.vcTraveler}</TableCell>
                  <TableCell>{formatCurrency(scan.nCheckAmt)}</TableCell>
                  <TableCell>{scan.vcVendor}</TableCell>
                  <TableCell>{scan.dUploaded}</TableCell>
                  <TableCell>{scan.vcFileName}</TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>
      
      {total > 0 && (
        <div className="flex items-center justify-between px-4 py-4 border-t">
          <div className="text-sm text-muted-foreground">
            Showing {checkScans.length} of {total} results
          </div>
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              disabled={page <= 1}
              onClick={() => {
                // Handle previous page
              }}
            >
              <ChevronLeft className="h-4 w-4" />
              Previous
            </Button>
            <Button
              variant="outline"
              size="sm"
              disabled={page >= totalPages}
              onClick={() => {
                // Handle next page
              }}
            >
              Next
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      )}
    </Card>
  )
} 