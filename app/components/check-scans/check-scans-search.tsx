"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function CheckScansSearch() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
      <div className="space-y-2">
        <Label htmlFor="vendor">Vendor</Label>
        <Input id="vendor" name="vendor" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="depositDate">Deposited On</Label>
        <Input id="depositDate" name="depositDate" type="date" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="traveler">Traveler</Label>
        <Input id="traveler" name="traveler" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="uploadDate">Uploaded On</Label>
        <Input id="uploadDate" name="uploadDate" type="date" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="checkNum">Check Number</Label>
        <Input id="checkNum" name="checkNum" className="rounded-none" />
      </div>
      <div className="space-y-2">
        <Label htmlFor="amount">Check Amount</Label>
        <Input id="amount" name="amount" type="number" step="0.01" className="rounded-none" />
      </div>
    </div>
  )
} 