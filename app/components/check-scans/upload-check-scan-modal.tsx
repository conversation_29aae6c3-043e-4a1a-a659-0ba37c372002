"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Footer } from "@/app/components/ui/dialog"
import { Button } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { gql, useMutation } from "@apollo/client"
import { Alert, AlertDescription } from "@/app/components/ui/alert"
import { AlertCircle, Upload, Loader2 } from "lucide-react"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { format } from "date-fns"
import { Calendar } from "@/app/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/app/components/ui/popover"
import { cn } from "@/app/lib/utils"
import { CalendarIcon } from "lucide-react"

// Define the GraphQL mutation
const UPLOAD_CHECK_SCAN = gql`
  mutation UploadCheckScan($input: UploadCheckScanInput!) {
    uploadCheckScan(input: $input) {
      success
      message
      checkScan
      filePath
    }
  }
`

export interface UploadCheckScanModalProps {
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
  vendors?: { id: string; name: string }[]
}

interface FormData {
  depositDate: Date | null
  checkNumber: string
  travelerName: string
  checkAmount: string
  vendorName: string
}

export function UploadCheckScanModal({ 
  isOpen, 
  onClose, 
  onSuccess,
  vendors = [{ id: "DISNEY", name: "DISNEY" }] 
}: UploadCheckScanModalProps) {
  const [file, setFile] = useState<File | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [formData, setFormData] = useState<FormData>({
    depositDate: null,
    checkNumber: "",
    travelerName: "",
    checkAmount: "",
    vendorName: vendors[0]?.name || ""
  })

  // Set up the mutation
  const [uploadCheckScan, { loading }] = useMutation(UPLOAD_CHECK_SCAN, {
    onCompleted: (data) => {
      if (data.uploadCheckScan.success) {
        onSuccess()
        resetForm()
      } else {
        setError(data.uploadCheckScan.message || "Upload failed")
      }
    },
    onError: (error) => {
      setError(error.message || "An error occurred during upload")
    }
  })

  const resetForm = () => {
    setFile(null)
    setError(null)
    setFormData({
      depositDate: null,
      checkNumber: "",
      travelerName: "",
      checkAmount: "",
      vendorName: vendors[0]?.name || ""
    })
  }

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      setFile(e.target.files[0])
      setError(null)
    }
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({ ...prev, [name]: value }))
  }

  const handleVendorChange = (value: string) => {
    setFormData(prev => ({ ...prev, vendorName: value }))
  }

  const handleDateChange = (date: Date | null) => {
    setFormData(prev => ({ ...prev, depositDate: date }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!file) {
      setError("Please select a file to upload")
      return
    }

    if (!formData.depositDate) {
      setError("Please select a deposit date")
      return
    }

    if (!formData.checkNumber || !formData.travelerName || !formData.checkAmount || !formData.vendorName) {
      setError("Please fill in all required fields")
      return
    }

    try {
      // Convert file to base64
      const reader = new FileReader()
      reader.readAsDataURL(file)
      reader.onload = async () => {
        const base64String = reader.result?.toString().split(',')[1] || ''
        
        await uploadCheckScan({
          variables: { 
            input: {
              depositDate: formData.depositDate ? format(formData.depositDate, 'yyyy-MM-dd') : '',
              checkNumber: formData.checkNumber,
              travelerName: formData.travelerName,
              checkAmount: parseFloat(formData.checkAmount),
              vendorName: formData.vendorName,
              file: base64String,
              fileName: file.name,
              fileType: file.type
            }
          }
        })
      }
    } catch (err) {
      // Error is handled in onError callback
    }
  }

  const handleClose = () => {
    resetForm()
    onClose()
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-md">
        <DialogHeader>
          <DialogTitle>Upload Check Scan</DialogTitle>
        </DialogHeader>
        
        <form onSubmit={handleSubmit} className="space-y-4">
          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
          
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="depositDate">Deposit Date *</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !formData.depositDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {formData.depositDate ? format(formData.depositDate, "PPP") : "Select date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0">
                <Calendar
                  mode="single"
                  selected={formData.depositDate || undefined}
                  onSelect={handleDateChange}
                  initialFocus
                  required
                />
              </PopoverContent>
            </Popover>
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="checkNumber">Check Number *</Label>
            <Input
              id="checkNumber"
              name="checkNumber"
              value={formData.checkNumber}
              onChange={handleInputChange}
              placeholder="Enter check number"
              required
            />
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="travelerName">Traveler Name *</Label>
            <Input
              id="travelerName"
              name="travelerName"
              value={formData.travelerName}
              onChange={handleInputChange}
              placeholder="Enter traveler name"
              required
            />
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="checkAmount">Check Amount ($) *</Label>
            <Input
              id="checkAmount"
              name="checkAmount"
              type="number"
              step="0.01"
              min="0"
              value={formData.checkAmount}
              onChange={handleInputChange}
              placeholder="0.00"
              required
            />
          </div>

          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="vendorName">Vendor *</Label>
            <Select value={formData.vendorName} onValueChange={handleVendorChange}>
              <SelectTrigger>
                <SelectValue placeholder="Select vendor" />
              </SelectTrigger>
              <SelectContent>
                {vendors.map(vendor => (
                  <SelectItem key={vendor.id} value={vendor.name}>
                    {vendor.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
          
          <div className="grid w-full items-center gap-1.5">
            <Label htmlFor="check-scan">Check Scan File *</Label>
            <Input
              id="check-scan"
              type="file"
              accept=".pdf,.jpg,.jpeg,.png"
              onChange={handleFileChange}
              className="cursor-pointer"
              required
            />
            <p className="text-sm text-gray-500">
              Accepted formats: PDF, JPG, JPEG, PNG
            </p>
          </div>
          
          {file && (
            <div className="text-sm">
              Selected file: <span className="font-medium">{file.name}</span> ({(file.size / 1024).toFixed(2)} KB)
            </div>
          )}
          
          <DialogFooter className="sm:justify-end">
            <Button
              type="button"
              variant="outline"
              onClick={handleClose}
              disabled={loading}
            >
              Cancel
            </Button>
            <Button 
              type="submit"
              className="bg-[#3C36A9] hover:bg-[#3C36A9]/90 text-white"
              disabled={loading || !file}
            >
              {loading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                <>
                  <Upload className="mr-2 h-4 w-4" />
                  Upload
                </>
              )}
            </Button>
          </DialogFooter>
        </form>
      </DialogContent>
    </Dialog>
  )
} 