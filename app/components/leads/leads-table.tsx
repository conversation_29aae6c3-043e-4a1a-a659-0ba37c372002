'use client'

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table"
import { formatDate } from "@/lib/utils"
import { Lead, LeadFilters } from "@/app/types/leads"
import { useState, useMemo, useEffect } from "react"
import { useR<PERSON>er, useSearchPara<PERSON> } from "next/navigation"
import { 
  ChevronLeft, 
  ChevronRight, 
  ChevronsLeft, 
  ChevronsRight,
  Trash2,
  ChevronDown,
  MoreHorizontal
} from "lucide-react"
import { Checkbox } from "../ui/checkbox"
import { Button } from "../ui/button"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import React from "react"
import { gql, useQuery } from '@apollo/client'
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/app/components/ui/dropdown-menu"
import { Lead as LeadType } from "@/app/types/leads"

interface MetaData {
  meta_key: string
  meta_value: string | null
}

interface ViewedBy {
  viewed_leads_id: string
  agent_id: string
  lead_id: string
}

interface LeadData extends LeadType {
  claimed_agent_id: string | null
  claimed_date: string | null
  successful_booking: boolean
  failed_notes: string | null
  viewed_by: ViewedBy[]
  meta_data: MetaData[]
}

interface LeadsTableProps {
  currentPage: number
  perPage: number
  filters: LeadFilters
}

export function LeadsTable({ currentPage, perPage, filters }: LeadsTableProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { toast } = useToast()

  const [selectedLeads, setSelectedLeads] = useState<Set<string>>(new Set())
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const [totalItems, setTotalItems] = useState(100)

  const formattedDuration = useMemo(() => {
    if (!filters.travel_duration) return "";
    // Remove "days" if it's already in the string to prevent duplication
    const duration = filters.travel_duration.replace(/\s*days\s*$/i, '');
    return `${duration} days`;
  }, [filters.travel_duration]);

  const query = useMemo(() => gql`
    query GetFilteredLeads {
      getFilteredLeads(
        agent_id: "${filters.agent_id || ""}"
        travel_type: "${filters.travel_type || ""}"
        destination: "${filters.destination || ""}"
        departure_state: "${filters.departure_state || ""}"
        departure_city: "${filters.departure_city || ""}"
        travel_duration: "${formattedDuration}"
        claimed: ${Boolean(filters.claimed)}
        start_date: "${filters.start_date || ""}"
        end_date: "${filters.end_date || ""}"
      ) {
        lead_id
        claimed
        claimed_agent_id
        claimed_date
        travel_type 
        successful_booking
        failed_notes
        created_date
        meta_data {
          meta_key
          meta_value
        }
        viewed_by {
          viewed_leads_id
          agent_id
          lead_id
        }
      }
    }
  `, [filters, formattedDuration]);

  const { data, loading: queryLoading, error: queryError, refetch } = useQuery(query, {
    fetchPolicy: 'network-only', // Don't cache results
    nextFetchPolicy: 'network-only'
  });

  // Refetch when filters change
  useEffect(() => {
    refetch();
  }, [filters, refetch]);

  const leads = data?.getFilteredLeads ?? []

  const totalPages = Math.ceil(totalItems / perPage)

  function getMetaValue(lead: LeadData, key: string) {
    return lead.meta_data.find((meta: MetaData) => meta.meta_key === key)?.meta_value || '-'
  }

  // Add selection handlers
  const handleSelectAll = () => {
    if (selectedLeads.size === leads.length) {
      setSelectedLeads(new Set())
    } else {
      setSelectedLeads(new Set(leads.map((lead: LeadData) => lead.lead_id)))
    }
  }

  const handleSelectLead = (leadId: string) => {
    const newSelected = new Set(selectedLeads)
    if (newSelected.has(leadId)) {
      newSelected.delete(leadId)
    } else {
      newSelected.add(leadId)
    }
    setSelectedLeads(newSelected)
  }

  const handleDeleteSelected = async () => {
    try {
      // Mock delete functionality
      toast({
        title: "Success",
        description: `${selectedLeads.size} leads deleted successfully.`,
      })
      setSelectedLeads(new Set()) // Clear selection after delete
      refetch() // Refetch the data after deletion
    } catch (error) {
      toast({
        variant: "destructive",
        title: "Error",
        description: "Failed to delete selected leads.",
      })
    }
  }

  const updatePage = (page: number) => {
    const params = new URLSearchParams(searchParams.toString())
    params.set('page', page.toString())
    router.push(`?${params.toString()}`)
  }

  const toggleRow = (leadId: string) => {
    const newExpanded = new Set(expandedRows)
    if (newExpanded.has(leadId)) {
      newExpanded.delete(leadId)
    } else {
      newExpanded.add(leadId)
    }
    setExpandedRows(newExpanded)
  }

  if (queryLoading) {
    return <div>Loading...</div>
  }

  if (queryError) {
    return <div>Error: {queryError.message}</div>
  }

  return (
    <>
      <div className="space-y-4">
        {selectedLeads.size > 0 && (
          <div className="flex items-center gap-2">
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteSelected}
              className="flex items-center gap-2"
            >
              <Trash2 className="h-4 w-4" />
              Delete Selected ({selectedLeads.size})
            </Button>
          </div>
        )}
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-[30px]">
                  <Checkbox
                    checked={selectedLeads.size === leads.length}
                    onCheckedChange={handleSelectAll}
                    aria-label="Select all"
                  />
                </TableHead>
                <TableHead className="w-[50px]">Expand</TableHead>
                <TableHead className="w-[200px]">Name</TableHead>
                <TableHead className="w-[100px]">Status</TableHead>
                <TableHead className="hidden md:table-cell w-[200px]">Email</TableHead>
                <TableHead className="hidden md:table-cell w-[150px]">Created Date</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[150px]">Travel Type</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[150px]">Destination</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[100px]">Travelers</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[100px]">Children</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[100px]">Duration</TableHead>
                <TableHead className="hidden 2xl:table-cell w-[150px]">Agent</TableHead>
                <TableHead className="w-[60px]">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {leads.map((lead: LeadData) => (
                <React.Fragment key={lead.lead_id}>
                  <TableRow>
                    <TableCell>
                      <Checkbox
                        checked={selectedLeads.has(lead.lead_id)}
                        onCheckedChange={() => handleSelectLead(lead.lead_id)}
                        aria-label={`Select lead ${lead.lead_id}`}
                      />
                    </TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => toggleRow(lead.lead_id)}
                      >
                        <ChevronDown
                          className={cn(
                            "h-4 w-4 transition-transform",
                            expandedRows.has(lead.lead_id) && "rotate-180"
                          )}
                        />
                      </Button>
                    </TableCell>
                    <TableCell>
                      {`${getMetaValue(lead, 'first_name')} ${getMetaValue(lead, 'last_name')}`}
                    </TableCell>
                    <TableCell>
                      <Badge 
                        variant="secondary"
                        className={cn(
                          lead.claimed && "bg-green-100 text-green-800 hover:bg-green-100"
                        )}
                      >
                        {lead.claimed ? "Claimed" : "Unclaimed"}
                      </Badge>
                    </TableCell>
                    <TableCell className="hidden md:table-cell">{getMetaValue(lead, 'email')}</TableCell>
                    <TableCell className="hidden md:table-cell">{formatDate(lead.created_date)}</TableCell>
                    <TableCell className="hidden 2xl:table-cell">{lead.travel_type}</TableCell>
                    <TableCell className="hidden 2xl:table-cell">
                      {getMetaValue(lead, 'vacation_destination')}
                      {getMetaValue(lead, 'destination_elaboration') !== '-' && 
                        ` - ${getMetaValue(lead, 'destination_elaboration')}`
                      }
                    </TableCell>
                    <TableCell className="hidden 2xl:table-cell">{getMetaValue(lead, 'travelers')}</TableCell>
                    <TableCell className="hidden 2xl:table-cell">{getMetaValue(lead, 'children')}</TableCell>
                    <TableCell className="hidden 2xl:table-cell">{getMetaValue(lead, 'travel_duration')}</TableCell>
                    <TableCell className="hidden 2xl:table-cell">{getMetaValue(lead, 'agent_name') || 'N/A'}</TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Edit</DropdownMenuItem>
                          <DropdownMenuItem className="text-red-600">Delete</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                  {expandedRows.has(lead.lead_id) && (
                    <TableRow>
                      <TableCell colSpan={12} className="p-0">
                        <div className="block">
                          <div className="bg-muted/50 p-4 space-y-4">
                            {/* Mobile View (<768px) - Show all except Name, Status, Actions */}
                            <div className="block md:hidden">
                              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Email</span>
                                  <p className="text-sm">{getMetaValue(lead, 'email')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Created Date</span>
                                  <p className="text-sm">{formatDate(lead.created_date)}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Travel Type</span>
                                  <p className="text-sm">{lead.travel_type}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Destination</span>
                                  <p className="text-sm">
                                    {getMetaValue(lead, 'vacation_destination')}
                                    {getMetaValue(lead, 'destination_elaboration') !== '-' && 
                                      ` - ${getMetaValue(lead, 'destination_elaboration')}`
                                    }
                                  </p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Travelers</span>
                                  <p className="text-sm">{getMetaValue(lead, 'travelers')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Children</span>
                                  <p className="text-sm">{getMetaValue(lead, 'children')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Duration</span>
                                  <p className="text-sm">{getMetaValue(lead, 'travel_duration')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Agent</span>
                                  <p className="text-sm">{getMetaValue(lead, 'agent_name') || 'N/A'}</p>
                                </div>
                              </div>
                            </div>

                            {/* Tablet View (768px-1400px) - Show only fields hidden at this breakpoint */}
                            <div className="hidden md:block 2xl:hidden">
                              <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Travel Type</span>
                                  <p className="text-sm">{lead.travel_type}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Destination</span>
                                  <p className="text-sm">
                                    {getMetaValue(lead, 'vacation_destination')}
                                    {getMetaValue(lead, 'destination_elaboration') !== '-' && 
                                      ` - ${getMetaValue(lead, 'destination_elaboration')}`
                                    }
                                  </p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Travelers</span>
                                  <p className="text-sm">{getMetaValue(lead, 'travelers')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Children</span>
                                  <p className="text-sm">{getMetaValue(lead, 'children')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Duration</span>
                                  <p className="text-sm">{getMetaValue(lead, 'travel_duration')}</p>
                                </div>
                                <div className="space-y-1">
                                  <span className="text-sm font-medium text-muted-foreground">Agent</span>
                                  <p className="text-sm">{getMetaValue(lead, 'agent_name') || 'N/A'}</p>
                                </div>
                              </div>
                            </div>

                            {/* Additional metadata section - shown on all screen sizes when expanded */}
                            <div className="border-t border-border pt-4">
                              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
                                {lead.meta_data
                                  .filter((meta: MetaData) => 
                                    !['first_name', 'last_name', 'email', 'travelers', 'children',
                                      'travel_duration', 'agent_name', 'vacation_destination', 
                                      'destination_elaboration', 'hs_context'].includes(meta.meta_key))
                                  .map((meta: MetaData) => (
                                    <div key={meta.meta_key} className="space-y-1">
                                      <span className="text-sm font-medium text-muted-foreground">
                                        {meta.meta_key.split('_').map(word => 
                                          word.charAt(0).toUpperCase() + word.slice(1)
                                        ).join(' ')}
                                      </span>
                                      <p className="text-sm">{meta.meta_value || '-'}</p>
                                    </div>
                                  ))}
                              </div>
                            </div>
                          </div>
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="flex items-center justify-between px-2">
          <div className="text-sm text-muted-foreground">
            Showing {((currentPage - 1) * perPage) + 1} to {Math.min(currentPage * perPage, totalItems)} of {totalItems} entries
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(1)}
              disabled={currentPage === 1}
            >
              <ChevronsLeft className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(currentPage - 1)}
              disabled={currentPage === 1}
            >
              <ChevronLeft className="h-4 w-4" />
            </Button>
            
            <div className="text-sm font-medium">
              Page {currentPage} of {totalPages}
            </div>

            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(currentPage + 1)}
              disabled={currentPage === totalPages}
            >
              <ChevronRight className="h-4 w-4" />
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => updatePage(totalPages)}
              disabled={currentPage === totalPages}
            >
              <ChevronsRight className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>
    </>
  )
} 