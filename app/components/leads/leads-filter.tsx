'use client'

import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Label } from "@/app/components/ui/label"
import { useState, useEffect } from "react"
import { Button } from "@/app/components/ui/button"
import { Filter, X, ChevronDown, ChevronUp } from "lucide-react"
import { Card, CardContent } from "@/app/components/ui/card"

interface FilterState {
  agentId: string
  travelType: string
  destination: string
  departureState: string
  departureCity: string
  duration: string
  claimedStatus: string
  bookingStatus: string
  startDate?: Date | undefined
  endDate?: Date | undefined
}

interface LeadsFilterProps {
  onFilterChange: (filters: FilterState) => void
  onReset: () => void
  initialFilters: any // You'll need to properly type this based on your needs
  defaultFilters: any // You'll need to properly type this based on your needs
}

export function LeadsFilter({
  onFilterChange,
  onReset,
  initialFilters,
  defaultFilters,
}: LeadsFilterProps) {
  const [filters, setFilters] = useState<FilterState>({
    agentId: initialFilters.agent_id || '',
    travelType: initialFilters.travel_type || '',
    destination: initialFilters.destination || '',
    departureState: initialFilters.departure_state || '',
    departureCity: initialFilters.departure_city || '',
    duration: initialFilters.travel_duration || '',
    claimedStatus: initialFilters.claimed ? 'claimed' : 'unclaimed',
    bookingStatus: initialFilters.booking_status || '',
    startDate: initialFilters.start_date ? new Date(initialFilters.start_date) : undefined,
    endDate: initialFilters.end_date ? new Date(initialFilters.end_date) : undefined,
  })
  const [isExpanded, setIsExpanded] = useState(false)

  useEffect(() => {
    onFilterChange(filters)
  }, [filters, onFilterChange])

  const updateFilter = (key: keyof FilterState, value: any) => {
    setFilters(prev => ({ ...prev, [key]: value }))
  }

  const handleReset = () => {
    setFilters(initialFilters)
  }

  const handleApplyFilters = () => {
    onFilterChange(filters)
  }

  return (
    <Card className="mb-6">
      <div className="border-b border-border p-4 flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        <div className="flex items-center gap-2">
          <Filter className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Filters</h2>
        </div>
        <div className="flex flex-wrap items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsExpanded(!isExpanded)}
            className="flex items-center gap-1 w-full sm:w-auto"
          >
            {isExpanded ? (
              <>
                <ChevronUp className="h-4 w-4" />
                <span className="sm:hidden">Hide Filters</span>
                <span className="hidden sm:inline">Collapse</span>
              </>
            ) : (
              <>
                <ChevronDown className="h-4 w-4" />
                <span className="sm:hidden">Show Filters</span>
                <span className="hidden sm:inline">Expand</span>
              </>
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <X className="h-4 w-4" />
            Reset
          </Button>
          <Button
            size="sm"
            onClick={handleApplyFilters}
            className="flex items-center gap-2 w-full sm:w-auto"
          >
            <Filter className="h-4 w-4" />
            Apply Filters
          </Button>
        </div>
      </div>
      {isExpanded && (
        <CardContent className="p-4 border-t">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-x-6 gap-y-4">
            <div className="space-y-2">
              <Label>Agent ID</Label>
              <Input 
                placeholder="675667" 
                type="text"
                value={filters.agentId}
                onChange={(e) => updateFilter('agentId', e.target.value)}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <Label>Travel Type</Label>
              <Select
                value={filters.travelType}
                onValueChange={(value) => updateFilter('travelType', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select travel type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="beach-resort">Beach Resort</SelectItem>
                  <SelectItem value="cruises">Cruises</SelectItem>
                  <SelectItem value="family-travel">Family Travel</SelectItem>
                  <SelectItem value="honeymoons">Honeymoons</SelectItem>
                  <SelectItem value="luxury-resorts">Luxury Resorts</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Destination</Label>
              <Select
                value={filters.destination}
                onValueChange={(value) => updateFilter('destination', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select destination" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="caribbean">Caribbean</SelectItem>
                  <SelectItem value="europe">Europe</SelectItem>
                  <SelectItem value="north-america">North America</SelectItem>
                  <SelectItem value="south-america">South America</SelectItem>
                  <SelectItem value="asia">Asia</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Duration</Label>
              <Select
                value={filters.duration}
                onValueChange={(value) => updateFilter('duration', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select duration" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="1-3">1-3 days</SelectItem>
                  <SelectItem value="4-7">4-7 days</SelectItem>
                  <SelectItem value="8-14">8-14 days</SelectItem>
                  <SelectItem value="15+">15+ days</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Start Date</Label>
              <div className="w-full">
                <DatePicker 
                  date={filters.startDate}
                  setDate={(date: Date | null) => updateFilter('startDate', date || undefined)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>End Date</Label>
              <div className="w-full">
                <DatePicker 
                  date={filters.endDate}
                  setDate={(date: Date | null) => updateFilter('endDate', date || undefined)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label>Claimed Status</Label>
              <Select
                value={filters.claimedStatus}
                onValueChange={(value) => updateFilter('claimedStatus', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="claimed">Claimed</SelectItem>
                  <SelectItem value="unclaimed">Unclaimed</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Booking Status</Label>
              <Select
                value={filters.bookingStatus}
                onValueChange={(value) => updateFilter('bookingStatus', value)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="successful">Successful</SelectItem>
                  <SelectItem value="unsuccessful">Unsuccessful</SelectItem>
                  <SelectItem value="pending">Pending</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
} 