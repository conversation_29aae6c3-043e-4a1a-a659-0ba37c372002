'use client'

import { Cross2Icon } from '@radix-ui/react-icons'
import { Button } from '@/components/ui/button'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'

interface LeadsTableToolbarProps {
  selectedCount: number
  onDelete: () => void
  filterStatus: string
  onFilterChange: (value: string) => void
}

export function LeadsTableToolbar({
  selectedCount,
  onDelete,
  filterStatus,
  onFilterChange
}: LeadsTableToolbarProps) {
  return (
    <div className="flex items-center justify-between">
      <div className="flex flex-1 items-center space-x-2">
        <Select value={filterStatus} onValueChange={onFilterChange}>
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Leads</SelectItem>
            <SelectItem value="claimed">Claimed</SelectItem>
            <SelectItem value="unclaimed">Unclaimed</SelectItem>
          </SelectContent>
        </Select>
        {selectedCount > 0 && (
          <Button
            variant="ghost"
            onClick={onDelete}
            className="h-8 px-2 lg:px-3"
          >
            Delete {selectedCount} selected
            <Cross2Icon className="ml-2 h-4 w-4" />
          </Button>
        )}
      </div>
    </div>
  )
} 