"use client"

import { useState } from "react"
import { useQuery } from "@apollo/client"
import { gql } from "@apollo/client"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { But<PERSON> } from "@/app/components/ui/button"
import { Label } from "@/app/components/ui/label"
import { DatePicker } from "@/app/components/ui/date-picker"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { format } from "date-fns"
import { Search, X, ChevronDown, ChevronUp, Filter } from "lucide-react"
import { Card, CardContent, CardHeader } from "@/components/ui/card"

const TRIAL_AGENT_REPORT = gql`
  query TrialAgentReport($trialPeriodDays: Int) {
    trialAgentReport(params: {
      filters: {},
      trialPeriodDays: $trialPeriodDays
    }) {
      summary {
        totalTrials
        activeTrials
        conversions
        conversionRate
        averageBookingValue
      }
      details {
        agentId
        pin
        name
        trialStart
        status
        bookings
        daysRemaining
        converted
      }
      metrics {
        dailySignups {
          date
          count
        }
        conversionTrend {
          date
          rate
        }
      }
    }
  }
`

interface TrialAgent {
  agentId: string
  pin: string
  name: string
  trialStart: string
  status: string
  bookings: number
  daysRemaining: number
  converted: boolean
}

interface Summary {
  totalTrials: number
  activeTrials: number
  conversions: number
  conversionRate: string
  averageBookingValue: number
}

interface DailySignup {
  date: string
  count: number
}

interface ConversionTrend {
  date: string
  rate: number
}

interface Metrics {
  dailySignups: DailySignup[]
  conversionTrend: ConversionTrend[]
}

interface TrialAgentReport {
  summary: Summary
  details: TrialAgent[]
  metrics: Metrics
}

const statuses = [
  { id: "Active", name: "Active" },
  { id: "Inactive", name: "Inactive" },
]

export function TrialAgentTable() {
  const [selectedStatus, setSelectedStatus] = useState<string>("0")
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [sortBy, setSortBy] = useState<string>("pin")
  const [isFilterExpanded, setIsFilterExpanded] = useState(true)

  const { data, loading, error } = useQuery<{ trialAgentReport: TrialAgentReport }>(TRIAL_AGENT_REPORT, {
    variables: {
      trialPeriodDays: 30
    }
  })

  const handleSearch = () => {
    // Will implement filter logic with GraphQL variables
    console.log({ selectedStatus, startDate, endDate, sortBy })
  }

  const handleClear = () => {
    setSelectedStatus("0")
    setStartDate(undefined)
    setEndDate(undefined)
    setSortBy("pin")
  }

  const handleSort = (column: string) => {
    setSortBy(column)
    // Implement sorting logic
  }

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error loading data</div>

  const { summary, details: agents, metrics } = data?.trialAgentReport || { 
    summary: {
      totalTrials: 0,
      activeTrials: 0,
      conversions: 0,
      conversionRate: '0%',
      averageBookingValue: 0
    },
    details: [],
    metrics: {
      dailySignups: [],
      conversionTrend: []
    }
  }

  return (
    <>
      {/* Add Summary Section */}
      <Card className="rounded-none mb-4">
        <CardHeader className="py-4">
          <h3 className="text-lg font-semibold">Summary</h3>
        </CardHeader>
        <CardContent className="grid grid-cols-5 gap-4">
          <div>
            <p className="text-sm text-muted-foreground">Total Trials</p>
            <p className="text-2xl font-bold">{summary.totalTrials}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Active Trials</p>
            <p className="text-2xl font-bold">{summary.activeTrials}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Conversions</p>
            <p className="text-2xl font-bold">{summary.conversions}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Conversion Rate</p>
            <p className="text-2xl font-bold">{summary.conversionRate}</p>
          </div>
          <div>
            <p className="text-sm text-muted-foreground">Avg Booking Value</p>
            <p className="text-2xl font-bold">${summary.averageBookingValue}</p>
          </div>
        </CardContent>
      </Card>

      {/* Filter Section */}
      <Card className="rounded-none">
        <CardHeader className="border-b flex flex-row items-center justify-between py-3">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <span className="font-semibold">Filters</span>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              className="rounded-none h-8"
              onClick={() => setIsFilterExpanded(!isFilterExpanded)}
            >
              {isFilterExpanded ? (
                <div className="flex items-center gap-2">
                  <span>Collapse</span>
                  <ChevronUp className="h-4 w-4" />
                </div>
              ) : (
                <div className="flex items-center gap-2">
                  <span>Expand</span>
                  <ChevronDown className="h-4 w-4" />
                </div>
              )}
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              className="rounded-none h-8"
              onClick={handleClear}
            >
              Reset
            </Button>
            <Button 
              size="sm"
              className="rounded-none h-8 bg-[#3C36A9] hover:bg-[#3C36A9]/90"
              onClick={handleSearch}
            >
              Apply Filter
            </Button>
          </div>
        </CardHeader>

        {isFilterExpanded && (
          <CardContent className="pt-6">
            <div className="grid grid-cols-3 gap-4">
              <div className="space-y-2">
                <Label>Status</Label>
                <Select value={selectedStatus} onValueChange={setSelectedStatus}>
                  <SelectTrigger className="rounded-none">
                    <SelectValue placeholder="Select status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0">Any</SelectItem>
                    {statuses.map((status) => (
                      <SelectItem key={status.id} value={status.id}>
                        {status.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Enrolled From</Label>
                <DatePicker
                  date={startDate}
                  setDate={(date: Date | null) => setStartDate(date || undefined)}
                />
              </div>

              <div className="space-y-2">
                <Label>Until</Label>
                <DatePicker
                  date={endDate}
                  setDate={(date: Date | null) => setEndDate(date || undefined)}
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {/* Results Table */}
      <Card className="rounded-none">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Agent ID</TableHead>
              <TableHead>PIN</TableHead>
              <TableHead>Name</TableHead>
              <TableHead onClick={() => handleSort('trialStart')} className="cursor-pointer">
                Trial Start ↓
              </TableHead>
              <TableHead onClick={() => handleSort('status')} className="cursor-pointer">
                Status ↓
              </TableHead>
              <TableHead onClick={() => handleSort('bookings')} className="cursor-pointer">
                Bookings ↓
              </TableHead>
              <TableHead onClick={() => handleSort('daysRemaining')} className="cursor-pointer">
                Days Remaining ↓
              </TableHead>
              <TableHead onClick={() => handleSort('converted')} className="cursor-pointer">
                Converted ↓
              </TableHead>
              <TableHead onClick={() => handleSort('keyword')} className="cursor-pointer">
                Keyword ↓
              </TableHead>
              <TableHead onClick={() => handleSort('landingPage')} className="cursor-pointer">
                Landing Page ↓
              </TableHead>
              <TableHead onClick={() => handleSort('campaign')} className="cursor-pointer">
                Campaign ↓
              </TableHead>
              <TableHead onClick={() => handleSort('content')} className="cursor-pointer">
                Content ↓
              </TableHead>
              <TableHead onClick={() => handleSort('source')} className="cursor-pointer">
                Source ↓
              </TableHead>
              <TableHead onClick={() => handleSort('medium')} className="cursor-pointer">
                Medium ↓
              </TableHead>
              <TableHead onClick={() => handleSort('device')} className="cursor-pointer">
                Device ↓
              </TableHead>
              <TableHead onClick={() => handleSort('adPosition')} className="cursor-pointer">
                Ad Position ↓
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {agents.map((agent: TrialAgent) => (
              <TableRow key={agent.agentId}>
                <TableCell>
                  <a href={`/agents/${agent.agentId}`} className="text-primary hover:underline">
                    {agent.agentId || 'NA'}
                  </a>
                </TableCell>
                <TableCell>{agent.pin || 'NA'}</TableCell>
                <TableCell>{agent.name || 'NA'}</TableCell>
                <TableCell>{agent.trialStart ? format(new Date(agent.trialStart), "MM/dd/yyyy") : 'NA'}</TableCell>
                <TableCell>{agent.status || 'NA'}</TableCell>
                <TableCell>{agent.bookings ?? 'NA'}</TableCell>
                <TableCell>{agent.daysRemaining ?? 'NA'}</TableCell>
                <TableCell>{agent.converted ? 'Yes' : 'No'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
                <TableCell>{'NA'}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </>
  )
} 