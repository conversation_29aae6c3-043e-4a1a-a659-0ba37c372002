import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

interface Transaction {
  id: string
  date: string
  amount: number
  type: string
  method: string
  result: string
  admin: string
  comment: string
}

interface TransactionTableProps {
  transactions: Transaction[]
}

export function TransactionTable({ transactions }: TransactionTableProps) {
  return (
    <div>
      <h3 className="font-semibold mb-4">Transaction History:</h3>
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Type</TableHead>
            <TableHead>Method</TableHead>
            <TableHead>Result</TableHead>
            <TableHead>ID</TableHead>
            <TableHead>Admin</TableHead>
            <TableHead>Comment</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions.map((transaction, index) => (
            <TableRow key={transaction.id} className={index % 2 === 1 ? "bg-muted/50" : undefined}>
              <TableCell>{transaction.date}</TableCell>
              <TableCell>${transaction.amount.toFixed(2)}</TableCell>
              <TableCell>{transaction.type}</TableCell>
              <TableCell>{transaction.method}</TableCell>
              <TableCell>{transaction.result}</TableCell>
              <TableCell>
                <a href={`/admin/transactions/${transaction.id}`} className="text-primary hover:underline">
                  {transaction.id}
                </a>
              </TableCell>
              <TableCell>{transaction.admin}</TableCell>
              <TableCell>{transaction.comment}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 