"use client"

import { AgentInfo } from "./agent-info"
import { TransactionTable } from "./transaction-table"

const dummyAgent = {
  fullName: "<PERSON>",
  pin: "A123456",
  created: "01/15/2024"
}

const dummyTransactions = [
  {
    id: "1",
    date: "2024-01-20 14:30:00",
    amount: 39.99,
    type: "Monthly Fee",
    method: "Credit Card",
    result: "Success",
    admin: "Admin1",
    comment: "Regular monthly payment"
  },
  {
    id: "2",
    date: "2024-01-15 09:15:00",
    amount: 199.00,
    type: "Enrollment Fee",
    method: "Credit Card",
    result: "Success",
    admin: "Admin2",
    comment: "Initial enrollment"
  },
  // Add more dummy transactions as needed
]

interface TransactionHistoryProps {
  agentId: string
}

export function TransactionHistory({ agentId }: TransactionHistoryProps) {
  return (
    <div className="space-y-6">
      <AgentInfo agent={dummyAgent} />
      {dummyTransactions.length > 0 ? (
        <TransactionTable transactions={dummyTransactions} />
      ) : (
        <div className="text-muted-foreground">
          No transaction history found
        </div>
      )}
    </div>
  )
} 