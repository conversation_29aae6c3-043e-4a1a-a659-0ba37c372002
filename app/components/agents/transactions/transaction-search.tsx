"use client"

import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { useState } from "react"
import { useRouter } from "next/navigation"

export function TransactionSearch() {
  const [pin, setPin] = useState("")
  const router = useRouter()

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    if (pin) {
      // For demo, just use a dummy ID
      router.push(`/agents/1/transactions`)
    }
  }

  return (
    <form onSubmit={handleSearch} className="space-y-4 mb-8">
      <div className="flex items-center gap-4">
        <div>
          <label htmlFor="pin" className="text-sm text-muted-foreground">
            Search for all Transactions for PIN:
          </label>
          <Input
            id="pin"
            value={pin}
            onChange={(e) => setPin(e.target.value)}
            className="rounded-none mt-1 w-[200px]"
            maxLength={16}
          />
        </div>
        <div className="flex gap-2 mt-6">
          <Button type="submit" className="rounded-none">
            Search
          </Button>
          <Button 
            type="button" 
            variant="outline" 
            className="rounded-none"
            onClick={() => setPin("")}
          >
            Clear
          </Button>
        </div>
      </div>
    </form>
  )
} 