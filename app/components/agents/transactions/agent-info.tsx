interface AgentInfoProps {
  agent: {
    fullName: string
    pin: string
    created: string
  }
}

export function AgentInfo({ agent }: AgentInfoProps) {
  return (
    <div className="grid grid-cols-3 gap-4 text-sm">
      <div>
        <span className="font-medium">Agent: </span>
        {agent.fullName}
      </div>
      <div>
        <span className="font-medium">PIN: </span>
        {agent.pin}
      </div>
      <div>
        <span className="font-medium">Enrollment Date: </span>
        {agent.created}
      </div>
    </div>
  )
} 