"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Edit, Eye, History, MessageSquarePlus, Trash2 } from "lucide-react"
import { Checkbox } from "@/app/components/ui/checkbox"
import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { AgentTableProps } from "@/types/agent"
import { PaginationSection } from "@/app/components/ui/pagination-section"
import { gql, useMutation } from "@apollo/client"
import { toast } from "sonner"

interface AgentTableComponentProps {
  data: AgentTableProps['initialData']
}

const DELETE_AGENT_MUTATION = gql`
  mutation DeleteAgent($agentId: ID!) {
    deleteAgent(agentId: $agentId) {
      success
      message
    }
  }
`

export function AgentTable({ data: initialData }: AgentTableComponentProps) {
  const router = useRouter()
  const [data, setData] = useState(initialData)
  const [selectedAgents, setSelectedAgents] = useState<string[]>([])

  // Update data when initialData changes
  useEffect(() => {
    setData(initialData)
  }, [initialData])

  // Debug logging
  console.log('AgentTable received data:', {
    agentCount: initialData?.agents?.length || 0,
    total: initialData?.total || 0,
    pageInfo: initialData?.pageInfo,
    firstAgentName: initialData?.agents?.[0] ? `${initialData.agents[0].vcFName} ${initialData.agents[0].vcLName}` : 'No agents'
  })

  const isAllSelected = Boolean(data?.agents?.length) && selectedAgents.length === data.agents.length
  const isPartiallySelected = selectedAgents.length > 0 && !isAllSelected

  const toggleAll = () => {
    if (isAllSelected) {
      setSelectedAgents([])
    } else {
      setSelectedAgents(data?.agents.map(agent => agent.iAgentID) ?? [])
    }
  }

  const toggleAgent = (agentId: string) => {
    if (selectedAgents.includes(agentId)) {
      setSelectedAgents(selectedAgents.filter(id => id !== agentId))
    } else {
      setSelectedAgents([...selectedAgents, agentId])
    }
  }

  const handleEdit = (agentId: string) => {
    router.push(`/agents/${agentId}/edit`)
  }

  const handleContact = (agentId: string) => {
    router.push(`/agents/${agentId}/contact`)
  }

  const [deleteAgent, { loading: isDeleting }] = useMutation(DELETE_AGENT_MUTATION)

  const handleDelete = async (agentId: string) => {
    try {
      const response = await deleteAgent({
        variables: { agentId: parseInt(agentId, 10) },
      })

      if (response.data?.deleteAgent?.success) {
        toast.success("Agent deleted successfully")
        
        // Update local state to remove the deleted agent
        setData(prevData => ({
          ...prevData,
          agents: prevData.agents.filter(agent => agent.iAgentID !== agentId),
          total: (prevData.total || 0) - 1
        }))
        
        // Also remove from selected agents if it was selected
        if (selectedAgents.includes(agentId)) {
          setSelectedAgents(selectedAgents.filter(id => id !== agentId))
        }
        
        // Still refresh the router to update URL params if needed
        router.refresh()
      } else {
        toast.error(response.data?.deleteAgent?.message || "Failed to delete agent")
      }
    } catch (error) {
      console.error("Error deleting agent:", error)
      toast.error("An error occurred while deleting the agent")
    }
  }

  function handlePageChange(page: number) {
    router.push(`?page=${page}`)
  }

  return (
    <div className="flex flex-col">
      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[50px]">
                <Checkbox 
                  checked={isAllSelected}
                  className="rounded-none"
                  ref={(input) => {
                    if (input instanceof HTMLInputElement) {
                      input.indeterminate = isPartiallySelected
                    }
                  }}
                  onCheckedChange={toggleAll}
                />
              </TableHead>
              <TableHead>PIN</TableHead>
              <TableHead>Name</TableHead>
              <TableHead>Email</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Group</TableHead>
              <TableHead>Referred By</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Offer</TableHead>
              <TableHead>Landing Page</TableHead>
              <TableHead>Keyword</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data?.agents.map((agent) => (
              <TableRow key={agent.iAgentID}>
                <TableCell>
                  <Checkbox
                    checked={selectedAgents.includes(agent.iAgentID)}
                    onCheckedChange={() => toggleAgent(agent.iAgentID)}
                    className="rounded-none"
                  />
                </TableCell>
                <TableCell>{agent.vcPIN}</TableCell>
                <TableCell>{`${agent.vcFName} ${agent.vcLName}`}</TableCell>
                <TableCell>{agent.vcEmail}</TableCell>
                <TableCell>{agent.status.vcStatus}</TableCell>
                <TableCell>{agent.group}</TableCell>
                <TableCell>{agent.referredBy}</TableCell>
                <TableCell>{agent.status.vcStatus}</TableCell>
                <TableCell>{agent.offer}</TableCell>
                <TableCell>{agent.landingPage}</TableCell>
                <TableCell>{agent.keyword}</TableCell>
                <TableCell>
                  <div className="flex space-x-2">
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="rounded-none"
                      onClick={() => handleEdit(agent.iAgentID)}
                    >
                      <Edit className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="rounded-none"
                      onClick={() => router.push(`/agents/${agent.iAgentID}/transactions`)}
                    >
                      <History className="h-4 w-4" />
                    </Button>
                    <Button 
                      size="icon" 
                      variant="ghost" 
                      className="rounded-none"
                      onClick={() => handleContact(agent.iAgentID)}
                    >
                      <MessageSquarePlus className="h-4 w-4" />
                    </Button>
                    <Button size="icon" variant="ghost" className="rounded-none">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      size="icon"
                      variant="ghost"
                      className="rounded-none text-destructive hover:text-destructive"
                      onClick={() => handleDelete(agent.iAgentID)}
                      disabled={isDeleting}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <div className="border-t p-2">
          <PaginationSection
            currentPage={data.pageInfo.currentPage}
            totalPages={data.pageInfo.totalPages}
            totalItems={data.total || 0}
            pageSize={10}
            onPageChange={handlePageChange}
          />
        </div>
      </div>
    </div>
  )
} 