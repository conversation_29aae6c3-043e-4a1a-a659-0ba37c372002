"use client"

import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { useState } from "react"

interface ContactInfoFormProps {
  formData: any
  updateFormData: (data: any) => void
  onNext: () => void
  onPrevious: () => void
}

export function ContactInfoForm({ 
  formData, 
  updateFormData, 
  onNext, 
  onPrevious 
}: ContactInfoFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    updateFormData({
      address: {
        ...formData.address,
        [name]: value
      }
    })
  }

  const handlePhoneChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    updateFormData({
      phones: {
        ...formData.phones,
        [name]: value
      }
    })
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!formData.address.street) newErrors.street = "Street is required"
    if (!formData.address.city) newErrors.city = "City is required"
    if (!formData.address.state) newErrors.state = "State is required"
    if (!formData.address.zip) newErrors.zip = "ZIP code is required"
    if (!formData.address.country) newErrors.country = "Country is required"
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onNext()
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Address Information</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-2">
            <Label htmlFor="street">Street</Label>
            <Input
              id="street"
              name="street"
              value={formData.address.street}
              onChange={handleAddressChange}
            />
            {errors.street && <p className="text-sm text-red-500">{errors.street}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="street2">Street 2 (Optional)</Label>
            <Input
              id="street2"
              name="street2"
              value={formData.address.street2}
              onChange={handleAddressChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="city">City</Label>
            <Input
              id="city"
              name="city"
              value={formData.address.city}
              onChange={handleAddressChange}
            />
            {errors.city && <p className="text-sm text-red-500">{errors.city}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="state">State</Label>
            <Input
              id="state"
              name="state"
              value={formData.address.state}
              onChange={handleAddressChange}
            />
            {errors.state && <p className="text-sm text-red-500">{errors.state}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="zip">ZIP Code</Label>
            <Input
              id="zip"
              name="zip"
              value={formData.address.zip}
              onChange={handleAddressChange}
            />
            {errors.zip && <p className="text-sm text-red-500">{errors.zip}</p>}
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="country">Country</Label>
            <Input
              id="country"
              name="country"
              value={formData.address.country}
              onChange={handleAddressChange}
            />
            {errors.country && <p className="text-sm text-red-500">{errors.country}</p>}
          </div>
        </div>
      </div>
      
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Phone Numbers</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="space-y-2">
            <Label htmlFor="work">Work Phone</Label>
            <Input
              id="work"
              name="work"
              value={formData.phones.work}
              onChange={handlePhoneChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="home">Home Phone</Label>
            <Input
              id="home"
              name="home"
              value={formData.phones.home}
              onChange={handlePhoneChange}
            />
          </div>
          
          <div className="space-y-2">
            <Label htmlFor="cell">Cell Phone</Label>
            <Input
              id="cell"
              name="cell"
              value={formData.phones.cell}
              onChange={handlePhoneChange}
            />
          </div>
        </div>
      </div>
      
      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          Previous
        </Button>
        <Button type="submit">Next</Button>
      </div>
    </form>
  )
} 