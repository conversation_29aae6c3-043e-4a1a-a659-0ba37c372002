"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent } from "@/app/components/ui/card"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert"
import { <PERSON>ertCircle, CheckCircle2 } from "lucide-react"

interface ReviewSubmitFormProps {
  formData: any
  onPrevious: () => void
}

export function ReviewSubmitForm({ formData, onPrevious }: ReviewSubmitFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleSubmit = async () => {
    setIsSubmitting(true)
    setError(null)
    
    try {
      const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || '/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: `
            mutation CreateAgent($input: CreateAgentInput!) {
              createAgent(input: $input) {
                success
                agentId
                pin
                error
              }
            }
          `,
          variables: {
            input: {
              pin: formData.pin,
              firstName: formData.firstName,
              lastName: formData.lastName,
              email: formData.email,
              company: formData.company,
              address: formData.address,
              phones: formData.phones,
              groupId: formData.groupId,
              agentTypeId: formData.agentTypeId,
              statusId: formData.statusId,
              parentPIN: formData.parentPIN || null,
              mentorPIN: formData.mentorPIN || null,
              referredBy: formData.referredBy || null,
              salesNumber: formData.salesNumber || null
            }
          }
        })
      })
      
      const result = await response.json()
      
      if (result.data?.createAgent?.success) {
        setSuccess(true)
        // Redirect after a short delay
        setTimeout(() => {
          router.push('/agents')
        }, 2000)
      } else {
        setError(result.data?.createAgent?.error || 'Failed to create agent')
      }
    } catch (err) {
      setError('An error occurred while creating the agent')
      console.error(err)
    } finally {
      setIsSubmitting(false)
    }
  }

  if (success) {
    return (
      <Alert className="bg-green-50 border-green-200">
        <CheckCircle2 className="h-5 w-5 text-green-600" />
        <AlertTitle className="text-green-800">Success!</AlertTitle>
        <AlertDescription className="text-green-700">
          Agent has been created successfully. Redirecting to agents list...
        </AlertDescription>
      </Alert>
    )
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">Review Agent Information</h3>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Personal Information</h4>
            <dl className="space-y-2">
              <div className="flex justify-between">
                <dt className="text-gray-500">PIN:</dt>
                <dd>{formData.pin}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Name:</dt>
                <dd>{formData.firstName} {formData.lastName}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Email:</dt>
                <dd>{formData.email}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Company:</dt>
                <dd>{formData.company}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Contact Information</h4>
            <dl className="space-y-2">
              <div>
                <dt className="text-gray-500">Address:</dt>
                <dd className="mt-1">
                  {formData.address.street}<br />
                  {formData.address.street2 && <>{formData.address.street2}<br /></>}
                  {formData.address.city}, {formData.address.state} {formData.address.zip}<br />
                  {formData.address.country}
                </dd>
              </div>
              <div className="pt-2">
                <dt className="text-gray-500">Phone Numbers:</dt>
                <dd className="mt-1">
                  <div className="flex justify-between">
                    <span>Work:</span>
                    <span>{formData.phones.work}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Home:</span>
                    <span>{formData.phones.home}</span>
                  </div>
                  <div className="flex justify-between">
                    <span>Cell:</span>
                    <span>{formData.phones.cell}</span>
                  </div>
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Agent Details</h4>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex justify-between">
                <dt className="text-gray-500">Group ID:</dt>
                <dd>{formData.groupId}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Agent Type ID:</dt>
                <dd>{formData.agentTypeId}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Status ID:</dt>
                <dd>{formData.statusId}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Parent PIN:</dt>
                <dd>{formData.parentPIN || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Mentor PIN:</dt>
                <dd>{formData.mentorPIN || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Referred By:</dt>
                <dd>{formData.referredBy || 'N/A'}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Sales Number:</dt>
                <dd>{formData.salesNumber || 'N/A'}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          Previous
        </Button>
        <Button 
          type="button" 
          onClick={handleSubmit} 
          disabled={isSubmitting}
        >
          {isSubmitting ? 'Creating Agent...' : 'Create Agent'}
        </Button>
      </div>
    </div>
  )
} 