"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { PlusCircle, X, ChevronDown, ChevronUp, Filter } from "lucide-react"
import { useState } from "react"
import { Badge } from "@/app/components/ui/badge"
import { Calendar } from "@/app/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { CalendarIcon } from "lucide-react"
import { useAgentFilters } from "@/app/hooks/use-agent-filters"

const filterFields = [
  { value: "pin", label: "PIN", type: "text" },
  { value: "name", label: "Name", type: "text" },
  { value: "email", label: "Email", type: "text" },
  { value: "phone", label: "Phone", type: "text" },
  { value: "agentType", label: "Agent Type", type: "text" },
  { value: "group", label: "Group", type: "text" },
  { value: "status", label: "Status", type: "text" },
  { value: "billingStatus", label: "Billing Status", type: "text" },
  { value: "country", label: "Country", type: "text" },
  { value: "state", label: "State", type: "text" },
  { value: "city", label: "City", type: "text" },
  { value: "zip", label: "ZIP", type: "text" },
  { value: "createdDate", label: "Created Date", type: "date" },
  { value: "modifiedDate", label: "Modified Date", type: "date" },
] as const

const operators = [
  { value: "equals", label: "Equals" },
  { value: "contains", label: "Contains" },
  { value: "startsWith", label: "Starts With" },
  { value: "endsWith", label: "Ends With" },
  { value: "greaterThan", label: "Greater Than" },
  { value: "lessThan", label: "Less Than" },
] as const

const dateOperators = [
  { value: "equals", label: "Equals" },
  { value: "before", label: "Before" },
  { value: "after", label: "After" },
] as const

export function AgentFilters() {
  const {
    filters,
    currentFilter,
    setCurrentFilter,
    isApplying,
    addFilter,
    removeFilter,
    clearAllFilters,
    applyFilters
  } = useAgentFilters()
  
  const [date, setDate] = useState<Date>()
  const [isCollapsed, setIsCollapsed] = useState(false)

  const selectedField = filterFields.find(f => f.value === currentFilter.field)

  function handleAddFilter() {
    if (currentFilter.field && currentFilter.operator && currentFilter.value) {
      addFilter(currentFilter)
      setCurrentFilter({ field: "", operator: "", value: "", type: "text" })
      setDate(undefined)
    }
  }

  function handleClearAll() {
    clearAllFilters()
    setDate(undefined)
  }

  const getFieldLabel = (fieldValue: string) => {
    return filterFields.find(f => f.value === fieldValue)?.label || fieldValue
  }

  const getOperatorLabel = (operator: string, isDate: boolean = false) => {
    const operatorList = isDate ? dateOperators : operators
    return operatorList.find(o => o.value === operator)?.label || operator
  }

  const isDateField = (fieldValue: string) => {
    return fieldValue === "createdDate" || fieldValue === "modifiedDate"
  }

  return (
    <Card className="rounded-none">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b rounded-none">
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Filter className="h-5 w-5" />
          Advanced Filters
        </CardTitle>
        <div className="flex items-center space-x-2">
          <Button
            variant="ghost"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="rounded-none flex items-center gap-2"
          >
            {isCollapsed ? (
              <>
                <ChevronDown className="h-4 w-4" />
                Expand
              </>
            ) : (
              <>
                <ChevronUp className="h-4 w-4" />
                Collapse
              </>
            )}
          </Button>
          <Button 
            variant="outline" 
            className="rounded-none" 
            onClick={handleClearAll}
            disabled={filters.length === 0}
          >
            Reset
          </Button>
          <Button 
            className="rounded-none" 
            onClick={applyFilters}
            disabled={filters.length === 0 || isApplying}
          >
            {isApplying ? "Applying..." : "Apply Filters"}
          </Button>
        </div>
      </CardHeader>
      <CardContent 
        className={cn(
          "space-y-4 transition-all duration-200 ease-in-out rounded-none",
          isCollapsed ? "h-0 opacity-0 overflow-hidden p-0" : "h-auto opacity-100 pt-6"
        )}
      >
        {/* Filter Builder */}
        <div className="flex items-center space-x-2">
          <Select
            value={currentFilter.field}
            onValueChange={(value) => {
              const field = filterFields.find(f => f.value === value)
              setCurrentFilter({ 
                ...currentFilter, 
                field: value,
                type: field?.type || "text",
                operator: "",
                value: ""
              })
              setDate(undefined)
            }}
          >
            <SelectTrigger className="w-[200px] rounded-none">
              <SelectValue placeholder="Select field" />
            </SelectTrigger>
            <SelectContent className="rounded-none">
              {filterFields.map((field) => (
                <SelectItem key={field.value} value={field.value}>
                  {field.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          <Select
            value={currentFilter.operator}
            onValueChange={(value) =>
              setCurrentFilter({ ...currentFilter, operator: value })
            }
            disabled={!currentFilter.field}
          >
            <SelectTrigger className="w-[200px] rounded-none">
              <SelectValue placeholder="Select operator" />
            </SelectTrigger>
            <SelectContent className="rounded-none">
              {(selectedField?.type === "date" ? dateOperators : operators).map((operator) => (
                <SelectItem key={operator.value} value={operator.value}>
                  {operator.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>

          {selectedField?.type === "date" ? (
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant={"outline"}
                  className={cn(
                    "w-[200px] justify-start text-left font-normal rounded-none",
                    !date && "text-muted-foreground"
                  )}
                  disabled={!currentFilter.operator}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {date ? format(date, "PPP") : <span>Pick a date</span>}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0 rounded-none" align="start">
                <Calendar
                  mode="single"
                  selected={date}
                  onSelect={(newDate) => {
                    setDate(newDate)
                    if (newDate) {
                      setCurrentFilter({ ...currentFilter, value: newDate })
                    }
                  }}
                  initialFocus
                  className="rounded-none"
                />
              </PopoverContent>
            </Popover>
          ) : (
            <Input
              placeholder="Enter value"
              value={currentFilter.value as string}
              onChange={(e) =>
                setCurrentFilter({ ...currentFilter, value: e.target.value })
              }
              className="w-[200px] rounded-none"
              disabled={!currentFilter.operator}
            />
          )}

          <Button 
            onClick={handleAddFilter} 
            size="icon" 
            variant="ghost" 
            className="rounded-none"
            disabled={!currentFilter.field || !currentFilter.operator || !currentFilter.value}
          >
            <PlusCircle className="h-5 w-5" />
          </Button>
        </div>

        {/* Active Filters */}
        {filters.length > 0 && (
          <div className="space-y-2">
            <h4 className="text-sm font-medium text-muted-foreground">Active Filters:</h4>
            <div className="flex flex-wrap gap-2">
              {filters.map((filter) => (
                <Badge
                  key={filter.id}
                  variant="secondary"
                  className="flex items-center gap-2 px-3 py-1 rounded-none"
                >
                  <span>
                    {getFieldLabel(filter.field)}{" "}
                    {getOperatorLabel(filter.operator, isDateField(filter.field))}{" "}
                    {isDateField(filter.field) && filter.value
                      ? format(new Date(filter.value), "PPP")
                      : filter.value}
                  </span>
                  <Button
                    onClick={() => removeFilter(filter.id)}
                    size="icon"
                    variant="ghost"
                    className="h-4 w-4 p-0 hover:bg-transparent rounded-none"
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              ))}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  )
} 