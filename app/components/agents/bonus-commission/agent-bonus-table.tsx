"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { BonusAgent } from "@/app/types/bonus-commission"
import { format } from "date-fns"
import { useEffect, useState } from "react"
import { graphqlClient } from "@/app/lib/graphql-client"

const getBonusAgentsQuery = `
  query GetBonusAgentsWithBookingInfo {
    getBonusAgentsWithBookingInfo {
      success
      count
      message
      agents {
        vcPIN
        status
        vcFName
        vcLName
        bookingDate
        StartBonusDate
        EndBonusDate
      }
    }
  }
`

interface BonusAgentsResponse {
  getBonusAgentsWithBookingInfo: {
    success: boolean
    count: number
    message: string
    agents: BonusAgent[]
  }
}

export function AgentBonusTable() {
  const [agents, setAgents] = useState<BonusAgent[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchAgents() {
      try {
        const response = await graphqlClient.request<BonusAgentsResponse>(getBonusAgentsQuery)
        if (response.getBonusAgentsWithBookingInfo.success) {
          setAgents(response.getBonusAgentsWithBookingInfo.agents)
        } else {
          setError(response.getBonusAgentsWithBookingInfo.message)
        }
      } catch (err) {
        setError('Failed to fetch bonus agents')
        console.error('Error fetching bonus agents:', err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchAgents()
  }, [])

  if (isLoading) {
    return <div className="text-center py-4">Loading...</div>
  }

  if (error) {
    return <div className="text-center text-red-500 py-4">{error}</div>
  }

  const renderTableHeader = () => (
    <TableHeader>
      <TableRow>
        <TableHead>PIN</TableHead>
        <TableHead>Name</TableHead>
        <TableHead>Status</TableHead>
        <TableHead>Booking Date</TableHead>
        <TableHead>Start Date</TableHead>
        <TableHead>End Date</TableHead>
      </TableRow>
    </TableHeader>
  )

  const renderTableBody = () => (
    <TableBody>
      {!agents?.length ? (
        <TableRow>
          <TableCell colSpan={6} className="text-center py-4">
            No bonus agents found
          </TableCell>
        </TableRow>
      ) : (
        agents.map((agent) => (
          <TableRow key={agent.vcPIN}>
            <TableCell>{agent.vcPIN}</TableCell>
            <TableCell>{`${agent.vcFName} ${agent.vcLName}`}</TableCell>
            <TableCell>{agent.status}</TableCell>
            <TableCell>{format(new Date(agent.bookingDate), 'PP')}</TableCell>
            <TableCell>{format(new Date(agent.StartBonusDate), 'PP')}</TableCell>
            <TableCell>{format(new Date(agent.EndBonusDate), 'PP')}</TableCell>
          </TableRow>
        ))
      )}
    </TableBody>
  )

  return (
    <Table>
      {renderTableHeader()}
      {renderTableBody()}
    </Table>
  )
} 