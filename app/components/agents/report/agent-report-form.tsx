"use client"

import { useState, useEffect } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { DatePicker } from "@/components/ui/date-picker"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"

// Dummy data - replace with real data from API
const groups = [
  { id: 1, name: "Group A" },
  { id: 2, name: "Group B" },
]

const affiliates = [
  { id: 1, name: "Affiliate 1" },
  { id: 2, name: "Affiliate 2" },
]

const agentTypes = [
  { id: 1, name: "Standard" },
  { id: 2, name: "Premium" },
]

const statuses = [
  { id: 1, name: "Active" },
  { id: 2, name: "Inactive" },
]

const billingStatuses = [
  { id: 1, name: "Current" },
  { id: 2, name: "Past Due" },
]

const paymentTypes = [
  { id: 1, name: "Credit Card" },
  { id: 2, name: "ACH" },
]

const reportColumns = [
  { id: "pin", name: "PIN" },
  { id: "fullName", name: "Full Name" },
  { id: "email", name: "Email" },
  { id: "phone", name: "Phone" },
  { id: "address", name: "Address" },
  { id: "status", name: "Status" },
  { id: "createdDate", name: "Created Date" },
]

const emailRecipients = [
  { id: "support", email: "<EMAIL>" },
  { id: "admin", email: "<EMAIL>" },
  { id: "sales", email: "<EMAIL>" },
]

// Move these outside the component
const DEFAULT_COLUMN_VALUE = "select_column"
const DEFAULT_EMAIL_VALUE = "select_email"

export function AgentReportForm() {
  const [mounted, setMounted] = useState(false)
  const [formData, setFormData] = useState({
    pin: "",
    lastName: "",
    firstName: "",
    email: "",
    city: "",
    state: "",
    zip: "",
    country: "",
    homePhone: "",
    workPhone: "",
    company: "",
    parentPin: "",
    creditCard: "",
    referredBy: "",
    noPhoto: false,
    noEmail: false,
    primarySort: "",
    secondarySort: "",
    tertiarySort: "",
    includeGraduationDate: false,
    emailTo: "",
    groupId: "1",
    affiliateId: "1",
    agentTypeId: "1",
    statusId: "1",
    billingStatusId: "1",
    paymentTypeId: "1",
  })

  // Initialize with first options
  const [columnSelections, setColumnSelections] = useState<string[]>(
    Array(7).fill(reportColumns[0].id)
  )
  const [selectedEmailId, setSelectedEmailId] = useState(emailRecipients[0].id)

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log(formData)
  }

  const handleClear = () => {
    setFormData({
      pin: "",
      lastName: "",
      firstName: "",
      email: "",
      city: "",
      state: "",
      zip: "",
      country: "",
      homePhone: "",
      workPhone: "",
      company: "",
      parentPin: "",
      creditCard: "",
      referredBy: "",
      noPhoto: false,
      noEmail: false,
      primarySort: "",
      secondarySort: "",
      tertiarySort: "",
      includeGraduationDate: false,
      emailTo: "",
      groupId: "1",
      affiliateId: "1",
      agentTypeId: "1",
      statusId: "1",
      billingStatusId: "1",
      paymentTypeId: "1",
    })
  }

  // Render loading state on server and during hydration
  if (!mounted) {
    return (
      <div className="space-y-6 animate-pulse">
        <div className="h-8 bg-gray-200 rounded w-1/4"></div>
        <div className="grid grid-cols-4 gap-6">
          {Array(8).fill(0).map((_, i) => (
            <div key={i} className="h-10 bg-gray-200 rounded"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-4 gap-6">
        {/* Basic Info Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>PIN</Label>
            <Input 
              value={formData.pin}
              onChange={(e) => setFormData({ ...formData, pin: e.target.value })}
              className="rounded-none"
            />
          </div>
          
          <div className="space-y-2">
            <Label>Last Name</Label>
            <Input 
              value={formData.lastName}
              onChange={(e) => setFormData({ ...formData, lastName: e.target.value })}
              className="rounded-none"
            />
          </div>

          <div className="space-y-2">
            <Label>First Name</Label>
            <Input 
              value={formData.firstName}
              onChange={(e) => setFormData({ ...formData, firstName: e.target.value })}
              className="rounded-none"
            />
          </div>
        </div>

        {/* Contact Info Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Email</Label>
            <Input 
              value={formData.email}
              onChange={(e) => setFormData({ ...formData, email: e.target.value })}
              className="rounded-none"
            />
          </div>

          <div className="space-y-2">
            <Label>Home Phone</Label>
            <Input 
              value={formData.homePhone}
              onChange={(e) => setFormData({ ...formData, homePhone: e.target.value })}
              className="rounded-none"
            />
          </div>

          <div className="space-y-2">
            <Label>Work Phone</Label>
            <Input 
              value={formData.workPhone}
              onChange={(e) => setFormData({ ...formData, workPhone: e.target.value })}
              className="rounded-none"
            />
          </div>
        </div>

        {/* Address Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>City</Label>
            <Input 
              value={formData.city}
              onChange={(e) => setFormData({ ...formData, city: e.target.value })}
              className="rounded-none"
            />
          </div>

          <div className="space-y-2">
            <Label>State</Label>
            <Input 
              value={formData.state}
              onChange={(e) => setFormData({ ...formData, state: e.target.value })}
              className="rounded-none"
              maxLength={2}
            />
          </div>

          <div className="space-y-2">
            <Label>ZIP</Label>
            <Input 
              value={formData.zip}
              onChange={(e) => setFormData({ ...formData, zip: e.target.value })}
              className="rounded-none"
            />
          </div>
        </div>

        {/* Status Section */}
        <div className="space-y-4">
          <div className="space-y-2">
            <Label>Group</Label>
            <Select
              value={formData.groupId}
              onValueChange={(value) => setFormData({ ...formData, groupId: value })}
            >
              <SelectTrigger className="rounded-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {groups.map((group) => (
                  <SelectItem key={group.id} value={group.id.toString()}>
                    {group.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Status</Label>
            <Select
              value={formData.statusId}
              onValueChange={(value) => setFormData({ ...formData, statusId: value })}
            >
              <SelectTrigger className="rounded-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status.id} value={status.id.toString()}>
                    {status.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label>Billing Status</Label>
            <Select
              value={formData.billingStatusId}
              onValueChange={(value) => setFormData({ ...formData, billingStatusId: value })}
            >
              <SelectTrigger className="rounded-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {billingStatuses.map((status) => (
                  <SelectItem key={status.id} value={status.id.toString()}>
                    {status.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <div className="flex items-center gap-4">
        <div className="flex items-center gap-2">
          <Checkbox
            id="noPhoto"
            checked={formData.noPhoto}
            onCheckedChange={(checked) => 
              setFormData({ ...formData, noPhoto: checked as boolean })}
            className="rounded-none"
          />
          <Label htmlFor="noPhoto">No Photo</Label>
        </div>

        <div className="flex items-center gap-2">
          <Checkbox
            id="noEmail"
            checked={formData.noEmail}
            onCheckedChange={(checked) => 
              setFormData({ ...formData, noEmail: checked as boolean })}
            className="rounded-none"
          />
          <Label htmlFor="noEmail">No Email</Label>
        </div>
      </div>

      <div className="flex gap-4">
        <Button type="submit" className="rounded-none">
          Search
        </Button>
        <Button type="button" variant="outline" className="rounded-none" onClick={handleClear}>
          Clear
        </Button>
      </div>

      <Separator />

      {/* Report Configuration Section */}
      <div className="space-y-6">
        <h2 className="text-lg font-semibold">Report Configuration</h2>
        
        <div className="grid grid-cols-2 gap-6">
          <div className="space-y-4">
            <Label>Select Columns</Label>
            <div className="space-y-2">
              {reportColumns.map((_, index) => (
                <div key={index} className="flex items-center gap-4">
                  <Label>Column {index + 1}:</Label>
                  <Select
                    value={columnSelections[index]}
                    onValueChange={(value) => {
                      const newSelections = [...columnSelections]
                      newSelections[index] = value
                      setColumnSelections(newSelections)
                    }}
                  >
                    <SelectTrigger className="rounded-none w-[200px]">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      {reportColumns.map((col) => (
                        <SelectItem key={col.id} value={col.id}>
                          {col.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>
              ))}
            </div>
          </div>

          <div className="space-y-4">
            <Label>Email Report To</Label>
            <Select
              value={selectedEmailId}
              onValueChange={setSelectedEmailId}
            >
              <SelectTrigger className="rounded-none">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {emailRecipients.map((recipient) => (
                  <SelectItem key={recipient.id} value={recipient.id}>
                    {recipient.email}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>

            <Button type="button" className="rounded-none">
              Generate Report
            </Button>
          </div>
        </div>
      </div>
    </form>
  )
} 