"use client"

import { useState, useEffect } from "react"
import { PersonalInfoForm } from "./steps/personal-info-form"
import { ContactInfoForm } from "./steps/contact-info-form"
import { AgentDetailsForm } from "./steps/agent-details-form"
import { ReviewSubmitForm } from "./steps/review-submit-form"
import { Steps, Step } from "@/app/components/ui/steps"
import { AnimatePresence, motion } from "framer-motion"

export interface AgentFormData {
  agentId?: number
  pin?: string
  firstName: string
  lastName: string
  email: string
  company: string
  address: {
    street: string
    street2?: string
    city: string
    state: string
    zip: string
    country: string
  }
  phones: {
    work?: string
    home?: string
    cell?: string
  }
  groupId?: number | null
  agentTypeId?: number | null
  statusId: number | null
  isActive?: boolean
  parentPIN?: string | null
  mentorPIN?: string | null
  referredBy?: string | null
  salesNumber?: string | null
}

interface AgentFormStepperProps {
  initialData?: Partial<AgentFormData>
  isEditMode: boolean
  onSubmitSuccess?: (data: any) => void
}

export function AgentFormStepper({ initialData, isEditMode, onSubmitSuccess }: AgentFormStepperProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [completedSteps, setCompletedSteps] = useState<number[]>([])
  const [formData, setFormData] = useState<AgentFormData>({
    firstName: "",
    lastName: "",
    email: "",
    company: "",
    address: {
      street: "",
      street2: "",
      city: "",
      state: "",
      zip: "",
      country: ""
    },
    phones: {
      work: "",
      home: "",
      cell: ""
    },
    statusId: null,
    isActive: true
  })

  useEffect(() => {
    if (initialData) {
      setFormData(prev => ({
        ...prev,
        ...initialData
      }))
    }
  }, [initialData])

  const steps = [
    { title: "Personal Info", description: "Basic agent details" },
    { title: "Contact Info", description: "Address and phone numbers" },
    { title: "Agent Details", description: "Role and relationships" },
    { title: "Review & Submit", description: isEditMode ? "Confirm and update agent" : "Confirm and create agent" }
  ]

  const handleNext = () => {
    setCompletedSteps((prev) => {
      const newSet = new Set([...prev, currentStep]);
      return Array.from(newSet);
    })
    setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1))
  }

  const handlePrevious = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0))
  }

  const updateFormData = (data: Partial<AgentFormData>) => {
    setFormData((prev) => ({ ...prev, ...data }))
  }

  return (
    <div className="space-y-8">
      <Steps currentStep={currentStep} className="mb-12">
        {steps.map((step, index) => (
          <Step 
            key={index} 
            title={step.title} 
            description={step.description}
            onClick={() => index <= Math.max(...completedSteps, 0) ? setCurrentStep(index) : null}
            completed={completedSteps.includes(index)}
          />
        ))}
      </Steps>

      <AnimatePresence mode="wait">
        <motion.div
          key={currentStep}
          initial={{ opacity: 0, x: 20 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -20 }}
          transition={{ duration: 0.3 }}
          className="mt-8"
        >
          {currentStep === 0 && (
            <PersonalInfoForm 
              formData={formData} 
              updateFormData={updateFormData} 
              onNext={handleNext}
              isEditMode={isEditMode}
            />
          )}
          {currentStep === 1 && (
            <ContactInfoForm 
              formData={formData} 
              updateFormData={updateFormData} 
              onNext={handleNext} 
              onPrevious={handlePrevious} 
            />
          )}
          {currentStep === 2 && (
            <AgentDetailsForm 
              formData={formData} 
              updateFormData={updateFormData} 
              onNext={handleNext} 
              onPrevious={handlePrevious}
              isEditMode={isEditMode} 
            />
          )}
          {currentStep === 3 && (
            <ReviewSubmitForm 
              formData={formData} 
              onPrevious={handlePrevious}
              isEditMode={isEditMode}
              onSubmitSuccess={onSubmitSuccess}
            />
          )}
        </motion.div>
      </AnimatePresence>
    </div>
  )
} 