"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent } from "@/app/components/ui/card"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { Alert, AlertDescription, AlertTitle } from "@/app/components/ui/alert"
import { <PERSON>ertCircle, CheckCircle2 } from "lucide-react"
import { AgentFormData } from "../agent-form-stepper"

interface ReviewSubmitFormProps {
  formData: AgentFormData
  onPrevious: () => void
  isEditMode: boolean
  onSubmitSuccess?: (data: any) => void
}

export function ReviewSubmitForm({ 
  formData, 
  onPrevious, 
  isEditMode,
  onSubmitSuccess 
}: ReviewSubmitFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState(false)

  const handleSubmit = async () => {
    setIsSubmitting(true)
    setError(null)
    
    try {
      const mutation = isEditMode 
        ? `
          mutation UpdateAgent($agentId: Int!) {
            updateAgent(
              agentId: $agentId,
              input: {
                firstName: "${formData.firstName}"
                lastName: "${formData.lastName}"
                email: "${formData.email}"
                company: "${formData.company || ""}"
                address: {
                  street: "${formData.address.street}"
                  city: "${formData.address.city}"
                  state: "${formData.address.state}"
                  zip: "${formData.address.zip}"
                  country: "${formData.address.country}"
                }
                phones: {
                  work: "${formData.phones.work || ""}"
                  cell: "${formData.phones.cell || ""}"
                }
                statusId: ${formData.statusId}
                isActive: ${formData.isActive}
              }
            ) {
              success
              agentId
              pin
              error
            }
          }
        `
        : `
          mutation CreateAgent {
            createAgent(
              input: {
                pin: "${formData.pin}"
                firstName: "${formData.firstName}"
                lastName: "${formData.lastName}"
                email: "${formData.email}"
                company: "${formData.company || ""}"
                address: {
                  street: "${formData.address.street}"
                  city: "${formData.address.city}"
                  state: "${formData.address.state}"
                  zip: "${formData.address.zip}"
                  country: "${formData.address.country}"
                }
                phones: {
                  work: "${formData.phones.work || ""}"
                  cell: "${formData.phones.cell || ""}"
                }
                statusId: ${formData.statusId || null}
                groupId: ${formData.groupId || null}
                agentTypeId: ${formData.agentTypeId || null}
                parentPIN: ${formData.parentPIN ? `"${formData.parentPIN}"` : null}
                mentorPIN: ${formData.mentorPIN ? `"${formData.mentorPIN}"` : null}
                referredBy: ${formData.referredBy ? `"${formData.referredBy}"` : null}
                salesNumber: ${formData.salesNumber ? `"${formData.salesNumber}"` : null}
              }
            ) {
              success
              agentId
              pin
              error
            }
          }
        `;

      const variables = isEditMode
        ? {
            agentId: formData.agentId
          }
        : {};
      
      const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || '/api/graphql', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          query: mutation,
          variables
        })
      });
      
      const result = await response.json();
      
      if (result.errors) {
        const errorMessage = result.errors[0]?.message || 'An error occurred';
        setError(errorMessage);
        return;
      }
      
      if (
        (isEditMode && result.data?.updateAgent?.success) || 
        (!isEditMode && result.data?.createAgent?.success)
      ) {
        setSuccess(true);
        
        if (onSubmitSuccess) {
          onSubmitSuccess(isEditMode ? result.data.updateAgent : result.data.createAgent);
        }
        
        // Redirect after a short delay
        setTimeout(() => {
          router.push('/agents');
        }, 2000);
      } else {
        const errorMessage = isEditMode 
          ? result.data?.updateAgent?.message || 'Failed to update agent'
          : result.data?.createAgent?.error || 'Failed to create agent';
        
        setError(errorMessage);
      }
    } catch (err) {
      console.error('Error submitting form:', err);
      setError(err instanceof Error ? err.message : 'An unknown error occurred');
    } finally {
      setIsSubmitting(false);
    }
  };

  if (success) {
    return (
      <Alert className="bg-green-50 border-green-200">
        <CheckCircle2 className="h-5 w-5 text-green-600" />
        <AlertTitle className="text-green-800">Success!</AlertTitle>
        <AlertDescription className="text-green-700">
          Agent has been {isEditMode ? 'updated' : 'created'} successfully. Redirecting to agents list...
        </AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-6">
      <h3 className="text-lg font-medium">Review Agent Information</h3>
      
      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card>
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Personal Information</h4>
            <dl className="space-y-2">
              {formData.pin && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">PIN:</dt>
                  <dd>{formData.pin}</dd>
                </div>
              )}
              <div className="flex justify-between">
                <dt className="text-gray-500">Name:</dt>
                <dd>{formData.firstName} {formData.lastName}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Email:</dt>
                <dd>{formData.email}</dd>
              </div>
              <div className="flex justify-between">
                <dt className="text-gray-500">Company:</dt>
                <dd>{formData.company}</dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Contact Information</h4>
            <dl className="space-y-2">
              <div>
                <dt className="text-gray-500">Address:</dt>
                <dd className="mt-1">
                  {formData.address.street}<br />
                  {formData.address.street2 && <>{formData.address.street2}<br /></>}
                  {formData.address.city}, {formData.address.state} {formData.address.zip}<br />
                  {formData.address.country}
                </dd>
              </div>
              <div className="pt-2">
                <dt className="text-gray-500">Phone Numbers:</dt>
                <dd className="mt-1">
                  {formData.phones.work && (
                    <div className="flex justify-between">
                      <span>Work:</span>
                      <span>{formData.phones.work}</span>
                    </div>
                  )}
                  {formData.phones.home && (
                    <div className="flex justify-between">
                      <span>Home:</span>
                      <span>{formData.phones.home}</span>
                    </div>
                  )}
                  {formData.phones.cell && (
                    <div className="flex justify-between">
                      <span>Cell:</span>
                      <span>{formData.phones.cell}</span>
                    </div>
                  )}
                </dd>
              </div>
            </dl>
          </CardContent>
        </Card>
        
        <Card className="md:col-span-2">
          <CardContent className="pt-6">
            <h4 className="font-medium mb-4">Agent Details</h4>
            <dl className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {formData.groupId && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Group ID:</dt>
                  <dd>{formData.groupId}</dd>
                </div>
              )}
              {formData.agentTypeId && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Agent Type ID:</dt>
                  <dd>{formData.agentTypeId}</dd>
                </div>
              )}
              {formData.statusId && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Status ID:</dt>
                  <dd>{formData.statusId}</dd>
                </div>
              )}
              {formData.parentPIN && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Parent PIN:</dt>
                  <dd>{formData.parentPIN}</dd>
                </div>
              )}
              {formData.mentorPIN && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Mentor PIN:</dt>
                  <dd>{formData.mentorPIN}</dd>
                </div>
              )}
              {formData.referredBy && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Referred By:</dt>
                  <dd>{formData.referredBy}</dd>
                </div>
              )}
              {formData.salesNumber && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Sales Number:</dt>
                  <dd>{formData.salesNumber}</dd>
                </div>
              )}
              {formData.isActive !== undefined && (
                <div className="flex justify-between">
                  <dt className="text-gray-500">Status:</dt>
                  <dd>{formData.isActive ? 'Active' : 'Inactive'}</dd>
                </div>
              )}
            </dl>
          </CardContent>
        </Card>
      </div>
      
      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          Previous
        </Button>
        <Button 
          type="button" 
          onClick={handleSubmit} 
          disabled={isSubmitting}
        >
          {isSubmitting ? 
            (isEditMode ? 'Updating Agent...' : 'Creating Agent...') : 
            (isEditMode ? 'Update Agent' : 'Create Agent')
          }
        </Button>
      </div>
    </div>
  )
} 