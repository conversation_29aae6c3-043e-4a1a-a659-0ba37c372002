"use client"

import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { useState } from "react"
import { AgentFormData } from "../agent-form-stepper"

interface PersonalInfoFormProps {
  formData: AgentFormData
  updateFormData: (data: Partial<AgentFormData>) => void
  onNext: () => void
  isEditMode: boolean
}

export function PersonalInfoForm({ formData, updateFormData, onNext, isEditMode }: PersonalInfoFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!isEditMode && !formData.pin) newErrors.pin = "PIN is required"
    if (!formData.firstName) newErrors.firstName = "First name is required"
    if (!formData.lastName) newErrors.lastName = "Last name is required"
    if (!formData.email) newErrors.email = "Email is required"
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onNext()
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {!isEditMode && (
          <div className="space-y-2">
            <Label htmlFor="pin">Agent PIN</Label>
            <Input
              id="pin"
              name="pin"
              value={formData.pin || ""}
              onChange={handleChange}
              placeholder="AG12345"
            />
            {errors.pin && <p className="text-sm text-red-500">{errors.pin}</p>}
          </div>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="firstName">First Name</Label>
          <Input
            id="firstName"
            name="firstName"
            value={formData.firstName}
            onChange={handleChange}
          />
          {errors.firstName && <p className="text-sm text-red-500">{errors.firstName}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="lastName">Last Name</Label>
          <Input
            id="lastName"
            name="lastName"
            value={formData.lastName}
            onChange={handleChange}
          />
          {errors.lastName && <p className="text-sm text-red-500">{errors.lastName}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input
            id="email"
            name="email"
            type="email"
            value={formData.email}
            onChange={handleChange}
          />
          {errors.email && <p className="text-sm text-red-500">{errors.email}</p>}
        </div>
        
        <div className="space-y-2">
          <Label htmlFor="company">Company</Label>
          <Input
            id="company"
            name="company"
            value={formData.company}
            onChange={handleChange}
          />
        </div>
      </div>
      
      <div className="flex justify-end">
        <Button type="submit">Next</Button>
      </div>
    </form>
  )
} 