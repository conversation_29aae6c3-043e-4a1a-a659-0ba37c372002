"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Switch } from "@/app/components/ui/switch"
import { useState, useEffect } from "react"
import { AgentFormData } from "../agent-form-stepper"

interface AgentDetailsFormProps {
  formData: AgentFormData
  updateFormData: (data: Partial<AgentFormData>) => void
  onNext: () => void
  onPrevious: () => void
  isEditMode: boolean
}

export function AgentDetailsForm({ 
  formData, 
  updateFormData, 
  onNext, 
  onPrevious,
  isEditMode
}: AgentDetailsFormProps) {
  const [errors, setErrors] = useState<Record<string, string>>({})
  const [groups, setGroups] = useState<{id: number, name: string}[]>([])
  const [agentTypes, setAgentTypes] = useState<{id: number, name: string}[]>([])
  const [statuses, setStatuses] = useState<{id: number, name: string}[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    async function fetchReferenceData() {
      try {
        // This would be replaced with actual API calls to get the reference data
        // For now, we'll use mock data
        setGroups([
          { id: 1, name: "Group 1" },
          { id: 2, name: "Group 2" },
          { id: 3, name: "Group 3" }
        ])
        
        setAgentTypes([
          { id: 1, name: "Agent Type 1" },
          { id: 2, name: "Agent Type 2" },
          { id: 3, name: "Agent Type 3" }
        ])
        
        setStatuses([
          { id: 1, name: "Active" },
          { id: 2, name: "Inactive" },
          { id: 3, name: "Pending" }
        ])
      } catch (err) {
        console.error("Failed to load reference data", err)
      } finally {
        setLoading(false)
      }
    }
    
    fetchReferenceData()
  }, [])

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    updateFormData({ [name]: value })
  }

  const handleSelectChange = (name: string, value: string) => {
    updateFormData({ [name]: parseInt(value) })
  }

  const handleSwitchChange = (checked: boolean) => {
    updateFormData({ isActive: checked })
  }

  const validateForm = () => {
    const newErrors: Record<string, string> = {}
    
    if (!isEditMode && !formData.groupId) newErrors.groupId = "Group is required"
    if (!isEditMode && !formData.agentTypeId) newErrors.agentTypeId = "Agent type is required"
    if (!formData.statusId) newErrors.statusId = "Status is required"
    
    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (validateForm()) {
      onNext()
    }
  }

  if (loading) {
    return <div>Loading...</div>
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {!isEditMode && (
          <>
            <div className="space-y-2">
              <Label htmlFor="groupId">Group</Label>
              <Select 
                value={formData.groupId?.toString() || ""} 
                onValueChange={(value) => handleSelectChange("groupId", value)}
              >
                <SelectTrigger id="groupId">
                  <SelectValue placeholder="Select a group" />
                </SelectTrigger>
                <SelectContent>
                  {groups.map(group => (
                    <SelectItem key={group.id} value={group.id.toString()}>
                      {group.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.groupId && <p className="text-sm text-red-500">{errors.groupId}</p>}
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="agentTypeId">Agent Type</Label>
              <Select 
                value={formData.agentTypeId?.toString() || ""} 
                onValueChange={(value) => handleSelectChange("agentTypeId", value)}
              >
                <SelectTrigger id="agentTypeId">
                  <SelectValue placeholder="Select an agent type" />
                </SelectTrigger>
                <SelectContent>
                  {agentTypes.map(type => (
                    <SelectItem key={type.id} value={type.id.toString()}>
                      {type.name}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              {errors.agentTypeId && <p className="text-sm text-red-500">{errors.agentTypeId}</p>}
            </div>
          </>
        )}
        
        <div className="space-y-2">
          <Label htmlFor="statusId">Status</Label>
          <Select 
            value={formData.statusId?.toString() || ""} 
            onValueChange={(value) => handleSelectChange("statusId", value)}
          >
            <SelectTrigger id="statusId">
              <SelectValue placeholder="Select a status" />
            </SelectTrigger>
            <SelectContent>
              {statuses.map(status => (
                <SelectItem key={status.id} value={status.id.toString()}>
                  {status.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.statusId && <p className="text-sm text-red-500">{errors.statusId}</p>}
        </div>
        
        {isEditMode && (
          <div className="space-y-2 flex items-center">
            <div className="flex-1">
              <Label htmlFor="isActive">Active</Label>
            </div>
            <Switch 
              id="isActive"
              checked={formData.isActive || false}
              onCheckedChange={handleSwitchChange}
            />
          </div>
        )}
        
        {!isEditMode && (
          <>
            <div className="space-y-2">
              <Label htmlFor="parentPIN">Parent PIN</Label>
              <Input
                id="parentPIN"
                name="parentPIN"
                value={formData.parentPIN || ""}
                onChange={handleChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="mentorPIN">Mentor PIN</Label>
              <Input
                id="mentorPIN"
                name="mentorPIN"
                value={formData.mentorPIN || ""}
                onChange={handleChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="referredBy">Referred By</Label>
              <Input
                id="referredBy"
                name="referredBy"
                value={formData.referredBy || ""}
                onChange={handleChange}
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="salesNumber">Sales Number</Label>
              <Input
                id="salesNumber"
                name="salesNumber"
                value={formData.salesNumber || ""}
                onChange={handleChange}
              />
            </div>
          </>
        )}
      </div>
      
      <div className="flex justify-between">
        <Button type="button" variant="outline" onClick={onPrevious}>
          Previous
        </Button>
        <Button type="submit">Next</Button>
      </div>
    </form>
  )
} 