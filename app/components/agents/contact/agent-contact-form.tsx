"use client"

import { useState } from "react"
import { Input } from "@/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { Button } from "@/app/components/ui/button"
import { Textarea } from "@/app/components/ui/textarea"
import { Checkbox } from "@/app/components/ui/checkbox"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { CalendarIcon } from "lucide-react"

const contactTypes = ["Phone", "Email", "Fax", "Mail", "Chat"]

const categoryGroups = [
  {
    id: 1,
    name: "Sales",
    categories: [
      { id: 1, name: "New Sale" },
      { id: 2, name: "Follow Up" },
      { id: 3, name: "Cancellation" },
    ]
  },
  {
    id: 2,
    name: "Support",
    categories: [
      { id: 4, name: "Technical Issue" },
      { id: 5, name: "Account Access" },
      { id: 6, name: "General Question" },
    ]
  },
  // Add more groups as needed
]

interface AgentContactFormProps {
  agentId?: string
}

export function AgentContactForm({ agentId }: AgentContactFormProps) {
  const [pin, setPin] = useState("")
  const [contactType, setContactType] = useState<string>("")
  const [category, setCategory] = useState<string>("")
  const [needsAttention, setNeedsAttention] = useState(false)
  const [criticalDate, setCriticalDate] = useState<Date>()
  const [contactDate, setContactDate] = useState<Date>()
  const [notes, setNotes] = useState("")

  // Dummy agent data - replace with real data fetch
  const agentInfo = agentId ? {
    pin: "A123456",
    firstName: "John",
    lastName: "Smith"
  } : null

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
    console.log({
      pin: agentInfo?.pin || pin,
      contactType,
      category,
      needsAttention,
      criticalDate,
      contactDate,
      notes
    })
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-2 gap-6">
        <div className="space-y-2">
          <Label htmlFor="pin">Agent PIN</Label>
          {agentInfo ? (
            <div className="text-sm text-muted-foreground">{agentInfo.pin}</div>
          ) : (
            <Input
              id="pin"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              className="rounded-none"
              maxLength={16}
            />
          )}
        </div>

        {agentInfo && (
          <div className="space-y-2">
            <Label>Agent Name</Label>
            <div className="text-sm text-muted-foreground">
              {agentInfo.firstName} {agentInfo.lastName}
            </div>
          </div>
        )}

        <div className="space-y-2">
          <Label htmlFor="contactType">Contact Type</Label>
          <Select onValueChange={setContactType} value={contactType}>
            <SelectTrigger className="rounded-none">
              <SelectValue placeholder="Select contact type" />
            </SelectTrigger>
            <SelectContent>
              {contactTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label htmlFor="category">Category</Label>
          <Select onValueChange={setCategory} value={category}>
            <SelectTrigger className="rounded-none">
              <SelectValue placeholder="Select category" />
            </SelectTrigger>
            <SelectContent>
              {categoryGroups.map((group) => (
                <div key={group.id}>
                  <SelectItem value={`group-${group.id}`} disabled>
                    {group.name}
                  </SelectItem>
                  <SelectItem value={`separator-${group.id}`} disabled>
                    ====================
                  </SelectItem>
                  {group.categories.map((cat) => (
                    <SelectItem key={cat.id} value={cat.id.toString()}>
                      {cat.name}
                    </SelectItem>
                  ))}
                </div>
              ))}
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="needsAttention"
              checked={needsAttention}
              onCheckedChange={(checked) => setNeedsAttention(checked as boolean)}
              className="rounded-none"
            />
            <Label htmlFor="needsAttention">Needs Attention</Label>
          </div>
        </div>

        {needsAttention && (
          <div className="space-y-2">
            <Label>Critical Date</Label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal rounded-none",
                    !criticalDate && "text-muted-foreground"
                  )}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {criticalDate ? format(criticalDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={criticalDate}
                  onSelect={setCriticalDate}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        )}

        <div className="col-span-2 space-y-2">
          <Label htmlFor="notes">Notes</Label>
          <Textarea
            id="notes"
            value={notes}
            onChange={(e) => setNotes(e.target.value)}
            className="rounded-none min-h-[100px]"
          />
        </div>

        <div className="space-y-2">
          <Label>Contact Date</Label>
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant="outline"
                className={cn(
                  "w-full justify-start text-left font-normal rounded-none",
                  !contactDate && "text-muted-foreground"
                )}
              >
                <CalendarIcon className="mr-2 h-4 w-4" />
                {contactDate ? format(contactDate, "PPP") : "Pick a date"}
              </Button>
            </PopoverTrigger>
            <PopoverContent className="w-auto p-0" align="start">
              <Calendar
                mode="single"
                selected={contactDate}
                onSelect={setContactDate}
                initialFocus
              />
            </PopoverContent>
          </Popover>
        </div>
      </div>

      <div className="flex justify-start">
        <Button type="submit" className="rounded-none">
          Submit
        </Button>
      </div>
    </form>
  )
} 