import { <PERSON>, Card<PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/app/components/ui/card"

interface StatCardProps {
  title: string
  value: string | number
  className?: string
}

function StatCard({ title, value, className = "" }: StatCardProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium">{title}</CardTitle>
      </CardHeader>
      <CardContent>
        <div className={`text-2xl font-bold ${className}`}>{value}</div>
      </CardContent>
    </Card>
  )
}

export function AgentDashboard() {
  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <StatCard title="Total Advisors" value="5" />
      <StatCard title="Active" value="4" className="text-green-600" />
      <StatCard title="Inactive" value="1" className="text-amber-600" />
      <StatCard title="Average Rating" value="4.6" className="text-blue-600" />
    </div>
  )
} 