"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"

export function BillingStep() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">Billing Address</h2>
        <div className="flex items-center space-x-2 mb-4">
          <Checkbox id="same-address" className="rounded-none" />
          <Label htmlFor="same-address">Same as shipping address</Label>
        </div>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="billing-address">Address</Label>
            <Input id="billing-address" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billing-address2">Address 2</Label>
            <Input id="billing-address2" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billing-city">City</Label>
            <Input id="billing-city" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billing-state">State</Label>
            <Input id="billing-state" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billing-zip">ZIP</Label>
            <Input id="billing-zip" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="billing-country">Country</Label>
            <Input id="billing-country" className="rounded-none" />
          </div>
        </div>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">Payment Information</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="card-type">Card Type</Label>
            <Select>
              <SelectTrigger className="rounded-none">
                <SelectValue placeholder="Select card type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="visa">Visa</SelectItem>
                <SelectItem value="mastercard">Mastercard</SelectItem>
                <SelectItem value="amex">American Express</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="space-y-2">
            <Label htmlFor="card-number">Card Number</Label>
            <Input id="card-number" className="rounded-none" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="expiry">Expiry Date</Label>
            <div className="grid grid-cols-2 gap-2">
              <Select>
                <SelectTrigger className="rounded-none">
                  <SelectValue placeholder="MM" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 12 }, (_, i) => i + 1).map((month) => (
                    <SelectItem key={month} value={month.toString().padStart(2, '0')}>
                      {month.toString().padStart(2, '0')}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select>
                <SelectTrigger className="rounded-none">
                  <SelectValue placeholder="YYYY" />
                </SelectTrigger>
                <SelectContent>
                  {Array.from({ length: 10 }, (_, i) => new Date().getFullYear() + i).map((year) => (
                    <SelectItem key={year} value={year.toString()}>
                      {year}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="cardholder">Cardholder Name</Label>
            <Input id="cardholder" className="rounded-none" />
          </div>

          <div className="space-y-2">
            <Label htmlFor="monthly-fee">Monthly Fee</Label>
            <Input id="monthly-fee" type="number" className="rounded-none" />
          </div>
        </div>
      </div>
    </div>
  )
} 