"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"

export function BasicInfoStep() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="first-name">First Name</Label>
          <Input id="first-name" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="middle-name">Middle Initial</Label>
          <Input id="middle-name" className="rounded-none" maxLength={1} />
        </div>

        <div className="space-y-2">
          <Label htmlFor="last-name">Last Name</Label>
          <Input id="last-name" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="email">Email</Label>
          <Input id="email" type="email" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="phone">Phone</Label>
          <Input id="phone" type="tel" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="company">Company</Label>
          <Input id="company" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="ssn">SSN</Label>
          <Input id="ssn" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label>Gender</Label>
          <RadioGroup defaultValue="male" className="flex space-x-4">
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="male" id="male" className="rounded-none" />
              <Label htmlFor="male">Male</Label>
            </div>
            <div className="flex items-center space-x-2">
              <RadioGroupItem value="female" id="female" className="rounded-none" />
              <Label htmlFor="female">Female</Label>
            </div>
          </RadioGroup>
        </div>
      </div>
    </div>
  )
} 