"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"

export function AddressStep() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold mb-4">Shipping Address</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="space-y-2">
            <Label htmlFor="shipping-address">Address</Label>
            <Input id="shipping-address" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipping-address2">Address 2</Label>
            <Input id="shipping-address2" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipping-city">City</Label>
            <Input id="shipping-city" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipping-state">State</Label>
            <Input id="shipping-state" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipping-zip">ZIP</Label>
            <Input id="shipping-zip" className="rounded-none" />
          </div>
          <div className="space-y-2">
            <Label htmlFor="shipping-country">Country</Label>
            <Input id="shipping-country" className="rounded-none" />
          </div>
        </div>
      </div>
    </div>
  )
} 