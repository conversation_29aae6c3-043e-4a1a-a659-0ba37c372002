"use client"

import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"

export function AdditionalInfoStep() {
  return (
    <div className="space-y-6">
      <div className="grid grid-cols-2 gap-4">
        <div className="space-y-2">
          <Label htmlFor="referred-by">Referred By</Label>
          <Input id="referred-by" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="sales-number">Sales Number</Label>
          <Input id="sales-number" className="rounded-none" />
        </div>

        <div className="space-y-2">
          <Label htmlFor="initials">Initials</Label>
          <Input id="initials" className="rounded-none" maxLength={4} />
        </div>

        <div className="space-y-2">
          <Label htmlFor="photo-received">Photo Received Date</Label>
          <Input id="photo-received" type="date" className="rounded-none" />
        </div>
      </div>

      <div className="space-y-2">
        <Label htmlFor="notes">Notes</Label>
        <Textarea id="notes" className="rounded-none min-h-[150px]" />
      </div>

      <div className="space-y-4">
        <div className="flex items-center space-x-2">
          <Checkbox id="needs-attention" className="rounded-none" />
          <Label htmlFor="needs-attention">Needs Attention</Label>
        </div>

        <div className="flex items-center space-x-2">
          <Checkbox id="opt-newsletter" className="rounded-none" />
          <Label htmlFor="opt-newsletter">Opt-in Newsletter</Label>
        </div>
      </div>
    </div>
  )
} 