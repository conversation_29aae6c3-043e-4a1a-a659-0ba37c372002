"use client"

import { cn } from "@/lib/utils"
import { Check } from "lucide-react"

interface Step {
  title: string
  description: string
}

const steps: Step[] = [
  {
    title: "Basic Information",
    description: "Personal and contact details"
  },
  {
    title: "Address Information",
    description: "Shipping and billing addresses"
  },
  {
    title: "Billing Information",
    description: "Payment and subscription details"
  },
  {
    title: "Additional Information",
    description: "Other agent details and preferences"
  }
]

interface StepperProps {
  currentStep: number
  setCurrentStep: (step: number) => void
}

export function Stepper({ currentStep, setCurrentStep }: StepperProps) {
  return (
    <div className="w-full border-b rounded-none">
      <div className="flex justify-between">
        {steps.map((step, index) => (
          <div 
            key={step.title}
            className={cn(
              "flex-1 transition-all duration-300 ease-in-out",
              index !== steps.length - 1 && "border-r"
            )}
          >
            <button
              onClick={() => setCurrentStep(index)}
              className={cn(
                "w-full p-4 text-left transition-colors",
                currentStep === index ? "bg-primary/5" : "hover:bg-muted/50",
                currentStep > index && "text-primary"
              )}
            >
              <div className="flex items-center gap-3">
                <div className={cn(
                  "flex items-center justify-center w-8 h-8 rounded-full border",
                  currentStep > index && "bg-primary border-primary text-white",
                  currentStep === index && "border-primary text-primary",
                )}>
                  {currentStep > index ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <span>{index + 1}</span>
                  )}
                </div>
                <div>
                  <p className="font-medium">{step.title}</p>
                  <p className="text-sm text-muted-foreground">{step.description}</p>
                </div>
              </div>
            </button>
          </div>
        ))}
      </div>
    </div>
  )
} 