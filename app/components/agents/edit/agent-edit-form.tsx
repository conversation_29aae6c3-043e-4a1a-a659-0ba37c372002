"use client"

import { useState } from "react"
import { Stepper } from "./stepper"
import { BasicInfoStep } from "./steps/basic-info"
import { AddressStep } from "./steps/address"
import { BillingStep } from "./steps/billing"
import { AdditionalInfoStep } from "./steps/additional-info"
import { Button } from "@/components/ui/button"

interface AgentEditFormProps {
  agentId?: string
}

export function AgentEditForm({ agentId }: AgentEditFormProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const isEditing = !!agentId

  const steps = [
    <BasicInfoStep key="basic" />,
    <AddressStep key="address" />,
    <BillingStep key="billing" />,
    <AdditionalInfoStep key="additional" />
  ]

  const handleSubmit = async () => {
    // Handle form submission
    if (isEditing) {
      // Update existing agent
    } else {
      // Create new agent
    }
  }

  return (
    <form>
      <Stepper currentStep={currentStep} setCurrentStep={setCurrentStep} />
      
      <div className="p-6">
        <div className="min-h-[400px] transition-all duration-300 ease-in-out">
          {steps[currentStep]}
        </div>

        <div className="flex justify-between mt-6 pt-6 border-t">
          <Button
            type="button"
            variant="outline"
            className="rounded-none"
            onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
            disabled={currentStep === 0}
          >
            Previous
          </Button>
          
          <Button
            type="button"
            className="rounded-none"
            onClick={() => {
              if (currentStep === steps.length - 1) {
                handleSubmit()
              } else {
                setCurrentStep(Math.min(steps.length - 1, currentStep + 1))
              }
            }}
          >
            {currentStep === steps.length - 1 
              ? (isEditing ? "Save Changes" : "Create Agent") 
              : "Next"
            }
          </Button>
        </div>
      </div>
    </form>
  )
} 