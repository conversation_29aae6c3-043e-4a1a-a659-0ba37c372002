"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle, CheckCircle2, Loader2 } from "lucide-react"
import { Progress } from "@/components/ui/progress"

const formSchema = z.object({
  file: z.instanceof(File, { message: "Please select a file" })
    .refine(file => file.size > 0, "File is required")
    .refine(file => file.size <= 5 * 1024 * 1024, "File size must be less than 5MB")
    .refine(file => file.name.endsWith('.csv'), "File must be a CSV file")
})

interface UploadResponse {
  success: boolean
  message: string
  fileName: string
  filePath: string
  totalRecords: number
  processedRecords: number
  errors: { line: number; error: string }[]
}

export function TravelInsuredUpload() {
  const [isUploading, setIsUploading] = useState(false)
  const [uploadResult, setUploadResult] = useState<UploadResponse | null>(null)
  const [uploadError, setUploadError] = useState<string | null>(null)

  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      file: undefined,
    },
  })

  async function onSubmit(values: z.infer<typeof formSchema>) {
    setIsUploading(true)
    setUploadResult(null)
    setUploadError(null)

    try {
      const reader = new FileReader()
      
      reader.onload = async (event) => {
        const base64String = event.target?.result?.toString().split(',')[1]
        
        if (!base64String) {
          throw new Error("Failed to convert file to base64")
        }

        const mutation = `
          mutation UploadTravelInsuredCSV {
            uploadTravelInsuredCSV(
              input: {
                file: "${base64String}"
                fileName: "${values.file.name}"
              }
            ) {
              success
              message
              fileName
              filePath
              totalRecords
              processedRecords
              errors {
                line
                error
              }
            }
          }
        `

        const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || '/api/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({ query: mutation }),
        })

        const result = await response.json()
        
        if (result.errors) {
          throw new Error(result.errors[0].message)
        }

        setUploadResult(result.data.uploadTravelInsuredCSV)
      }

      reader.onerror = () => {
        throw new Error("Error reading file")
      }

      reader.readAsDataURL(values.file)
    } catch (error) {
      setUploadError(error instanceof Error ? error.message : "An unknown error occurred")
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <Card className="w-full rounded-none">
      <CardHeader>
        <CardDescription>
          Upload a CSV file containing Travel Insured policy information
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
            <FormField
              control={form.control}
              name="file"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>CSV File</FormLabel>
                  <FormControl>
                    <Input
                      type="file"
                      accept=".csv"
                      onChange={(e) => {
                        const file = e.target.files?.[0]
                        if (file) {
                          field.onChange(file)
                        }
                      }}
                    />
                  </FormControl>
                  <FormDescription>
                    Upload a CSV file with Travel Insured policy data
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button type="submit" disabled={isUploading}>
              {isUploading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Uploading...
                </>
              ) : (
                "Upload"
              )}
            </Button>
          </form>
        </Form>

        {isUploading && (
          <div className="mt-6 space-y-2">
            <p className="text-sm text-muted-foreground">Processing file...</p>
            <Progress value={50} className="h-2" />
          </div>
        )}

        {uploadResult && (
          <Alert className={`mt-6 ${uploadResult.success ? 'bg-green-50' : 'bg-red-50'}`}>
            <div className="flex items-start">
              {uploadResult.success ? (
                <CheckCircle2 className="h-5 w-5 text-green-600 mr-2" />
              ) : (
                <AlertCircle className="h-5 w-5 text-red-600 mr-2" />
              )}
              <div>
                <AlertTitle>{uploadResult.success ? 'Success' : 'Error'}</AlertTitle>
                <AlertDescription className="mt-2">
                  {uploadResult.message}
                  
                  {uploadResult.totalRecords > 0 && (
                    <div className="mt-2">
                      <p>Total records: {uploadResult.totalRecords}</p>
                      <p>Processed records: {uploadResult.processedRecords}</p>
                    </div>
                  )}
                  
                  {uploadResult.errors && uploadResult.errors.length > 0 && (
                    <div className="mt-2">
                      <p className="font-semibold">Errors:</p>
                      <ul className="list-disc pl-5 mt-1">
                        {uploadResult.errors.map((error, index) => (
                          <li key={index}>
                            Line {error.line}: {error.error}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </AlertDescription>
              </div>
            </div>
          </Alert>
        )}

        {uploadError && (
          <Alert className="mt-6 bg-red-50">
            <AlertCircle className="h-5 w-5 text-red-600" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{uploadError}</AlertDescription>
          </Alert>
        )}
      </CardContent>
      <CardFooter className="flex justify-between">
        <p className="text-sm text-muted-foreground">
          Make sure your CSV file follows the required format
        </p>
      </CardFooter>
    </Card>
  )
} 