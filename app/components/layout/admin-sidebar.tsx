"use client"

import { sidebarConfig } from "../../admin/config/sidebar"
import { Icons } from "@/components/ui/icons"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { cn } from "@/lib/utils"

export function AdminSidebar() {
  const pathname = usePathname()
  
  return (
    <nav className="space-y-6">
      {sidebarConfig.map((section) => (
        <div key={section.title} className="px-3 py-2">
          <h2 className="mb-2 px-4 text-lg font-semibold">{section.title}</h2>
          <div className="space-y-1">
            {section.items.map((link) => {
              const Icon = Icons[link.icon]
              return (
                <Link
                  key={link.href}
                  href={link.href}
                  className={cn(
                    "flex items-center gap-3 rounded-lg px-3 py-2 text-sm transition-colors",
                    "hover:bg-accent hover:text-accent-foreground",
                    pathname === link.href && "bg-accent text-accent-foreground"
                  )}
                >
                  <Icon className="h-4 w-4" />
                  {link.title}
                </Link>
              )
            })}
          </div>
        </div>
      ))}
    </nav>
  )
} 