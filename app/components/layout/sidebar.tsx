'use client'

import { cn } from "@/lib/utils"
import {
  LayoutDashboard,
  Users,
  Calendar,
  Bookmark,
  ClipboardList,
  DollarSign,
  FileText,
  UserCheck,
  BookOpen,
  UserPlus,
  ChevronDown,
  Upload,
  type LucideIcon,
  ShieldIcon,
  AlertTriangleIcon,
  Building2,
  Building2Icon,
  BookIcon,
  Search,
  AlertTriangle,
  BarChart,
  Globe,
  PhoneIcon,
  FileTextIcon,
  Tag,
  HandshakeIcon,
  Video,
  Award,
  GraduationCap,
  Home,
  PlaneIcon,
} from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useState } from "react"
import { buttonVariants } from "@/components/ui/button"

interface NavItem {
  title: string
  href?: string
  icon: LucideIcon
  items?: NavItem[]
  permission?: string
}

const sidebarLinks: NavItem[] = [
  {
    title: "Dashboard",
    href: "/",
    icon: LayoutDashboard
  },
  {
    title: "Agents",
    icon: Users,
    items: [
      {
        title: "All Agents",
        href: "/agents",
        icon: Users
      },
      {
        title: "Agent Report",
        href: "/agents/report",
        icon: FileText
      },
      {
        title: "Coach Report",
        href: "/agents/coach-report",
        icon: ClipboardList
      },
      {
        title: "Sellers Report",
        href: "/agents/sellers-report",
        icon: Bookmark
      },
      {
        title: "Bonus Commission",
        href: "/agents/bonus-commission",
        icon: DollarSign
      },
      {
        title: "Trial Report",
        href: "/agents/trial-report",
        icon: FileText
      },
      {
        title: "Admin Users",
        href: "/agents/admin-users",
        icon: UserCheck
      },
      {
        title: "GDPR Training",
        href: "/agents/gdpr-training",
        icon: BookOpen
      },
      {
        title: "UK Registration",
        href: "/agents/uk-registration",
        icon: UserPlus
      },
      {
        title: "Monthly UK Report",
        href: "/agents/monthly-uk-report",
        icon: FileText
      },
      {
        title: "World View",
        href: "/agents/world-view",
        icon: Globe
      },
    ]
  },
  {
    title: "Bookings",
    icon: Calendar,
    items: [
      {
        title: "All Bookings",
        href: "/bookings",
        icon: Calendar
      },
      {
        title: "Today's Bookings",
        href: `bookings?dBooked=${encodeURIComponent(JSON.stringify({
          "operator": "equals",
          "value": new Date().toISOString().split('T')[0] + " 00:00:00"
        }))}&page=1&pageSize=10`,
        icon: Calendar
      },
      {
        title: "Pending Bookings",
        href: `/bookings?nBookingStatusID=${encodeURIComponent(JSON.stringify({"operator":"equals","value":"3"}))}&page=1&pageSize=10`,
        icon: Calendar
      },
      {
        title: "Bank Bookings",
        href: `/bookings?bInTheBank=${encodeURIComponent(JSON.stringify({"operator":"equals","value":"1"}))}&page=1&pageSize=10`,
        icon: DollarSign
      },
      {
        title: "Check Reconciliation",
        href: "/bookings/check-reconciliation",
        icon: FileText
      },
      {
        title: "Check Scans",
        href: "/bookings/check-scans",
        icon: FileText
      },
      {
        title: "ECS Upload",
        href: "/bookings/ecs-upload",
        icon: Upload,
      },
      {
        title: "Travel Insured",
        href: "/bookings/travel-insured",
        icon: Upload,
      },
      {
        title: "Free User Bookings",
        href: "/bookings/free-user",
        icon: Calendar
      },
      {
        title: "Agent Bookings",
        href: "/bookings/agent",
        icon: Calendar
      },
      {
        title: "Mexican Bookings",
        href: "/bookings/mexican-bookings",
        icon: DollarSign
      },
      {
        title: "MOR Bookings",
        href: "/bookings/mor-bookings",
        icon: DollarSign
      },
      {
        title: "Large Bookings",
        href: "/bookings/large-bookings",
        icon: DollarSign
      },
      {
        title: "Search Claim Booking",
        href: "/bookings/search",
        icon: Search
      },
    ]
  },
  {
    title: "Leads",
    href: "/leads",
    icon: Bookmark
  },
  {
    title: "Commissions",
    icon: DollarSign,
    items: [
      {
        title: "All Commissions",
        href: "/commissions",
        icon: DollarSign
      },
      {
        title: "Commission Management",
        href: "/commissions/management",
        icon: BarChart
      },
      {
        title: "Commission Cleanup",
        href: "/commissions/cleanup",
        icon: BarChart
      },
      {
        title: "Commission Holds",
        href: "/commissions/holds",
        icon: AlertTriangle
      },
      {
        title: "Empty Check Addresses",
        href: "/commissions/empty-addresses",
        icon: AlertTriangle
      },
      {
        title: "Check Run PINs",
        href: "/commissions/check-run-pins",
        icon: FileText
      },
      {
        title: "Check Run Amounts",
        href: "/commissions/check-run-amounts",
        icon: FileText
      },
    ]
  },
  {
    title: "Transactions",
    href: "/transactions",
    icon: ClipboardList
  },
  {
    title: "Suppliers",
    icon: Building2Icon,
    items: [
      {
        title: "All Suppliers",
        href: "/suppliers",
        icon: Building2Icon
      },
      {
        title: "MOR Suppliers",
        href: "/suppliers/mor",
        icon: Building2Icon
      },
    ]
  },
  {
    title: "Administrative",
    icon: ShieldIcon,
    items: [
      {
        title: "Error Management",
        href: "/admin/error-management",
        icon: AlertTriangleIcon
      },
      {
        title: "Partner Education",
        href: "/admin/partner-education",
        icon: BookIcon
      },
      {
        title: "Partner Preferred",
        href: "/admin/partner-preferred",
        icon: Building2Icon
      },
      {
        title: "Partner Preferred MX",
        href: "/admin/partner-preferred-mx",
        icon: HandshakeIcon,
      },
      {
        title: "Ticket Limit Report",
        href: "/admin/ticket-limit-report",
        icon: Search
      },
      {
        title: "World View",
        href: "/admin/world-view",
        icon: Globe
      },
      {
        title: "VoiceShot Bounce Report",
        href: "/admin/voiceshot-bounce-report",
        icon: PhoneIcon
      },
      {
        title: "Reactivation Report",
        href: "/admin/reactivation-report",
        icon: FileTextIcon
      },
      {
        title: "Events Calendar",
        href: "/admin/events/calendar",
        icon: Calendar
      },
      {
        title: "FAMs",
        href: "/admin/fams",
        icon: PlaneIcon,
      },
    ]
  },
  {
    title: "Marketing",
    icon: Tag,
    items: [
      {
        title: "Weekly Deals",
        href: "/admin/marketing/weekly-deals",
        icon: Tag,
      },
      {
        title: "Hot Deals",
        href: "/admin/hot-deals",
        icon: Tag,
      },
      {
        title: "Partner Preferred",
        href: "/admin/partner-preferred",
        icon: Building2Icon,
      },
      {
        title: "Newsletter",
        href: "/newsletters",
        icon: FileTextIcon,
      },
    ],
  },
  {
    title: "Education",
    icon: BookOpen,
    items: [
      {
        title: "Tutorials",
        href: "/admin/education/tutorials",
        icon: Video,
      },
      {
        title: "Dream Maker Certificates",
        href: "/admin/education/dream-maker",
        icon: Award,
      },
    ],
  },
  {
    title: "Events",
    icon: Calendar,
    items: [
      {
        title: "Events Calendar",
        href: "/admin/events/calendar",
        icon: Calendar
      },
      {
        title: "Event Upload",
        href: "/admin/events/upload",
        icon: Upload
      }
    ]
  },
]

const ICON_MAP = {
  Video: Video,
} as const

export function Sidebar({ className }: { className?: string }) {
  const pathname = usePathname()
  const [openSections, setOpenSections] = useState<string[]>([])

  const toggleSection = (title: string) => {
    setOpenSections(prev =>
      prev.includes(title)
        ? prev.filter(t => t !== title)
        : [...prev, title]
    )
  }

  const isActive = (href?: string) => href && pathname.startsWith(href)

  const renderNavItem = (item: NavItem, depth = 0) => {
    const isExpanded = openSections.includes(item.title)
    const active = isActive(item.href)
    const Icon = item.icon

    return (
      <div key={item.title + (item.href || '')} className="w-full">
        {item.href ? (
          <Link
            href={item.href}
            className={cn(
              "flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-colors",
              active ? "bg-[#3d4ed7] text-white" : "text-white/70 hover:bg-[#3d4ed7] hover:text-white",
              depth > 0 && "ml-4 text-sm"
            )}
          >
            <Icon className="h-4 w-4" />
            <span>{item.title}</span>
          </Link>
        ) : (
          <>
            <button
              onClick={() => toggleSection(item.title)}
              className={cn(
                "flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm transition-colors",
                active ? "bg-[#3d4ed7] text-white" : "text-white/70 hover:bg-[#3d4ed7] hover:text-white"
              )}
            >
              <div className="flex items-center gap-2">
                <Icon className="h-4 w-4" />
                <span>{item.title}</span>
              </div>
              {item.items && (
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isExpanded && "rotate-180"
                )} />
              )}
            </button>
            {isExpanded && item.items && (
              <div className="mt-1">
                {item.items.map(subItem => renderNavItem(subItem, depth + 1))}
              </div>
            )}
          </>
        )}
      </div>
    )
  }

  return (
    <div className={cn(
      "flex h-full w-64 flex-col bg-[rgb(30,37,93)] text-white",
      className
    )}>
      <div className="flex h-16 items-center border-b border-white/10 px-6">
        <span className="text-lg font-semibold">InteleTravel</span>
      </div>
      <nav className="flex-1 space-y-1 overflow-y-auto p-4">
        {sidebarLinks.map(item => renderNavItem(item))}
      </nav>
    </div>
  )
} 