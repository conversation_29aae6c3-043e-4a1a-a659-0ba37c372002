'use client'

import { <PERSON> } from "lucide-react"
import { But<PERSON> } from "../ui/button"

export function Navbar() {
  return (
    <div className="h-16 bg-[rgb(30,37,93)] border-b border-white/10 px-4 flex items-center justify-between">
      {/* Left side - can be used for breadcrumbs or title */}
      <div className="text-white font-semibold">
        Admin Portal
      </div>

      {/* Right side - notifications */}
      <div className="flex items-center space-x-4">
        <Button 
          variant="ghost" 
          size="icon"
          className="text-white hover:bg-white/10"
        >
          <Bell className="h-5 w-5" />
        </Button>
      </div>
    </div>
  )
} 