"use client"

import * as React from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Input } from "@/app/components/ui/input"
import { BookingSearchResult } from "../types/booking"

interface DataTableProps {
  data: BookingSearchResult[]
}

export function BookingTable({ data }: DataTableProps) {
  const [searchTerm, setSearchTerm] = React.useState("")
  const [sortConfig, setSortConfig] = React.useState<{
    key: keyof BookingSearchResult | null
    direction: 'asc' | 'desc'
  }>({ key: null, direction: 'asc' })

  const filteredData = React.useMemo(() => {
    return data.filter(booking => 
      booking.vcClientName.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }, [data, searchTerm])

  const sortedData = React.useMemo(() => {
    const sorted = [...filteredData]
    if (sortConfig.key) {
      sorted.sort((a, b) => {
        const aVal = a[sortConfig.key!] ?? ''
        const bVal = b[sortConfig.key!] ?? ''
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1
        return 0
      })
    }
    return sorted
  }, [filteredData, sortConfig])

  const handleSort = (key: keyof BookingSearchResult) => {
    setSortConfig({
      key,
      direction: 
        sortConfig.key === key && sortConfig.direction === 'asc' 
          ? 'desc' 
          : 'asc',
    })
  }

  const columns = [
    { key: 'vcPIN' as const, label: 'PIN' },
    { key: 'vcConfirmationID' as const, label: 'Confirmation ID' },
    { key: 'vcClientName' as const, label: 'Client Name' },
    { key: 'dTravelStartDate' as const, label: 'Travel Start Date' },
    { key: 'dTravelEndDate' as const, label: 'Travel End Date' },
    { key: 'vcFinalDestination' as const, label: 'Final Destination' },
  ]

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <Input
          placeholder="Filter by client name..."
          value={searchTerm}
          onChange={(e: React.ChangeEvent<HTMLInputElement>) => setSearchTerm(e.target.value)}
          className="max-w-sm"
        />
      </div>
      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              {columns.map((column) => (
                <TableHead 
                  key={column.key}
                  className="cursor-pointer hover:bg-muted/50"
                  onClick={() => handleSort(column.key)}
                >
                  <div className="flex items-center gap-2">
                    {column.label}
                    {sortConfig.key === column.key && (
                      <span>{sortConfig.direction === 'asc' ? '↑' : '↓'}</span>
                    )}
                  </div>
                </TableHead>
              ))}
            </TableRow>
          </TableHeader>
          <TableBody>
            {sortedData.length > 0 ? (
              sortedData.map((booking, index) => (
                <TableRow key={index}>
                  {columns.map(column => (
                    <TableCell key={column.key}>
                      {column.key.includes('Date') 
                        ? formatDate(booking[column.key] ?? '')
                        : booking[column.key] ?? ''}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell 
                  colSpan={columns.length} 
                  className="h-24 text-center"
                >
                  No results.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  )
}

function formatDate(date: string) {
  return new Date(date).toLocaleDateString()
} 