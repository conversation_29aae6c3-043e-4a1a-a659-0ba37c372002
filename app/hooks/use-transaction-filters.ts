"use client"

import { useState, useCallback } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { type DateRange } from "react-day-picker"

interface TransactionFilters {
  dateRange?: DateRange
  startDate?: string
  endDate?: string
  minAmount?: string
  maxAmount?: string
  status?: string
  type?: string
}

export function useTransactionFilters() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [filters, setFilters] = useState<TransactionFilters>({
    startDate: searchParams.get("startDate") || "",
    endDate: searchParams.get("endDate") || "",
    minAmount: searchParams.get("minAmount") || "",
    maxAmount: searchParams.get("maxAmount") || "",
    status: searchParams.get("status") || "",
    type: searchParams.get("type") || "",
  })

  const updateFilters = useCallback((newFilters: Partial<TransactionFilters>) => {
    const params = new URLSearchParams(searchParams.toString())
    
    Object.entries(newFilters).forEach(([key, value]) => {
      if (value) {
        if (key === 'dateRange' && typeof value !== 'string') {
          if (value.from) params.set('startDate', value.from.toISOString())
          if (value.to) params.set('endDate', value.to.toISOString())
        } else {
          params.set(key, String(value))
        }
      } else {
        params.delete(key)
      }
    })

    router.push(`?${params.toString()}`)
    setFilters(prev => ({ ...prev, ...newFilters }))
  }, [router, searchParams])

  const resetFilters = useCallback(() => {
    router.push(window.location.pathname)
    setFilters({})
  }, [router])

  return {
    filters,
    updateFilters,
    resetFilters
  }
} 