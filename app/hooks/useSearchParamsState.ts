'use client'

import { useCallback, useEffect, useState } from 'react'
import { useRouter, useSearchParams, usePathname } from 'next/navigation'

export function useSearchParamsState<T>(
  key: string,
  defaultValue: T,
  serialize = JSON.stringify,
  deserialize = JSON.parse
): [T, (value: T) => void] {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()
  const [value, setValue] = useState<T>(() => {
    const param = searchParams.get(key)
    return param ? deserialize(param) : defaultValue
  })

  useEffect(() => {
    const param = searchParams.get(key)
    if (param) {
      setValue(deserialize(param))
    }
  }, [key, searchParams, deserialize])

  const updateValue = useCallback((newValue: T) => {
    setValue(newValue)
    const params = new URLSearchParams(searchParams.toString())
    params.set(key, serialize(newValue))
    router.replace(`${pathname}?${params.toString()}`)
  }, [key, pathname, router, searchParams, serialize])

  return [value, updateValue]
} 