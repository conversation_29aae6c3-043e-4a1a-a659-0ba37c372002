import { useEffect, useState } from 'react'
import { useMediaQuery } from '@/app/hooks/use-media-query'

export interface ColumnConfig {
  id: string
  header: string
  accessorKey: string
  isVisible: boolean
  showInExpandedOnly?: boolean
}

export function useResponsiveColumns(initialColumns: ColumnConfig[]) {
  const isMobile = useMediaQuery('(max-width: 640px)')
  const isTablet = useMediaQuery('(max-width: 1024px)')
  
  const getVisibleColumns = () => {
    return initialColumns.map(column => ({
      ...column,
      isVisible: isMobile ? !column.showInExpandedOnly 
        : isTablet ? !column.showInExpandedOnly || column.id === 'status'
        : true
    }))
  }

  const [columns, setColumns] = useState(getVisibleColumns())

  useEffect(() => {
    setColumns(getVisibleColumns())
  }, [isMobile, isTablet])

  return {
    mainColumns: columns.filter(col => col.isVisible),
    expandedColumns: columns.filter(col => !col.isVisible)
  }
} 