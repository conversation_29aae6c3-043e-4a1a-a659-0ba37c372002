import { useState, useCallback, useEffect } from 'react'
import { useSearchParams } from 'next/navigation'
import { FilterCriteria, DateFilterCriteria, AdvancedAgentFilters } from '@/app/lib/api/agent-api'

interface FilterCriteriaUI extends FilterCriteria {
  field: string
  id: string
}

interface DateFilterCriteriaUI extends DateFilterCriteria {
  field: string
  id: string
}

export type FilterCriteriaUIType = FilterCriteriaUI | DateFilterCriteriaUI

interface CurrentFilter {
  field: string
  operator: string
  value: string | Date
  type: "text" | "date"
}

export function useAgentFilters() {
  const searchParams = useSearchParams()
  
  const [filters, setFilters] = useState<FilterCriteriaUIType[]>([])
  const [currentFilter, setCurrentFilter] = useState<CurrentFilter>({
    field: "",
    operator: "",
    value: "",
    type: "text",
  })
  const [isApplying, setIsApplying] = useState(false)

  // Helper function to get current filters as AdvancedAgentFilters
  const getCurrentAdvancedFilters = useCallback((filterList: FilterCriteriaUIType[]): AdvancedAgentFilters => {
    const advancedFilters: AdvancedAgentFilters = {}
    
    filterList.forEach(filter => {
      const fieldKey = filter.field as keyof AdvancedAgentFilters
      
      if (filter.field === "createdDate" || filter.field === "modifiedDate") {
        const dateFilter: DateFilterCriteria = {
          operator: filter.operator as DateFilterCriteria['operator'],
          value: filter.value as string
        }
        ;(advancedFilters as Record<string, DateFilterCriteria | FilterCriteria>)[fieldKey] = dateFilter
      } else {
        const textFilter: FilterCriteria = {
          operator: filter.operator as FilterCriteria['operator'],
          value: filter.value as string
        }
        ;(advancedFilters as Record<string, DateFilterCriteria | FilterCriteria>)[fieldKey] = textFilter
      }
    })

    return advancedFilters
  }, [])

  // Helper function to apply filters to URL and trigger data refresh
  const applyFiltersToURL = useCallback((filterList: FilterCriteriaUIType[]) => {
    const advancedFilters = getCurrentAdvancedFilters(filterList)
    const params = new URLSearchParams(searchParams.toString())
    
    if (Object.keys(advancedFilters).length > 0) {
      params.set('filters', JSON.stringify(advancedFilters))
    } else {
      params.delete('filters')
    }
    
    params.set('page', '1')
    
    // Update URL without page reload
    const newUrl = `/agents?${params.toString()}`
    window.history.pushState({}, '', newUrl)
    
    // Dispatch custom event with filter data
    window.dispatchEvent(new CustomEvent('filtersChanged', {
      detail: {
        filters: advancedFilters,
        page: 1,
        limit: Number(searchParams.get('limit')) || 10,
        orderBy: searchParams.get('orderBy') || "vcFName",
        sortDirection: (searchParams.get('sortDirection') as 'ASC' | 'DESC') || "ASC"
      }
    }))
  }, [searchParams, getCurrentAdvancedFilters])

  // Initialize filters from URL on mount
  useEffect(() => {
    const filtersParam = searchParams.get('filters')
    if (filtersParam) {
      try {
        const urlFilters: AdvancedAgentFilters = JSON.parse(filtersParam)
        const filterArray: FilterCriteriaUIType[] = []
        
        Object.entries(urlFilters).forEach(([field, filterData]) => {
          // Skip pagination and sorting params
          if (['page', 'pageSize', 'orderBy', 'sortDirection'].includes(field)) {
            return
          }
          
          if (filterData && typeof filterData === 'object' && 'operator' in filterData && 'value' in filterData) {
            filterArray.push({
              id: `${field}_${Date.now()}_${Math.random()}`,
              field,
              operator: filterData.operator,
              value: filterData.value
            })
          }
        })
        
        setFilters(filterArray)
      } catch (error) {
        console.error('Error parsing filters from URL:', error)
      }
    }
  }, [searchParams])

  const addFilter = useCallback((filter: CurrentFilter) => {
    if (!filter.field || !filter.operator || !filter.value) return

    const isDateFieldType = filter.type === "date"
    const filterId = `${filter.field}_${Date.now()}`
    const filterValue = isDateFieldType && filter.value instanceof Date 
      ? filter.value.toISOString() 
      : filter.value as string
    
    const newFilter: FilterCriteriaUIType = isDateFieldType
      ? {
          id: filterId,
          field: filter.field,
          operator: filter.operator as DateFilterCriteria['operator'],
          value: filterValue
        }
      : {
          id: filterId,
          field: filter.field,
          operator: filter.operator as FilterCriteria['operator'],
          value: filterValue
        }
    
    const newFilters = [...filters, newFilter]
    setFilters(newFilters)
    // Auto-apply the new filters
    applyFiltersToURL(newFilters)
  }, [filters, applyFiltersToURL])

  const removeFilter = useCallback((id: string) => {
    const newFilters = filters.filter(f => f.id !== id)
    setFilters(newFilters)
    // Auto-apply the updated filters
    applyFiltersToURL(newFilters)
  }, [filters, applyFiltersToURL])

  const clearAllFilters = useCallback(() => {
    setFilters([])
    setCurrentFilter({
      field: "",
      operator: "",
      value: "",
      type: "text",
    })
    
    // Apply empty filters (which will clear the URL)
    applyFiltersToURL([])
  }, [applyFiltersToURL])

  const applyFilters = useCallback(async () => {
    setIsApplying(true)
    
    try {
      applyFiltersToURL(filters)
    } catch (error) {
      console.error('Error applying filters:', error)
    } finally {
      setIsApplying(false)
    }
  }, [filters, applyFiltersToURL])

  return {
    filters,
    currentFilter,
    setCurrentFilter,
    isApplying,
    addFilter,
    removeFilter,
    clearAllFilters,
    applyFilters,
    getCurrentAdvancedFilters
  }
} 