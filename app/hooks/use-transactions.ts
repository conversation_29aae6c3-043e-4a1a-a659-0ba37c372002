"use client"

import { useQuery } from "@tanstack/react-query"

interface TransactionResponse {
  agent: {
    fullName: string
    pin: string
    created: string
  }
  transactions: Array<{
    id: string
    date: string
    amount: number
    type: string
    method: string
    result: string
    admin: string
    comment: string
  }>
}

export function useTransactions(agentId: string) {
  return useQuery<TransactionResponse>({
    queryKey: ["transactions", agentId],
    queryFn: async () => {
      const response = await fetch(`/api/agents/${agentId}/transactions`)
      if (!response.ok) {
        throw new Error("Failed to fetch transactions")
      }
      return response.json()
    }
  })
} 