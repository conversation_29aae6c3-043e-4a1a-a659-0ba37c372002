"use client"

import { useState, useEffect } from "react"
import { useSearchParams } from "next/navigation"
import { Transaction } from "@/app/types/transaction"

export function useTransactionSearch() {
  const [transactions, setTransactions] = useState<Transaction[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const searchParams = useSearchParams()

  useEffect(() => {
    // Mock data for demonstration
    setTransactions([
      {
        id: "1",
        date: "2024-03-15",
        description: "Payment Received",
        amount: 1500,
        status: "completed",
        type: "credit"
      },
      // Add more mock transactions as needed
    ])
    setIsLoading(false)
  }, [searchParams])

  return { transactions, isLoading }
} 