"use client"

import React, { createContext, useContext, useEffect, useState } from 'react'
import { v4 as uuidv4 } from 'uuid'

interface SessionContextType {
  sessionId: string | null
}

const SessionContext = createContext<SessionContextType>({ sessionId: null })

export function SessionProvider({ children }: { children: React.ReactNode }) {
  const [sessionId, setSessionId] = useState<string | null>(null)

  useEffect(() => {
    let sid = localStorage.getItem("sessionId")
    if (!sid) {
      sid = uuidv4()
      localStorage.setItem("sessionId", sid)
    }
    setSessionId(sid)
  }, [])

  return (
    <SessionContext.Provider value={{ sessionId }}>
      {children}
    </SessionContext.Provider>
  )
}

export function useSession() {
  return useContext(SessionContext)
}