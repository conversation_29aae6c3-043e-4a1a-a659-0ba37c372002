'use client';

import React, { createContext, useContext, useState } from 'react';

interface SidebarContextType {
  showSidebar: boolean;
  toggleSidebar: (value?: boolean) => void;
}

const SidebarContext = createContext<SidebarContextType | undefined>(undefined);

export function SidebarProvider({ children }: { children: React.ReactNode }) {
  const [showSidebar, setShowSidebar] = useState(true);

  const toggleSidebar = (value?: boolean) => {
    setShowSidebar(prev => value !== undefined ? value : !prev);
  };

  return (
    <SidebarContext.Provider value={{ showSidebar, toggleSidebar }}>
      {children}
    </SidebarContext.Provider>
  );
}

export function useSidebar() {
  const context = useContext(SidebarContext);
  if (context === undefined) {
    throw new Error('useSidebar must be used within a SidebarProvider');
  }
  return context;
}