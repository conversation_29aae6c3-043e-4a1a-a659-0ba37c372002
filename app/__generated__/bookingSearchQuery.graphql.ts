/**
 * @generated SignedSource<<d764cf926a08be3f828eb7dea4eed9ac>>
 * @lightSyntaxTransform
 * @nogrep
 */

/* tslint:disable */
/* eslint-disable */
// @ts-nocheck

import { ConcreteRequest, Query } from 'relay-runtime';
export type ComparisonOperator = "contains" | "eq" | "gt" | "gte" | "lt" | "lte" | "%future added value";
export type BookingFiltersInput = {
  dTravelStartDate?: FilterOperator | null;
  iBookingID?: FilterOperator | null;
  nBookingStatusID?: FilterOperator | null;
  vcConfirmationID?: FilterOperator | null;
};
export type FilterOperator = {
  operator: ComparisonOperator;
  value?: any | null;
};
export type bookingSearchQuery$variables = {
  filters?: BookingFiltersInput | null;
  page: number;
  pageSize: number;
};
export type bookingSearchQuery$data = {
  readonly searchBookings: {
    readonly items: ReadonlyArray<{
      readonly bActive: number | null;
      readonly bIATANQual: number | null;
      readonly bInTheBank: number | null;
      readonly bNonCommissionable: number | null;
      readonly dAgentPaid: string | null;
      readonly dBooked: string | null;
      readonly dCheckRunDate: string | null;
      readonly dCreated: string | null;
      readonly dECS: string | null;
      readonly dModified: string | null;
      readonly dPending: string | null;
      readonly dTravelEndDate: string | null;
      readonly dTravelStartDate: string | null;
      readonly dVendorPaid: string | null;
      readonly iBookingID: number | null;
      readonly nAdminFee: number | null;
      readonly nAgentCommission: number | null;
      readonly nBookingStatusID: number | null;
      readonly nBookingTypeID: number | null;
      readonly nCheckRunID: number | null;
      readonly nCommissionRate: number | null;
      readonly nDepositAmount: number | null;
      readonly nEngineID: number | null;
      readonly nInteleTravelCommission: number | null;
      readonly nNumberOfPsgrs: number | null;
      readonly nOrderID: number | null;
      readonly nProcessingFee: number | null;
      readonly nSalesContactID: number | null;
      readonly nSegmentID: number | null;
      readonly nSupplierID: number | null;
      readonly nTotalCharges: number | null;
      readonly nTotalCommission: number | null;
      readonly nUserCreatedID: number | null;
      readonly nUserModifiedID: number | null;
      readonly nVendorCheckID: number | null;
      readonly totalChargesCurrency: string | null;
      readonly txtComments: string | null;
      readonly vcCancelConfirmation: string | null;
      readonly vcClientName: string | null;
      readonly vcConfirmationID: string | null;
      readonly vcFinalDestination: string | null;
      readonly vcFreeUserID: string | null;
      readonly vcGDS: string | null;
      readonly vcITCheckNumber: string | null;
      readonly vcLiftCity: string | null;
      readonly vcPIN: string | null;
      readonly vcTripID: string | null;
      readonly vcVendor: string | null;
      readonly vcVendorAddress: string | null;
      readonly vcVendorCheckNum: string | null;
      readonly vcVendorCityState: string | null;
      readonly vcVendorFax: string | null;
      readonly vcVendorID: number | null;
      readonly vcVendorNumber: string | null;
      readonly vcVendorPostalCode: string | null;
      readonly vcconfirmationidnew: string | null;
    }>;
    readonly page: number;
    readonly pageSize: number;
    readonly total: number;
    readonly totalPages: number;
  };
};
export type bookingSearchQuery = {
  response: bookingSearchQuery$data;
  variables: bookingSearchQuery$variables;
};

const node: ConcreteRequest = (function(){
var v0 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "filters"
},
v1 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "page"
},
v2 = {
  "defaultValue": null,
  "kind": "LocalArgument",
  "name": "pageSize"
},
v3 = [
  {
    "alias": null,
    "args": [
      {
        "kind": "Variable",
        "name": "filters",
        "variableName": "filters"
      },
      {
        "kind": "Variable",
        "name": "page",
        "variableName": "page"
      },
      {
        "kind": "Variable",
        "name": "pageSize",
        "variableName": "pageSize"
      }
    ],
    "concreteType": "BookingConnection",
    "kind": "LinkedField",
    "name": "searchBookings",
    "plural": false,
    "selections": [
      {
        "alias": null,
        "args": null,
        "concreteType": "Booking",
        "kind": "LinkedField",
        "name": "items",
        "plural": true,
        "selections": [
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "iBookingID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcPIN",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcFreeUserID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nEngineID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nBookingTypeID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcConfirmationID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nBookingStatusID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcClientName",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcLiftCity",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcFinalDestination",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nNumberOfPsgrs",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcGDS",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendor",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorAddress",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorCityState",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorPostalCode",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorNumber",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorFax",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dBooked",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dTravelStartDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dTravelEndDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nTotalCharges",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nAdminFee",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nCommissionRate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nTotalCommission",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "bIATANQual",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nInteleTravelCommission",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nAgentCommission",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nVendorCheckID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorCheckNum",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dVendorPaid",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "bInTheBank",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcITCheckNumber",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dAgentPaid",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcCancelConfirmation",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "txtComments",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nCheckRunID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dCheckRunDate",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "bActive",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dModified",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dCreated",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nUserModifiedID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nUserCreatedID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dPending",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "bNonCommissionable",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "dECS",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nOrderID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nSegmentID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "totalChargesCurrency",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nSupplierID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nDepositAmount",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nProcessingFee",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcconfirmationidnew",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "nSalesContactID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcVendorID",
            "storageKey": null
          },
          {
            "alias": null,
            "args": null,
            "kind": "ScalarField",
            "name": "vcTripID",
            "storageKey": null
          }
        ],
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "total",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "totalPages",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "page",
        "storageKey": null
      },
      {
        "alias": null,
        "args": null,
        "kind": "ScalarField",
        "name": "pageSize",
        "storageKey": null
      }
    ],
    "storageKey": null
  }
];
return {
  "fragment": {
    "argumentDefinitions": [
      (v0/*: any*/),
      (v1/*: any*/),
      (v2/*: any*/)
    ],
    "kind": "Fragment",
    "metadata": null,
    "name": "bookingSearchQuery",
    "selections": (v3/*: any*/),
    "type": "RootQuery",
    "abstractKey": null
  },
  "kind": "Request",
  "operation": {
    "argumentDefinitions": [
      (v1/*: any*/),
      (v2/*: any*/),
      (v0/*: any*/)
    ],
    "kind": "Operation",
    "name": "bookingSearchQuery",
    "selections": (v3/*: any*/)
  },
  "params": {
    "cacheID": "2858c665437c7c16ab9b41b8a1e08aec",
    "id": null,
    "metadata": {},
    "name": "bookingSearchQuery",
    "operationKind": "query",
    "text": "query bookingSearchQuery(\n  $page: Int!\n  $pageSize: Int!\n  $filters: BookingFiltersInput\n) {\n  searchBookings(page: $page, pageSize: $pageSize, filters: $filters) {\n    items {\n      iBookingID\n      vcPIN\n      vcFreeUserID\n      nEngineID\n      nBookingTypeID\n      vcConfirmationID\n      nBookingStatusID\n      vcClientName\n      vcLiftCity\n      vcFinalDestination\n      nNumberOfPsgrs\n      vcGDS\n      vcVendor\n      vcVendorAddress\n      vcVendorCityState\n      vcVendorPostalCode\n      vcVendorNumber\n      vcVendorFax\n      dBooked\n      dTravelStartDate\n      dTravelEndDate\n      nTotalCharges\n      nAdminFee\n      nCommissionRate\n      nTotalCommission\n      bIATANQual\n      nInteleTravelCommission\n      nAgentCommission\n      nVendorCheckID\n      vcVendorCheckNum\n      dVendorPaid\n      bInTheBank\n      vcITCheckNumber\n      dAgentPaid\n      vcCancelConfirmation\n      txtComments\n      nCheckRunID\n      dCheckRunDate\n      bActive\n      dModified\n      dCreated\n      nUserModifiedID\n      nUserCreatedID\n      dPending\n      bNonCommissionable\n      dECS\n      nOrderID\n      nSegmentID\n      totalChargesCurrency\n      nSupplierID\n      nDepositAmount\n      nProcessingFee\n      vcconfirmationidnew\n      nSalesContactID\n      vcVendorID\n      vcTripID\n    }\n    total\n    totalPages\n    page\n    pageSize\n  }\n}\n"
  }
};
})();

(node as any).hash = "76eae22cb530e9c94a9bbf1b1ab1bf41";

export default node;
