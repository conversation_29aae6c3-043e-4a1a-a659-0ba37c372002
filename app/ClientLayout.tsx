'use client'

import { Suspense } from 'react'
import { loadQuery } from 'react-relay'
import { getEnvironment } from './lib/relay'
import { SearchWrapper } from '@/app/components/booking-search/search-wrapper'
import { BOOKING_SEARCH_QUERY } from './graphql/queries/booking-search'
import type { bookingSearchQuery } from './__generated__/bookingSearchQuery.graphql'

interface ClientLayoutProps {
  children: React.ReactNode
  searchParams: { [key: string]: string }
}

export function ClientLayout({ children, searchParams }: ClientLayoutProps) {
  return (
    <div className="flex min-h-screen flex-col">
      <div className="flex-1">
        <div className="border-t">
          <div className="bg-background">
            <div className="grid lg:grid-cols-5">
              {children}
              <div className="col-span-3 lg:col-span-4 lg:border-l">
                <div className="h-full px-4 py-6 lg:px-8">
                  <Suspense fallback={<div>Loading...</div>}>
                    <SearchWrapper searchParams={searchParams} />
                  </Suspense>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
