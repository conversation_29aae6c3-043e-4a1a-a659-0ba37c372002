export interface MetaData {
  meta_key: string;
  meta_value: string | null;
}

export interface Lead {
  lead_id: string;
  claimed: boolean;
  travel_type: string;
  created_date: string;
  meta_data: MetaData[];
}

export interface LeadFilters {
  agent_id?: string;
  travel_type?: string;
  destination?: string;
  departure_state?: string;
  departure_city?: string;
  travel_duration?: string;
  claimed?: boolean;
  start_date?: string;
  end_date?: string;
}

export interface LeadMetaData {
  meta_key: string;
  meta_value: string;
}

export interface ViewedBy {
  agent_id: string;
  viewed_leads_id: string;
}

export interface GetActiveLeadsResponse {
  getActiveLeads: Lead[];
} 