export type Operator = {
  value: string
  label: string
}

export type SearchField = {
  name: string
  key: string
  label: string
  type: 'text' | 'number' | 'date' | 'select' | 'boolean' | 'money'
  operators: Operator[]
  options?: { value: string; label: string }[]
}

export interface SearchCriterion {
  id: string
  field: SearchField
  operator: Operator
  value: string | number
}

export interface SavedSearch {
  id: string
  name: string
  criteria: SearchCriterion[]
  isShared: boolean
  url: string
  createdAt: string
  updatedAt: string
} 