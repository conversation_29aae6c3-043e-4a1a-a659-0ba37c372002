export interface SearchField {
  id: string
  key: string
  name: string
  label: string
  type: 'number' | 'boolean' | 'select' | 'text' | 'date' | 'money'
  valueType?: 'string' | 'number' | 'boolean'
  operators: SearchOperator[]
  options?: {
    label: string
    value: string
  }[]
  maxLength?: number
  prefix?: string
  placeholder?: string
}

export interface SearchOperator {
  label: string
  value: string
}

export interface SearchCriterion {
  id: string
  field?: SearchField
  operator?: {
    value: string
    label: string
  }
  value?: string | number | boolean
}