'use server'

import { cookies } from 'next/headers'
import { revalidatePath } from 'next/cache'
import { GetActiveLeadsResponse } from "@/types/leads"

interface FetchLeadsParams {
  page: number
  perPage: number
}

async function fetchLeads({ page, perPage }: FetchLeadsParams) {
  const cookieStore = cookies()
  const token = cookieStore.get('auth_token')?.value
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

  try {
    const response = await fetch(`${apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` })
      },
      cache: 'no-store',
      body: JSON.stringify({
        query: `
          query GetActiveLeads {
            getActiveLeads(agent_id: "1", page: ${page}, limit: ${perPage}) {
              lead_id
              travel_type
              created_date
              meta_data {
                meta_key
                meta_value
              }
              viewed_by {
                agent_id
                viewed_leads_id
              }
            }
          }
        `
      })
    })

    if (!response.ok) {
      throw new Error('Failed to fetch leads')
    }

    const data = await response.json()
    revalidatePath('/leads')
    return data.data
  } catch (error) {
    console.error('Error fetching leads:', error)
    throw error
  }
}

export async function deleteLead(id: string) {
  const cookieStore = cookies()
  const token = cookieStore.get('auth_token')?.value
  const apiUrl = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000'

  try {
    const response = await fetch(`${apiUrl}/graphql`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` })
      },
      body: JSON.stringify({
        query: `
          mutation DeleteLead($leadId: ID!) {
            deleteLead(lead_id: $leadId) {
              success
              message
            }
          }
        `,
        variables: {
          leadId: id
        }
      })
    })

    if (!response.ok) {
      throw new Error('Failed to delete lead')
    }

    const data = await response.json()
    
    if (data.errors) {
      throw new Error(data.errors[0].message)
    }

    revalidatePath("/leads")
    return { success: true }
  } catch (error) {
    console.error('Error deleting lead:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : "Failed to delete lead" 
    }
  }
}

export { fetchLeads }