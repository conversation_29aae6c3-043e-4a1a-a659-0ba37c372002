import { notFound } from 'next/navigation'
import { Skeleton } from "@/components/ui/skeleton"

interface NewsletterPageProps {
  params: {
    id: string
  }
}

interface Newsletter {
  id: string
  title: string
  date: string
  content?: string
}

// This would come from your API/database
const NEWSLETTERS: Newsletter[] = [
  {
    id: '2024-03',
    title: 'March Newsletter',
    date: '2024-03-01',
    content: 'March 2024 newsletter content...'
  },
  {
    id: '2024-02',
    title: 'February Insights',
    date: '2024-02-12',
    content: 'February 2024 newsletter content...'
  },
  {
    id: '2024-01',
    title: 'January Update',
    date: '2024-01-15',
    content: 'January 2024 newsletter content...'
  },
  {
    id: '2023-12',
    title: 'December Roundup',
    date: '2023-12-15',
    content: 'December 2023 newsletter content...'
  },
  {
    id: '2023-11',
    title: 'November Digest',
    date: '2023-11-10',
    content: 'November 2023 newsletter content...'
  },
  {
    id: '2023-10',
    title: 'October Update',
    date: '2023-10-05',
    content: 'October 2023 newsletter content...'
  },
  {
    id: '2022-12',
    title: 'Year End Special',
    date: '2022-12-20',
    content: 'December 2022 newsletter content...'
  },
  {
    id: '2022-11',
    title: 'November Newsletter',
    date: '2022-11-15',
    content: 'November 2022 newsletter content...'
  }
]

export default function NewsletterPage({ params }: NewsletterPageProps) {
  const newsletter = NEWSLETTERS.find(n => n.id === params.id)

  if (!newsletter) {
    notFound()
  }

  return (
    <article className="max-w-3xl mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-4">{newsletter.title}</h1>
      <time className="text-muted-foreground mb-8 block">
        {new Date(newsletter.date).toLocaleDateString()}
      </time>
      <div className="prose prose-slate max-w-none">
        {newsletter.content || 'Newsletter content not available.'}
      </div>
    </article>
  )
} 