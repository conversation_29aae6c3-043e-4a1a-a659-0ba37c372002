"use client"

import { useState } from 'react'
import { Button } from '@/app/components/ui/button'
import { useRouter } from 'next/navigation'
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '@/app/components/ui/accordion'
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"

interface Newsletter {
  id: string
  title: string
  date: string
  description: string
}

// Current newsletters
const CURRENT_NEWSLETTERS: Newsletter[] = [
  {
    id: "2024-03",
    title: "Summer Travel Trends 2024",
    date: "2024-03-01",
    description: "Discover the hottest destinations and travel trends for summer 2024."
  },
  {
    id: "2024-02",
    title: "February Travel Insights",
    date: "2024-02-15",
    description: "Latest updates on travel industry developments and exclusive deals."
  },
  {
    id: "2024-01",
    title: "New Year Travel Guide",
    date: "2024-01-10",
    description: "Start your year with the best travel planning tips and destinations."
  }
]

// Archive newsletters grouped by year
const ARCHIVE_NEWSLETTERS: { year: number; newsletters: Newsletter[] }[] = [
  {
    year: 2023,
    newsletters: [
      {
        id: "2023-12",
        title: "Year End Special",
        date: "2023-12-15",
        description: "A look back at 2023's travel highlights"
      },
      {
        id: "2023-11",
        title: "Holiday Travel Guide",
        date: "2023-11-10",
        description: "Your complete guide to holiday season travel"
      }
    ]
  },
  {
    year: 2022,
    newsletters: [
      {
        id: "2022-12",
        title: "Winter Destinations",
        date: "2022-12-20",
        description: "Explore the best winter getaways"
      }
    ]
  }
]

export function NewsletterArchive() {
  const [activeYear, setActiveYear] = useState<string>('2023')
  const router = useRouter()

  return (
    <div className="w-full space-y-8">
      {/* Current Newsletters */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {CURRENT_NEWSLETTERS.map((newsletter) => (
          <Card 
            key={newsletter.id} 
            className="hover:bg-muted/50 transition-colors cursor-pointer"
            onClick={() => router.push(`/newsletters/${newsletter.id}`)}
          >
            <CardHeader>
              <CardTitle className="line-clamp-2">{newsletter.title}</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-muted-foreground mb-2">
                {new Date(newsletter.date).toLocaleDateString()}
              </p>
              <p className="text-sm text-muted-foreground line-clamp-2">
                {newsletter.description}
              </p>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Archive Section */}
      <div className="mt-8">
        <h2 className="text-lg font-semibold mb-4">Newsletter Archive</h2>
        <Accordion type="single" value={activeYear} onValueChange={setActiveYear}>
          {ARCHIVE_NEWSLETTERS.map((yearSection) => (
            <AccordionItem
              key={yearSection.year}
              value={yearSection.year.toString()}
              className="border mb-4"
            >
              <AccordionTrigger className="px-4 py-2 hover:bg-muted">
                <h3 className="text-lg font-semibold">
                  {yearSection.year} Issues
                </h3>
              </AccordionTrigger>
              <AccordionContent className="px-4 py-2">
                <div className="space-y-2">
                  {yearSection.newsletters.map((newsletter) => (
                    <Button
                      key={newsletter.id}
                      variant="ghost"
                      className="w-full justify-start text-left"
                      onClick={() => router.push(`/newsletters/${newsletter.id}`)}
                    >
                      {newsletter.title} - {new Date(newsletter.date).toLocaleDateString()}
                    </Button>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          ))}
        </Accordion>
      </div>
    </div>
  )
} 