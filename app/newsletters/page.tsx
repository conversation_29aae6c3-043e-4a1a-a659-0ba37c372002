import { Suspense } from 'react'
import { NewsletterArchive } from './newsletter-archive'
import { Skeleton } from '@/app/components/ui/skeleton'
import { Breadcrumb } from '@/app/components/ui/breadcrumb'

export const metadata = {
  title: 'Newsletter Archive',
  description: 'Access our newsletter archive with the latest travel industry updates and exclusive deals'
}

function NewsletterSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-12 w-[250px]" />
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <Skeleton key={i} className="h-[200px] rounded-lg" />
        ))}
      </div>
    </div>
  )
}

export default function NewslettersPage() {
  return (
    <div className="p-6 space-y-6">
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Newsletter Archive</h1>
      </div>
      <Suspense fallback={<NewsletterSkeleton />}>
        <NewsletterArchive />
      </Suspense>
    </div>
  )
} 