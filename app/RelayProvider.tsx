'use client'

import { ReactNode } from 'react'
import { RelayEnvironmentProvider } from 'react-relay'
import environment from './relay-environment'

interface RelayProviderProps {
  children: ReactNode
}

export function RelayProvider({ children }: RelayProviderProps) {
  console.log('RelayProvider: Initializing with environment', environment);
  
  return (
    <RelayEnvironmentProvider environment={environment}>
      {children}
    </RelayEnvironmentProvider>
  )
}
