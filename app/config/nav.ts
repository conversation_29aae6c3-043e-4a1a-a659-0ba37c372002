import {
  CalendarDays,
  CalendarCheck,
  Calendar<PERSON>lock,
  BookOpen,
  Users2,
  UserCog,
} from "lucide-react"
import { getTodayBookingUrl } from "@/app/lib/utils"

export const bookingNavItems = [
  {
    title: "All Bookings",
    href: "/bookings",
    icon: CalendarDays,
  },
  {
    title: "Today's Bookings",
    href: getTodayBookingUrl(),
    icon: CalendarCheck,
  },
  {
    title: "Pending Bookings",
    href: "/bookings?nBookingStatusID=%7B%22operator%22%3A%22equals%22%2C%22value%22%3A%223%22%7D&page=1&pageSize=10",
    icon: CalendarClock,
  },
  {
    title: "Free User Bookings",
    href: "/bookings/free-user",
    icon: Users2,
  },
  {
    title: "Agent Bookings",
    href: "/bookings/agent",
    icon: UserCog,
  },
]

export const mainNav = [
  {
    title: "Bookings",
    icon: BookOpen,
    items: bookingNavItems,
  },
]

export const sidebarNav = [
  {
    title: "Bookings",
    href: "/bookings",
    icon: "calendar",
    items: [
      {
        title: "All Bookings",
        href: "/bookings",
      },
      {
        title: "Today's Bookings",
        href: getTodayBookingUrl(),
      },
      {
        title: "Pending Bookings",
        href: "/bookings?nBookingStatusID=%7B%22operator%22%3A%22equals%22%2C%22value%22%3A%223%22%7D&page=1&pageSize=10",
      },
      {
        title: "Free User Bookings",
        href: "/bookings/free-user",
      },
      {
        title: "Agent Bookings",
        href: "/bookings/agent",
      }
    ]
  },
] 