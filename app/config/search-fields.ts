import { SearchField } from "@/app/types/search-criterion"

export const SEARCH_FIELDS: SearchField[] = [
  {
    id: "bookingId",
    name: "bookingId",
    key: "iBookingID",
    label: "Booking ID",
    type: "number",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "confirmationId",
    name: "confirmationId",
    key: "vcConfirmationID",
    label: "Confirmation ID",
    type: "text",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "travelStartDate",
    name: "travelStartDate",
    key: "dTravelStartDate",
    label: "Travel Start Date",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "bookingStatus",
    name: "bookingStatus",
    key: "nBookingStatusID",
    label: "Booking Status",
    type: "select",
    valueType: "string",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '1', label: "Active" },
      { value: '2', label: "Cancelled" },
      { value: '3', label: "Pending" }
    ]
  },
  {
    id: "pin",
    name: "pin",
    key: "vcPIN",
    label: "PIN",
    type: "text",
    maxLength: 32,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "bookedStartDate",
    name: "bookedStartDate",
    key: "dBooked",
    label: "Booked On (From)",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "bookedEndDate",
    name: "bookedEndDate",
    key: "dBooked",
    label: "Booked On (To)",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "newEngine",
    name: "newEngine",
    key: "nEngineID",
    label: "New Engine",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '1', label: "Allianz" },
      { value: '2', label: "Apple" }
    ]
  },
  {
    id: "oldEngine",
    name: "oldEngine",
    key: "nEngineID",
    label: "Old Engine",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '3', label: "air" },
      { value: '4', label: "Apple BookIT" }
    ]
  },
  {
    id: "bookingType",
    name: "bookingType",
    key: "bookingType",
    label: "Type",
    type: "select",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: "0", label: "All" },
      { value: "Air", label: "Air" },
      { value: "Car", label: "Car" },
      { value: "Hotel", label: "Hotel" },
      { value: "Cruise", label: "Cruise" },
      { value: "Vacation", label: "Vacation" },
      { value: "Insurance", label: "Insurance" },
      { value: "Fee", label: "Fee" },
      { value: "Passport/Visa", label: "Passport/Visa" },
      { value: "Events", label: "Events" },
      { value: "Rail", label: "Rail" },
      { value: "Activities", label: "Activities" }
    ]
  },
  {
    id: "nBookingStatusID",
    name: "nBookingStatusID",
    key: "nBookingStatusID",
    label: "Status",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '1', label: "Booked" },
      { value: '2', label: "Canceled" },
      { value: '3', label: "Pending" },
      { value: '4', label: "Needs Attention" }
    ]
  },
  {
    id: "commissionable",
    name: "commissionable",
    key: "bNonCommissionable",
    label: "Commissionable",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '0', label: "Commissionable" },
      { value: '1', label: "Non-Commissionable" }
    ]
  },
  {
    id: "active",
    name: "active",
    key: "bActive",
    label: "Active",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '1', label: "Active" },
      { value: '0', label: "Inactive" }
    ]
  },
  {
    id: "travelerName",
    name: "travelerName",
    key: "travelerName",
    label: "Traveler Name",
    type: "text",
    maxLength: 32,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "totalChargesStart",
    name: "totalChargesStart",
    key: "totalChargesStart",
    label: "Total Charges From",
    type: "number",
    prefix: '$',
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "totalChargesEnd",
    name: "totalChargesEnd",
    key: "totalChargesEnd",
    label: "Total Charges To",
    type: "number",
    prefix: '$',
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "liftCity",
    name: "liftCity",
    key: "vcLiftCity",
    label: "Lift City",
    type: "text",
    maxLength: 64,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "destination",
    name: "destination",
    key: "vcFinalDestination",
    label: "Destination",
    type: "text",
    maxLength: 64,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "vendor",
    name: "vendor",
    key: "vcVendor",
    label: "Vendor",
    type: "text",
    maxLength: 255,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "gds",
    name: "gds",
    key: "vcGDS",
    label: "GDS",
    type: "select",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: "0", label: "All" },
      { value: "Direct", label: "Direct" },
      // ... other GDS options
    ]
  },
  {
    id: "vendorCheckNum",
    name: "vendorCheckNum",
    key: "vcVendorCheckNum",
    label: "Vendor Check #",
    type: "text",
    maxLength: 30,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "itCheck",
    name: "itCheck",
    key: "vcITCheckNumber",
    label: "IT Check #",
    type: "text",
    maxLength: 32,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "inTheBank",
    name: "inTheBank",
    key: "bInTheBank",
    label: "In The Bank",
    type: "select",
    valueType: "number",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: '0', label: "All" },
      { value: '1', label: "Yes" },
      { value: '0', label: "No" }
    ]
  },
  {
    id: "atolTrips",
    name: "atolTrips",
    key: "atolTrips",
    label: "ATOL Trips",
    type: "boolean",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "enteredStartDate",
    name: "enteredStartDate",
    key: "enteredStartDate",
    label: "Entered On (From)",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "enteredEndDate",
    name: "enteredEndDate",
    key: "enteredEndDate",
    label: "Entered On (To)",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "commissionRateStart",
    name: "commissionRateStart",
    key: "commissionRateStart",
    label: "Commission % From",
    type: "number",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "commissionRateEnd",
    name: "commissionRateEnd",
    key: "commissionRateEnd",
    label: "Commission % To",
    type: "number",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "totalCommissionStart",
    name: "totalCommissionStart",
    key: "totalCommissionStart",
    label: "Total Commission From",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "totalCommissionEnd",
    name: "totalCommissionEnd",
    key: "totalCommissionEnd",
    label: "Total Commission To",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "itCommissionStart",
    name: "itCommissionStart",
    key: "itCommissionStart",
    label: "IT Commission From",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "itCommissionEnd",
    name: "itCommissionEnd",
    key: "itCommissionEnd",
    label: "IT Commission To",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "agentCommissionStart",
    name: "agentCommissionStart",
    key: "agentCommissionStart",
    label: "Agent Commission From",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "agentCommissionEnd",
    name: "agentCommissionEnd",
    key: "agentCommissionEnd",
    label: "Agent Commission To",
    type: "number",
    prefix: "$",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "greaterThan", label: "Greater than" },
      { value: "lessThan", label: "Less than" }
    ]
  },
  {
    id: "vendorPaidStartDate",
    name: "vendorPaidStartDate",
    key: "vendorPaidStartDate",
    label: "Vendor Paid From",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "vendorPaidEndDate",
    name: "vendorPaidEndDate",
    key: "vendorPaidEndDate",
    label: "Vendor Paid To",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "agentPaidStartDate",
    name: "agentPaidStartDate",
    key: "agentPaidStartDate",
    label: "Agent Paid From",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "agentPaidEndDate",
    name: "agentPaidEndDate",
    key: "agentPaidEndDate",
    label: "Agent Paid To",
    type: "date",
    operators: [
      { value: "equals", label: "Equals" },
      { value: "before", label: "Before" },
      { value: "after", label: "After" }
    ]
  },
  {
    id: "createdById",
    name: "createdById",
    key: "nUserCreatedID",
    label: "Created By",
    type: "select",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: "0", label: "Any" },
      // Add your user options here
    ]
  },
  {
    id: "modifiedById",
    name: "modifiedById",
    key: "nUserModifiedID",
    label: "Last Modified By",
    type: "select",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: "0", label: "Any" },
      // Add your user options here
    ]
  },
  {
    id: "groupId",
    name: "groupId",
    key: "groupId",
    label: "Group",
    type: "select",
    operators: [{ value: "equals", label: "Equals" }],
    options: [
      { value: "0", label: "All" },
      // Add your group options here
    ]
  },
  {
    id: "clientName",
    name: "clientName",
    key: "vcClientName",
    label: "Client Name",
    type: "text",
    maxLength: 255,
    operators: [
      { value: "equals", label: "Equals" },
      { value: "contains", label: "Contains" }
    ]
  },
  {
    id: "freeUserPin",
    name: "freeUserPin",
    key: "freeUserPin",
    label: "Free User Pin",
    type: "text",
    operators: [
      { label: "Equals", value: "equals" },
      { label: "Contains", value: "contains" },
      { label: "Starts with", value: "startsWith" },
      { label: "Ends with", value: "endsWith" }
    ],
    placeholder: "Enter Free User Pin"
  }
] 