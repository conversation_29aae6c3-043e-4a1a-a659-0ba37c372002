'use client'

import { LeadsTable } from '../components/leads/leads-table'
import { LeadsFilter } from '../components/leads/leads-filter'
import { Suspense, useEffect, useState, useCallback, useMemo } from 'react'
import { TableSkeleton } from '../components/ui/table-skeleton'
import { useSearchParams, useRouter } from 'next/navigation'
import { ErrorBoundary } from 'react-error-boundary'
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
interface MetaData {
  meta_key: string
  meta_value: string | null
}

export interface Lead {
  lead_id: string
  claimed: boolean
  travel_type: string
  created_date: string
  meta_data: MetaData[]
}

interface FilterState {
  agentId: string
  travelType: string
  destination: string
  departureState: string
  departureCity: string
  duration: string
  claimedStatus: string
  bookingStatus: string
  startDate?: Date | undefined
  endDate?: Date | undefined
}

export interface LeadFilters {
  agent_id?: string
  travel_type?: string
  destination?: string
  departure_state?: string
  departure_city?: string
  travel_duration?: string
  claimed?: boolean
  booking_status?: string
  start_date?: string
  end_date?: string
}

export interface LeadQueryVariables {
  agent_id: string
  travel_type: string
  destination: string
  departure_state: string
  departure_city: string
  travel_duration: string
  claimed: boolean
  booking_status: string
  start_date: string
  end_date: string
}

const defaultFilters: LeadFilters = {
  agent_id: '',
  travel_type: '',
  destination: '',
  departure_state: '',
  departure_city: '',
  travel_duration: '',
  claimed: false,
  booking_status: '',
  start_date: '',
  end_date: '',
}

function ErrorFallback({ error }: { error: Error }) {
  return (
    <div className="p-6 text-red-500">
      <h2 className="text-lg font-semibold">Something went wrong:</h2>
      <pre className="mt-2 text-sm">{error.message}</pre>
    </div>
  )
}

export default function LeadsPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const [isMounted, setIsMounted] = useState(false)
  
  const page = Number(searchParams.get('page')) || 1
  const perPage = Number(searchParams.get('per_page')) || 10

  const currentFilters = useMemo(() => ({
    agent_id: searchParams.get('agent_id') || defaultFilters.agent_id,
    travel_type: searchParams.get('travel_type') || defaultFilters.travel_type,
    destination: searchParams.get('destination') || defaultFilters.destination,
    departure_state: searchParams.get('departure_state') || defaultFilters.departure_state,
    departure_city: searchParams.get('departure_city') || defaultFilters.departure_city,
    travel_duration: searchParams.get('travel_duration') || defaultFilters.travel_duration,
    claimed: searchParams.get('claimed') === 'true',
    booking_status: searchParams.get('booking_status') || defaultFilters.booking_status,
    start_date: searchParams.get('start_date') || defaultFilters.start_date,
    end_date: searchParams.get('end_date') || defaultFilters.end_date,
  }), [searchParams])

  // Debug effect to log filter changes
  useEffect(() => {
    console.log('Filters changed:', currentFilters)
  }, [currentFilters])

  const tableKey = useMemo(() => {
    try {
      const filterString = Object.entries(currentFilters)
        .map(([key, value]) => `${key}:${value}`)
        .join('-')
      return `${page}-${perPage}-${filterString}`
    } catch (error) {
      console.error('Error generating table key:', error)
      return `${page}-${perPage}`
    }
  }, [page, perPage, currentFilters])

  const handleFilterChange = useCallback((filters: FilterState) => {
    try {
      console.log('Filter change requested:', filters)
      const baseUrl = window.location.pathname
      const params = new URLSearchParams()

      params.set('page', '1')
      params.set('per_page', String(perPage))

      // Convert FilterState to LeadFilters with proper date formatting
      const apiFilters: LeadFilters = {
        agent_id: filters.agentId,
        travel_type: filters.travelType,
        destination: filters.destination,
        departure_state: filters.departureState,
        departure_city: filters.departureCity,
        travel_duration: filters.duration,
        claimed: filters.claimedStatus === 'claimed',
        booking_status: filters.bookingStatus,
        start_date: filters.startDate?.toISOString().split('T')[0],
        end_date: filters.endDate?.toISOString().split('T')[0],
      }

      Object.entries(apiFilters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          if (value !== defaultFilters[key as keyof LeadFilters]) {
            params.set(key, String(value))
          }
        }
      })

      const newUrl = `${baseUrl}?${params.toString()}`
      console.log('Updating URL to:', newUrl)
      router.replace(newUrl, { scroll: false })
    } catch (error) {
      console.error('Error applying filters:', error)
    }
  }, [router, perPage])

  const handleResetFilters = useCallback(() => {
    try {
      console.log('Resetting filters')
      const baseUrl = window.location.pathname
      const params = new URLSearchParams()
      params.set('page', '1')
      params.set('per_page', String(perPage))
      router.replace(`${baseUrl}?${params.toString()}`, { scroll: false })
    } catch (error) {
      console.error('Error resetting filters:', error)
    }
  }, [router, perPage])

  useEffect(() => {
    setIsMounted(true)
  }, [])

  if (!isMounted) {
    return <TableSkeleton />
  }

  return (
    <div className="p-6 space-y-6"> 
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold tracking-tight">Leads</h1>
      </div>
      <ErrorBoundary FallbackComponent={ErrorFallback}>
        <LeadsFilter 
          onFilterChange={handleFilterChange}
          onReset={handleResetFilters}
          initialFilters={currentFilters}
          defaultFilters={defaultFilters}
        />
        <div className="relative">
          <Suspense fallback={<TableSkeleton />}>
            <LeadsTable 
              key={tableKey}
              currentPage={page} 
              perPage={perPage}
              filters={currentFilters}
            />
          </Suspense>
        </div>
      </ErrorBoundary>
    </div>
  )
} 