import { Suspense } from "react"
import { FiltersWrapper } from "@/app/components/transactions/filters-wrapper"
import { TransactionsTable } from "@/app/components/transactions/transactions-table"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { fetchTransactions } from "@/app/lib/api/transactions"
import { useSearchParams } from "next/navigation"

interface TransactionsPageProps {
  searchParams: { [key: string]: string | string[] | undefined }
}

export default async function TransactionsPage({
  searchParams,
}: TransactionsPageProps) {
  const page = Number(searchParams.page) || 1
  const limit = Number(searchParams.limit) || 10
  
  const transactionsData = await fetchTransactions({ page, limit })

  return (
    <main className="mx-auto p-4 md:p-6">
      <Breadcrumb />
      <h1 className="text-2xl font-bold mb-6">Transactions</h1>
      
      <div className="space-y-6">
        <Suspense fallback={<div>Loading filters...</div>}>
          <FiltersWrapper />
        </Suspense>
          
        <div className="space-y-6">
          <h1 className="text-2xl font-semibold">Transaction List</h1>
          <Suspense fallback={<div>Loading transactions...</div>}>
            <TransactionsTable 
              initialData={transactionsData}
            />
          </Suspense>
        </div>
      </div>
    </main>
  )
} 