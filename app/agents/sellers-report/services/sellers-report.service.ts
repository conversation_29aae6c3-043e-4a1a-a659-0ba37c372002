import { gql } from 'graphql-request'

export interface SellersReportParams {
  dateRange: {
    startDate: string
    endDate: string
    useBookingDate: boolean
  }
  filters: Record<string, unknown>
  output: {
    limit: number
    includeTransactions: boolean
    includeBookings: boolean
  }
}

export interface SellersReportAgent {
  pin: string
  name: string
  totalBookings: number
  totalCommission: number
  averageCommission: number
  largestBooking: number
}

export interface SellersReportSummary {
  totalAgents: number
  totalCommission: number
  averageCommission: number
  topVendors: Array<{
    name: string
    bookings: number
    commission: number
  }>
}

export interface SellersReportResponse {
  topSellersReport: {
    agents: SellersReportAgent[]
    summary: SellersReportSummary
  }
}

export const TOP_SELLERS_REPORT_QUERY = gql`
  query TopSellersReport {
    topSellersReport(
      params: {
        dateRange: {
          startDate: "2023-01-01T00:00:00Z"
          endDate: "2023-12-31T23:59:59Z"
          useBookingDate: true
        }
        filters: {
          # Empty object for no filters
        }
        output: {
          limit: 10
          includeTransactions: false
          includeBookings: false
        }
      }
    ) {
      agents {
        pin
        name
        totalBookings
        totalCommission
        averageCommission
        largestBooking
      }
      summary {
        totalAgents
        totalCommission
        averageCommission
        topVendors {
          name
          bookings
          commission
        }
      }
    }
  }
` 