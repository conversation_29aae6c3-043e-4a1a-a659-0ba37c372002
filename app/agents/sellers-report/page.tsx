import { Metadata } from "next"
import { SellersReportFilters } from "./components/sellers-report-filters"
import { SellersReportTable } from "./components/sellers-report-table"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { Suspense } from "react"
import { TableSkeleton } from "@/components/ui/table-skeleton"
import { SellersReportSummary } from "./components/sellers-report-summary"

export const metadata: Metadata = {
  title: "Sellers Report | Agents",
  description: "View and analyze seller performance metrics"
}

export default function SellersReportPage() {
  return (
    <div className="flex flex-col gap-4 p-6">
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">Sellers Report</h1>
      </div>
      <Suspense fallback={<TableSkeleton />}>
        <SellersReportSummary />
      </Suspense>
      <SellersReportFilters />
      <Suspense fallback={<TableSkeleton />}>
        <SellersReportTable />
      </Suspense>
    </div>
  )
} 