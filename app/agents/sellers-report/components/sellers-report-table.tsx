"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { formatCurrency } from "@/lib/utils"
import { type SellersReportAgent, TOP_SELLERS_REPORT_QUERY, type SellersReportSummary } from "../services/sellers-report.service"
import { useEffect, useState } from "react"
import { graphqlClient } from "@/lib/graphql-client"
import { TableSkeleton } from "@/components/ui/table-skeleton"

interface Agent {
  pin: string
  name: string
  totalBookings: number
  totalCommission: number
  averageCommission: number
  largestBooking: number
}

interface TopSellersReportResponse {
  topSellersReport: {
    agents: Agent[]
    summary: {
      totalAgents: number
      totalCommission: number
      averageCommission: number
      topVendors: any[] // Update this type if you have a more specific type for vendors
    }
  }
}

export function SellersReportTable() {
  const [data, setData] = useState<Agent[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await graphqlClient.request<TopSellersReportResponse>(TOP_SELLERS_REPORT_QUERY)
        setData(response.topSellersReport.agents)
      } catch (error) {
        console.error('Failed to fetch sellers report:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  if (isLoading) {
    return <TableSkeleton />
  }

  return (
    <div className="border rounded-none">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Agent PIN</TableHead>
            <TableHead>Name</TableHead>
            <TableHead className="text-right">Total Bookings</TableHead>
            <TableHead className="text-right">Total Commission</TableHead>
            <TableHead className="text-right">Average Commission</TableHead>
            <TableHead className="text-right">Largest Booking</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((agent) => (
            <TableRow key={agent.pin}>
              <TableCell>{agent.pin}</TableCell>
              <TableCell>{agent.name}</TableCell>
              <TableCell className="text-right">{agent.totalBookings ?? 'N/A'}</TableCell>
              <TableCell className="text-right">
                {agent.totalCommission ? formatCurrency(agent.totalCommission) : 'N/A'}
              </TableCell>
              <TableCell className="text-right">
                {agent.averageCommission ? formatCurrency(agent.averageCommission) : 'N/A'}
              </TableCell>
              <TableCell className="text-right">
                {agent.largestBooking ? formatCurrency(agent.largestBooking) : 'N/A'}
              </TableCell>
            </TableRow>
          ))}
          {data.length === 0 && (
            <TableRow>
              <TableCell colSpan={6} className="text-center">
                No data available
              </TableCell>
            </TableRow>
          )}
        </TableBody>
      </Table>
    </div>
  )
} 