"use client"

import { formatCurrency } from "@/lib/utils"
import { type SellersReportSummary as SummaryType } from "../services/sellers-report.service"
import { Card, CardContent } from "@/components/ui/card"
import { useState, useEffect } from "react"
import { graphqlClient } from "@/lib/graphql-client"
import { TOP_SELLERS_REPORT_QUERY } from "../services/sellers-report.service"
import { Skeleton } from "@/components/ui/skeleton"

interface TopSellersReportResponse {
  topSellersReport: {
    summary: SummaryType
  }
}

interface SummaryProps {
  data: SummaryType | null
  isLoading: boolean
}

function SummaryCard({ title, value, isLoading }: { title: string; value: string | number; isLoading: boolean }) {
  return (
    <Card className="rounded-none">
      <CardContent className="pt-6">
        {isLoading ? (
          <>
            <Skeleton className="h-8 w-24 mb-2" />
            <Skeleton className="h-4 w-32" />
          </>
        ) : (
          <>
            <div className="text-2xl font-bold">{value}</div>
            <p className="text-sm text-muted-foreground">{title}</p>
          </>
        )}
      </CardContent>
    </Card>
  )
}

function SummaryContent({ data, isLoading }: SummaryProps) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
      <SummaryCard
        title="Total Agents"
        value={data?.totalAgents ?? 0}
        isLoading={isLoading}
      />
      <SummaryCard
        title="Total Commission"
        value={data ? formatCurrency(data.totalCommission) : '$0.00'}
        isLoading={isLoading}
      />
      <SummaryCard
        title="Average Commission"
        value={data ? formatCurrency(data.averageCommission) : '$0.00'}
        isLoading={isLoading}
      />
    </div>
  )
}

export function SellersReportSummary() {
  const [summary, setSummary] = useState<SummaryType | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    async function fetchData() {
      try {
        const response = await graphqlClient.request<TopSellersReportResponse>(TOP_SELLERS_REPORT_QUERY)
        setSummary(response.topSellersReport.summary)
      } catch (error) {
        console.error('Failed to fetch sellers report summary:', error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  return <SummaryContent data={summary} isLoading={isLoading} />
} 