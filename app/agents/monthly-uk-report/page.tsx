import { Metada<PERSON> } from "next"
import { MonthlyUkTable } from "./components/monthly-uk-table"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/app/components/ui/card"
import { Users, Download, Mail } from "lucide-react"
import { Button } from "@/app/components/ui/button"
import { EmailReportDialog } from "./components/email-report-dialog"
import { DateDisplay } from "./components/date-display"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { graphqlClient } from "@/app/lib/graphql-client"
import { Suspense } from 'react'
import { ErrorBoundaryClient } from "@/app/components/error-boundary"

export const metadata: Metadata = {
  title: "Monthly UK Agent Report | Agents",
  description: "View and export monthly UK agent registration data"
}

interface UKABTAReport {
  activeAgents: Array<{
    title: number
    firstName: string
    lastName: string
    dateOfBirth: string
    emailAddress: string
    businessPhone: string
    mobilePhone: string
    address1: string
    address2: string
    address3: string | null
    town: string
    county: string
    postcode: string
    website: string
    brand: string
    dateOfCommencement: string
    dateOfEnrollment: string
    pin: string
  }>
  canceledAgents: Array<any>
  amendments: Array<any>
  reportDate: string
  dateRange: {
    startDate: string
    endDate: string
  }
}

async function getUKABTAReport() {
  'use server'
  
  const query = `
    query GenerateUKABTAReport($params: UKABTAReportParams!) {
      generateUKABTAReport(params: $params) {
        activeAgents {
          title
          firstName
          lastName
          dateOfBirth
          emailAddress
          businessPhone
          mobilePhone
          address1
          address2
          address3
          town
          county
          postcode
          website
          brand
          dateOfCommencement
          dateOfEnrollment
          pin
        }
        canceledAgents {
          title
          firstName
          lastName
          dateOfBirth
          emailAddress
          businessPhone
          mobilePhone
          address1
          address2
          address3
          town
          county
          postcode
          website
          brand
          dateOfCommencement
          dateOfEnrollment
          dateOfCancellation
          pin
        }
        amendments {
          oldFullName
          newFullName
          pin
          emailAddress
          changeType
          changeDate
        }
        reportDate
        dateRange {
          startDate
          endDate
        }
      }
    }
  `

  const currentDate = new Date()
  const startDate = new Date(currentDate.getFullYear(), currentDate.getMonth(), 1)
  const endDate = new Date(currentDate.getFullYear(), currentDate.getMonth() + 1, 0)

  const variables = {
    params: {
      dateRange: {
        startDate: startDate.toISOString().split('T')[0],
        endDate: endDate.toISOString().split('T')[0]
      },
      includeAmendments: true,
      includeCancellations: true,
      excludedPins: [],
    }
  }

  try {
    console.log('Fetching UK ABTA report...', {
      url: process.env.NEXT_PUBLIC_GRAPHQL_URL,
      variables
    })

    const response = await graphqlClient.request<{ generateUKABTAReport: UKABTAReport }>(query, variables)
    console.log('Response received:', response)
    return response.generateUKABTAReport
  } catch (error) {
    console.error('Error fetching UK ABTA report:', error)
    throw error
  }
}

function LoadingFallback() {
  return (
    <div className="flex flex-col gap-4 p-6">
      <div className="h-8 bg-gray-200 animate-pulse rounded" />
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {[1, 2, 3].map((i) => (
          <Card key={i} className="rounded-none">
            <CardHeader className="flex flex-row items-center space-y-0 pb-2">
              <CardTitle className="text-md font-medium">
                <div className="h-4 w-24 bg-gray-200 animate-pulse rounded" />
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="h-8 w-16 bg-gray-200 animate-pulse rounded" />
            </CardContent>
          </Card>
        ))}
      </div>
      <div className="h-96 bg-gray-200 animate-pulse rounded" />
    </div>
  )
}

async function ReportContent() {
  const report = await getUKABTAReport()

  return (
    <>
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">Monthly UK Agent Report</h1>
        <div className="flex gap-2">
          <EmailReportDialog />
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Excel
          </Button>
        </div>
      </div>

      <DateDisplay startDate={report.dateRange.startDate} endDate={report.dateRange.endDate} />
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="rounded-none">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <CardTitle className="text-md font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Active Agents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{report.activeAgents.length}</div>
          </CardContent>
        </Card>

        <Card className="rounded-none">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <CardTitle className="text-md font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Canceled Agents
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{report.canceledAgents.length}</div>
          </CardContent>
        </Card>

        <Card className="rounded-none">
          <CardHeader className="flex flex-row items-center space-y-0 pb-2">
            <CardTitle className="text-md font-medium flex items-center gap-2">
              <Users className="h-4 w-4" />
              Amendments
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{report.amendments.length}</div>
          </CardContent>
        </Card>
      </div>

      <MonthlyUkTable 
        activeAgents={report.activeAgents}
        canceledAgents={report.canceledAgents}
        amendments={report.amendments}
      />
    </>
  )
}

export default function MonthlyUkReportPage() {
  return (
    <div className="flex flex-col gap-4 p-6">
      <Breadcrumb />
      <ErrorBoundaryClient>
        <Suspense fallback={<LoadingFallback />}>
          <ReportContent />
        </Suspense>
      </ErrorBoundaryClient>
    </div>
  )
} 