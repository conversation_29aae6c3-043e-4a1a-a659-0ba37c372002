"use client"

import { Card, CardContent } from "@/app/components/ui/card"
import { Calendar } from "lucide-react"

interface DateDisplayProps {
  startDate: string
  endDate: string
}

export function DateDisplay({ startDate, endDate }: DateDisplayProps) {
  return (
    <Card className="rounded-none">
      <CardContent className="py-3 flex items-center gap-2">
        <Calendar className="h-4 w-4 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">
          Showing data from {new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}
        </span>
      </CardContent>
    </Card>
  )
} 