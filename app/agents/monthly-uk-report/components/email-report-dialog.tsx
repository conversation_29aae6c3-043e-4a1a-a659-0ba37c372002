"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON>alog<PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>ead<PERSON>,
  Dialog<PERSON><PERSON>le,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import { Button } from "@/app/components/ui/button"
import { Mail } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"

const EMAIL_RECIPIENTS = [
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "<PERSON>", value: "j<PERSON><EMAIL>" },
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "<PERSON>cia", value: "<EMAIL>" },
  { label: "Sharon Yardeni", value: "<EMAIL>" }
]

export function EmailReportDialog() {
  const [open, setOpen] = useState(false)
  const [selectedEmail, setSelectedEmail] = useState<string>("")
  const [isSending, setIsSending] = useState(false)

  const handleSendReport = async () => {
    if (!selectedEmail) return
    
    try {
      setIsSending(true)
      // API call would go here
      console.log("Sending report to:", selectedEmail)
      
      // On success
      setOpen(false)
      setSelectedEmail("")
    } catch (error) {
      console.error("Failed to send report:", error)
    } finally {
      setIsSending(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" className="rounded-none">
          <Mail className="mr-2 h-4 w-4" />
          Email Report
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Send Monthly UK Report</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Select 
            value={selectedEmail} 
            onValueChange={setSelectedEmail}
          >
            <SelectTrigger className="w-full rounded-none">
              <SelectValue placeholder="Select recipient" />
            </SelectTrigger>
            <SelectContent>
              {EMAIL_RECIPIENTS.map((recipient) => (
                <SelectItem 
                  key={recipient.value} 
                  value={recipient.value}
                  className="rounded-none"
                >
                  {recipient.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button 
            onClick={handleSendReport} 
            disabled={!selectedEmail || isSending}
            className="rounded-none"
          >
            {isSending ? "Sending..." : "Send Report"}
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 