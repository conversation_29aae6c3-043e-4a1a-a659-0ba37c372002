"use client"

import { DataTable } from "@/app/components/ui/data-table"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/app/components/ui/tabs"

interface Agent {
  title: number
  firstName: string
  lastName: string
  dateOfBirth: string
  emailAddress: string
  businessPhone: string
  mobilePhone: string
  address1: string
  address2: string
  address3: string | null
  town: string
  county: string
  postcode: string
  website: string
  brand: string
  dateOfCommencement: string
  dateOfEnrollment: string
  pin: string
  dateOfCancellation?: string
}

interface Amendment {
  oldFullName: string
  newFullName: string
  pin: string
  emailAddress: string
  changeType: string
  changeDate: string
}

interface MonthlyUkTableProps {
  activeAgents: Agent[]
  canceledAgents: Agent[]
  amendments: Amendment[]
}

const columns = {
  agents: [
    { accessorKey: "pin" as keyof Agent, header: "PIN" },
    { accessorKey: "firstName" as keyof Agent, header: "First Name" },
    { accessorKey: "lastName" as keyof Agent, header: "Last Name" },
    { accessorKey: "emailAddress" as keyof Agent, header: "Email" },
    { accessorKey: "dateOfEnrollment" as keyof Agent, header: "Enrollment Date" },
    { accessorKey: "dateOfCommencement" as keyof Agent, header: "Start Date" },
    { accessorKey: "dateOfCancellation" as keyof Agent, header: "Cancellation Date" },
  ],
  amendments: [
    { accessorKey: "pin" as keyof Amendment, header: "PIN" },
    { accessorKey: "oldFullName" as keyof Amendment, header: "Old Name" },
    { accessorKey: "newFullName" as keyof Amendment, header: "New Name" },
    { accessorKey: "emailAddress" as keyof Amendment, header: "Email" },
    { accessorKey: "changeType" as keyof Amendment, header: "Change Type" },
    { accessorKey: "changeDate" as keyof Amendment, header: "Change Date" },
  ],
}

export function MonthlyUkTable({ activeAgents, canceledAgents, amendments }: MonthlyUkTableProps) {
  return (
    <Tabs defaultValue="active" className="w-full">
      <TabsList>
        <TabsTrigger value="active">Active Agents</TabsTrigger>
        <TabsTrigger value="canceled">Canceled Agents</TabsTrigger>
        <TabsTrigger value="amendments">Amendments</TabsTrigger>
      </TabsList>
      <TabsContent value="active">
        <DataTable columns={columns.agents} data={activeAgents} pageCount={Math.ceil(activeAgents.length / 10)} />
      </TabsContent>
      <TabsContent value="canceled">
        <DataTable columns={columns.agents} data={canceledAgents} pageCount={Math.ceil(canceledAgents.length / 10)} />
      </TabsContent>
      <TabsContent value="amendments">
        <DataTable columns={columns.amendments} data={amendments} pageCount={Math.ceil(amendments.length / 10)} />
      </TabsContent>
    </Tabs>
  )
} 