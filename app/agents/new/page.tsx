"use client"

import { Card } from "@/app/components/ui/card"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { AgentFormStepper } from "@/app/components/agents/shared/agent-form-stepper"

export default function NewAgentPage() {
  return (
    <div className="p-6 space-y-6">
      <Breadcrumb />
      <div className="flex items-center gap-2">
        <h1 className="text-2xl font-semibold">Add New Agent</h1>
      </div>

      <Card className="bg-white rounded-none p-6">
        <AgentFormStepper isEditMode={false} />
      </Card>
    </div>
  )
} 