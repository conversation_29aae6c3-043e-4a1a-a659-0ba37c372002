"use client"

import { AgentFilters } from "@/app/components/agents/agent-filters"
import { AgentTable } from "@/app/components/agents/agent-table"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { Button } from "@/app/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import { fetchAgents } from "@/app/lib/api/agent-api"
import { useState, useEffect, useCallback } from "react"
import { useSearchParams } from "next/navigation"
import { AdvancedAgentFilters } from "@/app/lib/api/agent-api"
import { AgentResponse } from "@/types/agent"

interface FiltersChangedEvent extends CustomEvent {
  detail: {
    filters: AdvancedAgentFilters
    page: number
    limit: number
    orderBy: string
    sortDirection: 'ASC' | 'DESC'
  }
}

export default function AgentsPage() {
  const searchParams = useSearchParams()
  const [data, setData] = useState<AgentResponse>({
    agents: [],
    total: 0,
    pageInfo: {
      currentPage: 1,
      totalPages: 1,
      hasNextPage: false,
      hasPreviousPage: false
    }
  })
  const [isLoading, setIsLoading] = useState(true)

  // Function to fetch data with specific parameters
  const fetchDataWithParams = useCallback(async (params: {
    page: number
    limit: number
    orderBy: string
    sortDirection: 'ASC' | 'DESC'
    filters: AdvancedAgentFilters
  }) => {
    setIsLoading(true)
    try {
      const result = await fetchAgents(params)
      setData(result)
    } catch (error) {
      console.error('Error fetching agents:', error)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Function to fetch data based on current URL params
  const fetchData = useCallback(async () => {
    // Parse search params with defaults
    const page = Number(searchParams.get('page')) || 1
    const limit = Number(searchParams.get('limit')) || 10
    const orderBy = searchParams.get('orderBy') || "vcFName"
    const sortDirection = (searchParams.get('sortDirection') as 'ASC' | 'DESC') || "ASC"

    // Parse advanced filters from URL
    let advancedFilters: AdvancedAgentFilters = {}
    const filtersParam = searchParams.get('filters')
    if (filtersParam) {
      try {
        advancedFilters = JSON.parse(filtersParam)
      } catch (error) {
        console.error('Error parsing filters from URL:', error)
      }
    }

    await fetchDataWithParams({
      page,
      limit,
      orderBy,
      sortDirection,
      filters: advancedFilters
    })
  }, [searchParams, fetchDataWithParams])

  // Fetch data when search params change (initial load)
  useEffect(() => {
    fetchData()
  }, [fetchData])

  // Listen for custom filtersChanged events
  useEffect(() => {
    const handleFiltersChanged = (event: FiltersChangedEvent) => {
      fetchDataWithParams(event.detail)
    }

    window.addEventListener('filtersChanged', handleFiltersChanged as EventListener)
    return () => window.removeEventListener('filtersChanged', handleFiltersChanged as EventListener)
  }, [fetchDataWithParams])

  return (
    <div className="p-6 space-y-6">
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold">Advisor Management</h1>
        <Link href="/agents/new">
          <Button className="rounded-none">
            <Plus className="h-4 w-4 mr-2" />
            Add Agent
          </Button>
        </Link>
      </div>
      <AgentFilters />
      {isLoading ? (
        <div className="flex items-center justify-center py-8">
          <div className="text-lg">Loading agents...</div>
        </div>
      ) : (
        <AgentTable data={data} />
      )}
    </div>
  )
} 