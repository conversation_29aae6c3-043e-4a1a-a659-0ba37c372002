import { Metada<PERSON> } from "next"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { CoachReportContent } from "./components/coach-report-content"

export const metadata: Metadata = {
  title: "Coach Report | Agents",
  description: "View and analyze coach-agent relationships and performance"
}

export const dynamic = 'force-dynamic' // Disable static page generation
export const revalidate = 0 // Disable cache

export default function CoachReportPage() {
  return (
    <div className="flex flex-col gap-4 p-6">
      <Breadcrumb />  
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">Coach Report</h1>
      </div>
      <CoachReportContent />
    </div>
  );
} 