interface DateRange {
  startDate: string;
  endDate: string;
}

interface ReportFilters {
  agentGroups?: number[];
}

interface OutputParams {
  limit?: number;
  includeInactive?: boolean;
}

interface CoachReportParams {
  dateRange: DateRange;
  filters: ReportFilters;
  output: OutputParams;
}

interface Agent {
  id: string;
  pin: string;
  name: string;
  status: string;
  statusId: number;
  totalCalls: number;
  lastCallDate: string | null;
}

interface Coach {
  id: string;
  name: string;
  userName: string;
  totalAgents: number;
  activeAgents: number;
  inactiveAgents: number;
  retentionRate: number;
  totalCalls: number;
  avgCallDuration: number;
  lastCallDate: string | null;
  agents: Agent[];
}

interface ReportSummary {
  totalCoaches: number;
  totalAgentsCoached: number;
  avgAgentsPerCoach: number;
  avgCallsPerAgent: number;
  avgCallDuration: number;
  topPerformingCoach: string;
}

export interface CoachReportResponse {
  data: {
    inteleCoachReport: {
      coaches: Coach[];
      summary: ReportSummary;
    };
  };
}

export async function fetchCoachReport(params: CoachReportParams): Promise<CoachReportResponse> {
  const query = `
    query {
      inteleCoachReport(params: {
        dateRange: {
          startDate: "${params.dateRange.startDate}",
          endDate: "${params.dateRange.endDate}"
        },
        filters: {
          agentGroups: [${params.filters.agentGroups?.join(',')}]
        },
        output: {
          limit: ${params.output.limit},
          includeInactive: ${params.output.includeInactive}
        }
      }) {
        coaches {
          id
          name
          userName
          totalAgents
          activeAgents
          inactiveAgents
          retentionRate
          totalCalls
          avgCallDuration
          lastCallDate
          agents {
            id
            pin
            name
            status
            statusId
            totalCalls
            lastCallDate
          }
        }
        summary {
          totalCoaches
          totalAgentsCoached
          avgAgentsPerCoach
          avgCallsPerAgent
          avgCallDuration
          topPerformingCoach
        }
      }
    }
  `;

  const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL!, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ query }),
  });

  if (!response.ok) {
    const errorData = await response.text();
    throw new Error(`API request failed with status ${response.status}: ${errorData}`);
  }

  const result = await response.json();
  
  if (result.errors) {
    throw new Error(`GraphQL Error: ${result.errors[0].message}`);
  }

  return result;
}

export type { DateRange, ReportFilters, OutputParams, Agent, Coach, ReportSummary }; 