'use client';

import { useEffect, useState } from 'react';
import { CoachReportFilters } from "./coach-report-filters";
import { CoachReportTable } from "./coach-report-table";
import { fetchCoachReport, CoachReportResponse } from "../api/coach-report";

export function CoachReportContent() {
  const [data, setData] = useState<CoachReportResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const loadData = async () => {
      try {
        const params = {
          dateRange: {
            startDate: "2023-01-01T00:00:00Z",
            endDate: "2023-12-31T23:59:59Z"
          },
          filters: {
            agentGroups: [85]
          },
          output: {
            limit: 10,
            includeInactive: true
          }
        };

        const response = await fetchCoachReport(params);
        console.log('API Response:', response); // Debug log
        setData(response);
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred');
        console.error('Error fetching coach report:', err);
      } finally {
        setIsLoading(false);
      }
    };

    loadData();
  }, []);

  if (error) {
    return (
      <div className="text-red-600">
        Error loading coach report: {error}
      </div>
    );
  }

  if (isLoading) {
    return <div>Loading...</div>;
  }

  // Debug log to verify data before rendering
  console.log('Rendering with data:', data?.data.inteleCoachReport);

  return (
    <>
      <CoachReportFilters />
      {data?.data.inteleCoachReport && (
        <CoachReportTable data={data.data.inteleCoachReport} />
      )}
    </>
  );
} 