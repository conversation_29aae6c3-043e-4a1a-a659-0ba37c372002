"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Coach, ReportSummary } from "../api/coach-report"

interface CoachReportRow {
  coachPin: string
  coachName: string
  agentPin: string
  agentName: string
  agentStatus: string
  bookingType: string
  bookingStatus: string
  vendor: string
  arrivalCity: string
  departureCity: string
  travelStartDate: string
  travelEndDate: string
  totalCommission: number
  totalSales: number
  inTheBank: boolean
}

interface CoachReportTableProps {
  data: {
    coaches: Coach[];
    summary: ReportSummary;
  };
}

// Dummy data for demonstration
const DUMMY_DATA: CoachReportRow[] = [
  {
    coachPin: "12345",
    coachName: "John Coach",
    agentPin: "67890",
    agentName: "Jane Agent",
    agentStatus: "Active",
    bookingType: "Cruise",
    bookingStatus: "Confirmed",
    vendor: "Royal Caribbean",
    arrivalCity: "Miami",
    departureCity: "Nassau",
    travelStartDate: "2024-06-15",
    travelEndDate: "2024-06-22",
    totalCommission: 500,
    totalSales: 5000,
    inTheBank: true
  },
  // Add more dummy data...
]

export function CoachReportTable({ data }: CoachReportTableProps) {
  return (
    <div className="border table-container">
      <Table>
        <TableHeader>
          <TableRow className="rounded-none">
            <TableHead>Coach Name</TableHead>
            <TableHead>Username</TableHead>
            <TableHead>Total Agents</TableHead>
            <TableHead>Active Agents</TableHead>
            <TableHead>Inactive Agents</TableHead>
            <TableHead>Retention Rate</TableHead>
            <TableHead>Total Calls</TableHead>
            <TableHead>Avg Call Duration</TableHead>
            <TableHead>Last Call Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.coaches.map((coach) => (
            <TableRow key={coach.id}>
              <TableCell>{coach.name}</TableCell>
              <TableCell>{coach.userName}</TableCell>
              <TableCell>{coach.totalAgents}</TableCell>
              <TableCell>{coach.activeAgents}</TableCell>
              <TableCell>{coach.inactiveAgents}</TableCell>
              <TableCell>{coach.retentionRate}%</TableCell>
              <TableCell>{coach.totalCalls}</TableCell>
              <TableCell>{coach.avgCallDuration}</TableCell>
              <TableCell>{coach.lastCallDate || 'N/A'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 