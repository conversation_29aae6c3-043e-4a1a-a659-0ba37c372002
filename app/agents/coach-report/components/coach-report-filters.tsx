"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/app/components/ui/select"
import { Input } from "@/app/components/ui/input"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Filter, X, ChevronDown, ChevronUp, Plus } from "lucide-react"
import { Badge } from "@/app/components/ui/badge"

interface FilterCriteria {
  field: string
  operator: string
  value: string | Date | null
}

const FILTER_FIELDS = [
  { 
    label: "Coach PIN", 
    value: "mentorPin", 
    type: "text" 
  },
  { 
    label: "Agent Status", 
    value: "agentStatus", 
    type: "select",
    options: [
      { label: "Active", value: "1" },
      { label: "Inactive", value: "2" },
      // Add other status options as needed
    ]
  },
  { 
    label: "Booking Type", 
    value: "bookingType", 
    type: "select",
    options: [
      { label: "Cruise", value: "1" },
      { label: "Hotel", value: "2" },
      // Add other booking types as needed
    ]
  },
  { 
    label: "Booking Status", 
    value: "bookingStatus", 
    type: "select",
    options: [
      { label: "Confirmed", value: "1" },
      { label: "Pending", value: "2" },
      // Add other booking statuses as needed
    ]
  },
  { 
    label: "Travel Start Date", 
    value: "travelStartDate", 
    type: "date" 
  },
  { 
    label: "Travel End Date", 
    value: "travelEndDate", 
    type: "date" 
  },
  { 
    label: "In The Bank", 
    value: "inTheBank", 
    type: "select",
    options: [
      { label: "Yes", value: "1" },
      { label: "No", value: "0" }
    ]
  }
]

export function CoachReportFilters() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [filters, setFilters] = useState<FilterCriteria[]>([])
  const [selectedField, setSelectedField] = useState("")
  const [selectedOperator, setSelectedOperator] = useState("")
  const [selectedValue, setSelectedValue] = useState<string | Date | null>("")

  const addFilter = () => {
    if (selectedField && selectedOperator && selectedValue) {
      setFilters([...filters, { 
        field: selectedField, 
        operator: selectedOperator, 
        value: selectedValue 
      }])
      // Reset selections
      setSelectedField("")
      setSelectedOperator("")
      setSelectedValue("")
    }
  }

  const removeFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index))
  }

  const resetFilters = () => {
    setFilters([])
    setSelectedField("")
    setSelectedOperator("")
    setSelectedValue("")
  }

  const applyFilters = () => {
    console.log("Applying filters:", filters)
  }

  const getFieldLabel = (value: string) => {
    return FILTER_FIELDS.find(f => f.value === value)?.label || value
  }

  const getOperatorLabel = (operator: string) => {
    const operators = {
      equals: "equals",
      contains: "contains",
      greater: "greater than",
      less: "less than"
    }
    return operators[operator as keyof typeof operators] || operator
  }

  const handleDateChange = (date: Date | null) => {
    setSelectedValue(date)
  }

  return (
    <Card className="rounded-none">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2 border-b rounded-none">
        <CardTitle className="text-md font-medium flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filters
        </CardTitle>
        <div className="flex gap-2">
          <Button
            variant="outline" 
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="gap-2 rounded-none"
          >
            {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
            {isCollapsed ? "Show Filters" : "Hide Filters"}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
            className="rounded-none"
          >
            Reset
          </Button>
          <Button 
            size="sm"
            onClick={applyFilters}
            className="rounded-none"
          >
            Apply
          </Button>
        </div>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="pt-4">
          <div className="space-y-4">
            {/* Active Filters */}
            {filters.length > 0 && (
              <div className="flex flex-wrap gap-2">
                {filters.map((filter, index) => (
                  <Badge 
                    key={index} 
                    variant="secondary"
                    className="flex items-center gap-1 rounded-none"
                  >
                    {getFieldLabel(filter.field)} {getOperatorLabel(filter.operator)} {filter.value?.toString()}
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-4 w-4 p-0 hover:bg-transparent rounded-none"
                      onClick={() => removeFilter(index)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ))}
              </div>
            )}
            
            {/* Filter Selection */}
            <div className="flex gap-2 items-start">
              <Select
                value={selectedField}
                onValueChange={setSelectedField}
              >
                <SelectTrigger className="w-[200px] rounded-none">
                  <SelectValue placeholder="Select field" />
                </SelectTrigger>
                <SelectContent className="rounded-none">
                  {FILTER_FIELDS.map((field) => (
                    <SelectItem key={field.value} value={field.value} className="rounded-none">
                      {field.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              <Select
                value={selectedOperator}
                onValueChange={setSelectedOperator}
              >
                <SelectTrigger className="w-[200px] rounded-none">
                  <SelectValue placeholder="Select operator" />
                </SelectTrigger>
                <SelectContent className="rounded-none">
                  <SelectItem value="equals" className="rounded-none">Equals</SelectItem>
                  <SelectItem value="contains" className="rounded-none">Contains</SelectItem>
                  <SelectItem value="greater" className="rounded-none">Greater than</SelectItem>
                  <SelectItem value="less" className="rounded-none">Less than</SelectItem>
                </SelectContent>
              </Select>

              {selectedField && FILTER_FIELDS.find(f => f.value === selectedField)?.type === "select" ? (
                <Select
                  value={selectedValue as string}
                  onValueChange={setSelectedValue}
                >
                  <SelectTrigger className="w-[200px] rounded-none">
                    <SelectValue placeholder="Select value" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none">
                    {FILTER_FIELDS.find(f => f.value === selectedField)?.options?.map(option => (
                      <SelectItem 
                        key={option.value} 
                        value={option.value}
                        className="rounded-none"
                      >
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : selectedField && FILTER_FIELDS.find(f => f.value === selectedField)?.type === "date" ? (
                <div className="rounded-none">
                  <DatePicker
                    date={selectedValue as Date}
                    setDate={handleDateChange}
                  />
                </div>
              ) : (
                <Input
                  value={selectedValue as string}
                  onChange={(e) => setSelectedValue(e.target.value)}
                  placeholder="Enter value"
                  className="w-[200px] rounded-none"
                />
              )}

              <Button
                variant="outline"
                size="icon"
                onClick={addFilter}
                disabled={!selectedField || !selectedOperator || !selectedValue}
                className="rounded-none"
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </CardContent>
      )}
    </Card>
  )
} 