import { Card } from "@/app/components/ui/card"
import { TransactionHistory } from "@/app/components/agents/transactions/transaction-history"
import { TransactionSearch } from "@/app/components/agents/transactions/transaction-search"

export default function AgentTransactionsPage({ params }: { params: { id: string } }) {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-2xl font-semibold">Transaction History</h1>

      <Card className="bg-white rounded-none p-6">
        <TransactionSearch />
        <TransactionHistory agentId={params.id} />
      </Card>
    </div>
  )
} 