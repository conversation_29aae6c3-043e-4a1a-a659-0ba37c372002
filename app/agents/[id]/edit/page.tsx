"use client"

import { Card } from "@/app/components/ui/card"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { AgentFormStepper } from "@/app/components/agents/shared/agent-form-stepper"
import { useEffect, useState } from "react"
import { useParams } from "next/navigation"
import { Skeleton } from "@/app/components/ui/skeleton"

export default function EditAgentPage() {
  const params = useParams()
  const agentId = params.id as string
  const [agent, setAgent] = useState<any>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchAgent() {
      try {
        const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || '/api/graphql', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            query: `
              query GetAgent($agentId: Int!) {
                agent(agentId: $agentId) {
                  iAgentID
                  vcPIN
                  vcFName
                  vcLName
                  vcEmail
                  vcCompany
                  vcStreet
                  vcStreet2
                  vcCity
                  vcState
                  vcZip
                  vcCountry
                  vcWorkPhone
                  vcHomePhone
                  vcCellPhone
                  bActive
                  iGroupID
                  iAgentTypeID
                  iStatusID
                }
              }
            `,
            variables: {
              agentId: parseInt(agentId)
            }
          })
        })

        const result = await response.json()
        
        if (result.data?.agent) {
          const agentData = result.data.agent
          setAgent({
            agentId: agentData.iAgentID,
            pin: agentData.vcPIN,
            firstName: agentData.vcFName,
            lastName: agentData.vcLName,
            email: agentData.vcEmail,
            company: agentData.vcCompany,
            address: {
              street: agentData.vcStreet || "",
              street2: agentData.vcStreet2 || "",
              city: agentData.vcCity || "",
              state: agentData.vcState || "",
              zip: agentData.vcZip || "",
              country: agentData.vcCountry || ""
            },
            phones: {
              work: agentData.vcWorkPhone || "",
              home: agentData.vcHomePhone || "",
              cell: agentData.vcCellPhone || ""
            },
            isActive: agentData.bActive,
            groupId: agentData.iGroupID,
            agentTypeId: agentData.iAgentTypeID,
            statusId: agentData.iStatusID
          })
        } else {
          setError("Agent not found")
        }
      } catch (err) {
        console.error(err)
        setError("Failed to load agent data")
      } finally {
        setLoading(false)
      }
    }

    fetchAgent()
  }, [agentId])

  return (
    <div className="p-6 space-y-6">
      <Breadcrumb />
      <div className="flex items-center gap-2">
        <h1 className="text-2xl font-semibold">Edit Agent</h1>
      </div>

      <Card className="bg-white rounded-none p-6">
        {loading ? (
          <div className="space-y-4">
            <Skeleton className="h-8 w-1/3" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
              <Skeleton className="h-32" />
            </div>
          </div>
        ) : error ? (
          <div className="text-red-500">{error}</div>
        ) : (
          <AgentFormStepper 
            initialData={agent} 
            isEditMode={true} 
          />
        )}
      </Card>
    </div>
  )
} 