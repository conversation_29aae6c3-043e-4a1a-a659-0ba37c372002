import { Card } from "@/app/components/ui/card"
import { AgentBonusTable } from "@/app/components/agents/bonus-commission/agent-bonus-table"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { Suspense } from "react"
import { TableSkeleton } from "@/app/components/ui/table-skeleton"

export default function AgentBonusCommissionPage() {
  return (
    <div className="p-6 space-y-6"> 
      <Breadcrumb />
      <h1 className="text-2xl font-semibold">Agent Bonus Commission</h1>
      
      <Card className="bg-white rounded-none p-6">
        <Suspense fallback={<TableSkeleton />}>
          <AgentBonusTable />
        </Suspense>
      </Card>
    </div>
  )
} 