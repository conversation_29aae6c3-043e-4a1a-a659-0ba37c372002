import { z } from "zod"

export const adminUserSchema = z.object({
  username: z.string()
    .min(3, "Username must be at least 3 characters")
    .max(50, "Username cannot exceed 50 characters"),
  password: z.string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number"),
  fullName: z.string()
    .min(2, "Full name is required")
    .max(100, "Full name cannot exceed 100 characters"),
  email: z.string()
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
  cellNumber: z.string()
    .min(10, "Cell number must be at least 10 digits")
    .max(15, "Cell number cannot exceed 15 digits")
    .regex(/^\+?[\d\s-]+$/, "Please enter a valid phone number"),
  isActive: z.boolean(),
  securityLevelId: z.number()
})

export type AdminUserFormData = z.infer<typeof adminUserSchema> 