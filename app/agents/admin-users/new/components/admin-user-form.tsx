"use client"

import { useForm } from "react-hook-form"
import type { AdminUser } from "../../[userId]/schema"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Switch } from "@/components/ui/switch"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { graphqlClient } from "@/app/lib/graphql-client"
import {
  Card,
  CardContent,
  CardFooter,
} from "@/app/components/ui/card"
import { cn } from "@/app/lib/utils"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

interface AdminUserFormProps {
  initialData?: AdminUser
  isEditing?: boolean
}

interface AdminUserFormData extends Omit<AdminUser, 'password'> {
  password?: string
}

interface CreateAdminUserResponse {
  createAdminUser: {
    status: string
    message: string
    adminUser: {
      userId: string
      username: string
    }
  }
}

interface UpdateAdminUserResponse {
  updateAdminUser: {
    status: string
    message: string
    adminUser: AdminUser
  }
}

export function AdminUserForm({ initialData, isEditing = false }: AdminUserFormProps) {
  const router = useRouter()
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")

  const form = useForm<AdminUserFormData>({
    defaultValues: initialData || {
      username: "",
      fullName: "",
      email: "",
      cellNumber: "",
      securityLevelId: 1,
      isActive: true,
      status: "Active",
      password: "",
    },
  })

  async function onSubmit(data: AdminUserFormData) {
    try {
      setIsLoading(true)
      setError("")
      
      if (isEditing) {
        const { password, ...updateData } = data
        const userId = String(initialData?.userId)
        
        const mutation = `
          mutation UpdateAdminUser($userId: ID!, $input: UpdateAdminUserInput!) {
            updateAdminUser(
              userId: $userId
              input: $input
            ) {
              status
              message
              adminUser {
                userId
                username
                securityLevelId
                securityLevel
                status
                cellNumber
                fullName
                email
              }
            }
          }
        `

        const variables = {
          userId,
          input: {
            fullName: updateData.fullName,
            email: updateData.email,
            cellNumber: updateData.cellNumber,
            isActive: updateData.isActive
          }
        }
        
        const response = await graphqlClient.request<UpdateAdminUserResponse>(mutation, variables)
        const result = response.updateAdminUser

        if (result.status === "true") {
          window.location.href = "/agents/admin-users"
        } else {
          setError(result.message || "Failed to update admin user")
        }
      } else {
        const mutation = `
          mutation CreateAdminUser($input: CreateAdminUserInput!) {
            createAdminUser(input: $input) {
              status
              message
              adminUser {
                userId
                username
              }
            }
          }
        `

        const response = await graphqlClient.request<CreateAdminUserResponse>(mutation, {
          input: data
        })
        const result = response.createAdminUser
        
        if (result.status === "true") {
          window.location.href = "/agents/admin-users"
        } else {
          setError(result.message || "Failed to create admin user")
        }
      }
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError("An unexpected error occurred")
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="rounded-none">
      <Form {...form}>
        <form 
          onSubmit={form.handleSubmit(onSubmit)} 
          className="space-y-6"
          noValidate
        >
          <CardContent className="space-y-6">
            {error && (
              <div className="p-3 text-sm font-medium text-destructive bg-destructive/10 rounded-md">
                {error}
              </div>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="username"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Username
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.username && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter username"
                        autoComplete="username"
                        disabled={isLoading}
                        onChange={(e) => {
                          field.onChange(e)
                          form.trigger("username")
                        }}
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.username?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
              
              {!isEditing && (
                <FormField
                  control={form.control}
                  name="password"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel className="flex items-center gap-1">
                        Password
                        <span className="text-destructive">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input 
                          type="password"
                          {...field}
                          className={cn(
                            "transition-colors",
                            form.formState.errors.password && "border-destructive focus-visible:ring-destructive"
                          )}
                          placeholder="Enter password"
                          autoComplete="new-password"
                          disabled={isLoading}
                          onChange={(e) => {
                            field.onChange(e)
                            form.trigger("password")
                          }}
                        />
                      </FormControl>
                      <FormMessage>
                        {form.formState.errors.password?.message}
                      </FormMessage>
                    </FormItem>
                  )}
                />
              )}
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Full Name
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.fullName && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter full name"
                        disabled={isLoading}
                        onBlur={(e) => {
                          field.onBlur()
                          form.trigger("fullName")
                        }}
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.fullName?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Email
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="email"
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.email && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter email address"
                        autoComplete="email"
                        disabled={isLoading}
                        onBlur={(e) => {
                          field.onBlur()
                          form.trigger("email")
                        }}
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.email?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="cellNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Cell Number
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.cellNumber && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter cell number"
                        type="tel"
                        autoComplete="tel"
                        disabled={isLoading}
                        onBlur={(e) => {
                          field.onBlur()
                          form.trigger("cellNumber")
                        }}
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.cellNumber?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="securityLevelId"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Security Level</FormLabel>
                    <Select 
                      onValueChange={(value) => field.onChange(parseInt(value))}
                      defaultValue={field.value.toString()}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select security level" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="1">Super User</SelectItem>
                        <SelectItem value="2">Admin</SelectItem>
                        <SelectItem value="3">User</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">Active Status</FormLabel>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        disabled={isLoading}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  {isEditing ? "Updating..." : "Creating..."}
                </span>
              ) : (
                isEditing ? "Update" : "Create"
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
} 