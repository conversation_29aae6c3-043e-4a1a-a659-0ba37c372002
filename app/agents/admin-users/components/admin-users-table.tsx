"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Edit, Trash2 } from "lucide-react"
import Link from "next/link"
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "@/app/components/ui/alert-dialog"
import { AdminUser } from "@/app/types/admin-users"
import { Badge } from "@/app/components/ui/badge"
import { graphqlClient } from "@/app/lib/graphql-client"
import { useRouter } from "next/navigation"
import { deleteAdminUserAction } from "../actions"

interface AdminUsersTableProps {
  initialData: AdminUser[]
}

export function AdminUsersTable({ initialData }: AdminUsersTableProps) {
  const [statusFilter, setStatusFilter] = useState<"all" | "active" | "inactive">("all")
  const [isDeleting, setIsDeleting] = useState(false)
  const [userToDelete, setUserToDelete] = useState<AdminUser | null>(null)
  const router = useRouter()

  const filteredUsers = (initialData || []).filter(user => {
    if (statusFilter === "all") return true
    return statusFilter === "active" ? user.status === "Active" : user.status === "Inactive"
  })

  const handleDelete = async (user: AdminUser) => {
    setUserToDelete(user)
  }

  const confirmDelete = async () => {
    if (!userToDelete) return
    
    setIsDeleting(true)
    try {
      const result = await deleteAdminUserAction(userToDelete.userId.toString())
      
      if (result.success) {
        window.location.href = "/agents/admin-users"
      } else {
        alert(result.error || 'Failed to delete user')
      }
    } catch (error) {
      alert('An error occurred while deleting the user')
    } finally {
      setIsDeleting(false)
      setUserToDelete(null)
    }
  }

  if (!filteredUsers.length) {
    return (
      <Card className="rounded-none p-6">
        <div className="text-center text-muted-foreground">No users available</div>
      </Card>
    )
  }

  return (
    <Card className="rounded-none">
      <div className="p-4 flex items-center justify-between border-b">
        <Select value={statusFilter} onValueChange={(value) => setStatusFilter(value as "all" | "active" | "inactive")}>
          <SelectTrigger className="w-[180px] rounded-none">
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Users</SelectItem>
            <SelectItem value="active">Active</SelectItem>
            <SelectItem value="inactive">Inactive</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Username</TableHead>
            <TableHead>Security Level</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Cell Number</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {filteredUsers.map((user) => (
            <TableRow key={user.userId}>
              <TableCell className="font-medium">{user.username}</TableCell>
              <TableCell>{user.securityLevel}</TableCell>
              <TableCell>
                <Badge variant={user.status === "Active" ? "success" : "secondary"}>
                  {user.status}
                </Badge>
              </TableCell>
              <TableCell>{user.cellNumber || "N/A"}</TableCell>
              <TableCell className="text-right">
                <div className="flex justify-end gap-2">
                  <Button variant="ghost" size="icon" asChild>
                    <Link href={`/agents/admin-users/${user.userId}`}>
                      <Edit className="h-4 w-4" />
                    </Link>
                  </Button>
                  <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={() => handleDelete(user)}
                    disabled={isDeleting}
                  >
                    <Trash2 className="h-4 w-4 text-red-500" />
                  </Button>
                </div>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>

      <AlertDialog open={!!userToDelete} onOpenChange={() => setUserToDelete(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Are you sure?</AlertDialogTitle>
            <AlertDialogDescription>
              This action cannot be undone. This will permanently delete the user
              {userToDelete?.username && ` "${userToDelete.username}"`}.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={confirmDelete}
              disabled={isDeleting}
              className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </Card>
  )
} 