'use server'

import { graphqlClient } from "@/app/lib/graphql-client"
import { revalidatePath } from "next/cache"

const deleteAdminUserAction = async function deleteAdminUser(userId: string) {
  try {
    // Log the request for debugging
    console.log('Attempting to delete user:', userId)

    const mutation = `
      mutation DeleteAdminUser {
        deleteAdminUser(userId: ${parseInt(userId, 10)}) {
          status
          message
        }
      }
    `
    
    // Log the mutation for debugging
    console.log('GraphQL Mutation:', mutation)
    console.log('Using client with endpoint:', process.env.NEXT_PUBLIC_GRAPHQL_URL)

    // Use graphqlClient which should be configured to use localhost:4000/graphql
    const response = await graphqlClient.request<{
      deleteAdminUser: {
        status: string
        message: string
      }
    }>(mutation)
    console.log('GraphQL Response:', response)

    if (response?.deleteAdminUser?.status === 'true') {
      revalidatePath('/agents/admin-users')
      return { success: true }
    } else {
      throw new Error(response?.deleteAdminUser?.message || 'Failed to delete user')
    }
  } catch (error) {
    console.error('Delete action error:', error)
    return { 
      success: false, 
      error: error instanceof Error ? error.message : 'Failed to delete user'
    }
  }
}

export { deleteAdminUserAction } 