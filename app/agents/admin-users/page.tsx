import { Metadata } from "next"
import { AdminUsersTable } from "./components/admin-users-table"
import { But<PERSON> } from "@/app/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { graphqlClient } from "@/app/lib/graphql-client"
import { AdminUsersResponse } from "@/app/types/admin-users"
import { Suspense } from "react"
import { TableSkeleton } from "@/app/components/ui/table-skeleton"

// Disable caching
export const dynamic = 'force-dynamic'
export const revalidate = 0

export const metadata: Metadata = {
  title: "Admin Users | Agents",
  description: "Manage admin users and their permissions"
}

async function getAdminUsers() {
  const query = `
    query GetAllAdminUsers {
      getAllAdminUsers {
        status
        message
        totalCount
        adminUsers {
          userId
          username
          securityLevel
          status
          cellNumber
        }
      }
    }
  `

  return graphqlClient.request<AdminUsersResponse>(query)
}

export default async function AdminUsersPage() {
  const data = await getAdminUsers()
  
  if (!data?.getAllAdminUsers?.adminUsers) {
    return (
      <div className="p-6">
        <div className="bg-destructive/10 text-destructive p-4 rounded-md">
          Error loading admin users
        </div>
      </div>
    )
  }

  return (
    <div className="flex flex-col gap-4 p-6">
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">Admin Users</h1>
        <Button asChild>
          <Link href="/agents/admin-users/new">
            <Plus className="mr-2 h-4 w-4" />
            Create New User
          </Link>
        </Button>
      </div>
      <Suspense fallback={<TableSkeleton />}>
        <AdminUsersTable initialData={data.getAllAdminUsers.adminUsers} />
      </Suspense>
    </div>
  )
} 