import { notFound } from "next/navigation"
import { graphqlClient } from "@/app/lib/graphql-client"
import type { AdminUser } from "./schema"
import { AdminUserForm } from "../new/components/admin-user-form"

interface GetAdminUserByIdResponse {
  getAdminUserById: {
    status: string
    message: string
    adminUser: AdminUser | null
  }
}

async function getAdminUser(userId: string): Promise<AdminUser | null> {
  try {
    const query = `
      query GetAdminUserById($userId: ID!) {
        getAdminUserById(userId: $userId) {
          status
          message
          adminUser {
            userId
            username
            securityLevelId
            securityLevel
            isActive
            status
            cellNumber
            fullName
            email
            created_at
            modified_at
          }
        }
      }
    `

    const response = await graphqlClient.request<GetAdminUserByIdResponse>(query, {
      userId: userId
    })

    // Add debug logging
    console.log('GraphQL Response:', JSON.stringify(response, null, 2))

    // Check if we have a valid response structure
    if (!response?.getAdminUserById) {
      console.error('Invalid response structure')
      return null
    }

    const { status, message, adminUser } = response.getAdminUserById

    // Log the status and message
    console.log('Status:', status, 'Message:', message)

    if (status === "true") {
      return adminUser
    }

    console.error('Invalid status:', status, 'Message:', message)
    return null
  } catch (error) {
    console.error('GraphQL Error:', error)
    return null
  }
}

interface AdminUserPageProps {
  params: {
    userId: string
  }
}

export default async function AdminUserPage({ params }: AdminUserPageProps) {
  if (!params.userId) {
    console.error('No userId provided')
    notFound()
  }

  try {
    const adminUser = await getAdminUser(params.userId)
    
    if (!adminUser) {
      console.error('No admin user found for ID:', params.userId)
      notFound()
    }
    
    return (
      <div className="container py-6">
        <div className="space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">Edit Admin User</h1>
          </div>
          <AdminUserForm 
            initialData={adminUser}
            isEditing={true}
          />
        </div>
      </div>
    )
  } catch (error) {
    console.error('Error in user page:', error)
    notFound()
  }
} 