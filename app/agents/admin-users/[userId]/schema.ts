import { z } from "zod"

export const adminUserUpdateSchema = z.object({
  fullName: z.string()
    .min(2, "Full name is required")
    .max(100, "Full name cannot exceed 100 characters"),
  email: z.string()
    .email("Please enter a valid email address")
    .min(1, "Email is required"),
  cellNumber: z.string()
    .min(10, "Cell number must be at least 10 digits")
    .max(15, "Cell number cannot exceed 15 digits")
    .regex(/^\+?[\d\s-]+$/, "Please enter a valid phone number"),
  isActive: z.boolean(),
})

export type AdminUserUpdateFormData = z.infer<typeof adminUserUpdateSchema>

export interface AdminUser {
  userId: string
  username: string
  securityLevelId: number
  securityLevel: string
  isActive: boolean
  status: string
  cellNumber: string
  fullName: string
  email: string
  created_at: string | null
  modified_at: string | null
} 