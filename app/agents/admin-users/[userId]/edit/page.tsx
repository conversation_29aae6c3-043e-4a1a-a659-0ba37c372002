import { AdminUserEditForm } from "../components/admin-user-edit-form"
import type { AdminUser } from "../schema"
import { notFound } from "next/navigation"
import { graphqlClient } from "@/app/lib/graphql-client"

interface GetAdminUserByIdResponse {
  data: {
    getAdminUserById: {
      status: string
      message: string
      adminUser: AdminUser
    }
  }
}

async function getAdminUser(userId: string): Promise<AdminUser | null> {
  console.log('Fetching admin user:', userId)

  try {
    const query = `
      query GetAdminUserById($userId: ID!) {
        getAdminUserById(userId: $userId) {
          status
          message
          adminUser {
            userId
            username
            securityLevelId
            securityLevel
            isActive
            status
            cellNumber
            fullName
            email
            created_at
            modified_at
          }
        }
      }
    `

    const response = await graphqlClient.request<GetAdminUserByIdResponse>(query, {
      userId: userId // Don't parse as integer since it expects an ID type
    })

    console.log('GraphQL Response:', JSON.stringify(response, null, 2))

    if (response?.data?.getAdminUserById?.status === "true") {
      return response.data.getAdminUserById.adminUser
    }

    console.log('GraphQL Error:', response?.data?.getAdminUserById?.message)
    return null
  } catch (error) {
    console.error('GraphQL Error:', error)
    return null
  }
}

interface AdminUserEditPageProps {
  params: {
    userId: string
  }
}

export default async function AdminUserEditPage({ params }: AdminUserEditPageProps) {
  console.log('Page Props - userId:', params.userId)

  try {
    const adminUser = await getAdminUser(params.userId)
    
    if (!adminUser) {
      console.log('Admin user not found:', params.userId)
      notFound()
    }
    
    return (
      <div className="container py-6">
        <AdminUserEditForm adminUser={adminUser} />
      </div>
    )
  } catch (error) {
    console.error('Error in edit page:', error)
    notFound()
  }
} 