"use client"

import { AdminUser } from "../schema"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { PencilIcon } from "lucide-react"

interface AdminUserDetailsProps {
  adminUser: AdminUser
}

export function AdminUserDetails({ adminUser }: AdminUserDetailsProps) {
  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between">
        <div>
          <CardTitle>{adminUser.fullName}</CardTitle>
          <CardDescription>{adminUser.username}</CardDescription>
        </div>
        <Link href={`/agents/admin-users/${adminUser.userId}/edit`}>
          <Button variant="outline" size="sm">
            <PencilIcon className="h-4 w-4 mr-2" />
            Edit
          </Button>
        </Link>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Status</h3>
              <Badge 
                variant={adminUser.isActive ? "success" : "destructive"}
                className="mt-1"
              >
                {adminUser.isActive ? "Active" : "Inactive"}
              </Badge>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Security Level</h3>
              <p className="mt-1">{adminUser.securityLevel}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Email</h3>
              <p className="mt-1">{adminUser.email}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Cell Number</h3>
              <p className="mt-1">{adminUser.cellNumber}</p>
            </div>
          </div>

          <div className="space-y-4">
            <div>
              <h3 className="text-sm font-medium text-muted-foreground">User ID</h3>
              <p className="mt-1">{adminUser.userId}</p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Created At</h3>
              <p className="mt-1">
                {adminUser.created_at 
                  ? format(new Date(adminUser.created_at), "PPP")
                  : "N/A"}
              </p>
            </div>

            <div>
              <h3 className="text-sm font-medium text-muted-foreground">Last Modified</h3>
              <p className="mt-1">
                {adminUser.modified_at
                  ? format(new Date(adminUser.modified_at), "PPP")
                  : "N/A"}
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 