"use client"

import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { adminUserUpdateSchema, type AdminUserUpdateFormData, type AdminUser } from "../schema"
import { Button } from "@/app/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/app/components/ui/form"
import { Input } from "@/app/components/ui/input"
import { Switch } from "@/app/components/ui/switch"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { graphqlClient } from "@/app/lib/graphql-client"
import {
  Card,
  CardContent,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/app/components/ui/card"
import { cn } from "@/app/lib/utils"

interface AdminUserEditFormProps {
  adminUser: AdminUser
}

interface UpdateAdminUserResponse {
  updateAdminUser: {
    status: string
    message: string
    adminUser: AdminUser
  }
}

export function AdminUserEditForm({ adminUser }: AdminUserEditFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()
  
  console.log('AdminUserEditForm props:', { adminUser })
  
  const form = useForm<AdminUserUpdateFormData>({
    resolver: zodResolver(adminUserUpdateSchema),
    defaultValues: {
      fullName: adminUser.fullName,
      email: adminUser.email,
      cellNumber: adminUser.cellNumber,
      isActive: adminUser.isActive,
    },
    mode: "all",
  })

  const onSubmit = async (data: AdminUserUpdateFormData) => {
    if (!form.formState.isValid) {
      await form.trigger()
      return
    }
    
    setIsLoading(true)
    setError("")
    
    try {
      const mutation = `
        mutation UpdateAdminUser($userId: Int!, $input: UpdateAdminUserInput!) {
          updateAdminUser(userId: $userId, input: $input) {
            status
            message
            adminUser {
              userId
              username
              securityLevelId
              securityLevel
              status
              cellNumber
              fullName
              email
            }
          }
        }
      `
      
      const response = await graphqlClient.request<UpdateAdminUserResponse>(
        mutation, 
        { 
          userId: parseInt(adminUser.userId),
          input: data
        }
      )
      
      const result = response.updateAdminUser
      
      if (result.status === "true") {
        router.push("/agents/admin-users")
        router.refresh()
      } else {
        setError(result.message || "Failed to update admin user")
      }
    } catch (error) {
      if (error instanceof Error) {
        setError(error.message)
      } else {
        setError("An unexpected error occurred")
      }
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Card className="rounded-none">
      <CardHeader>
        <CardTitle>Edit Admin User: {adminUser.username}</CardTitle>
      </CardHeader>
      <Form {...form}>
        <form 
          onSubmit={form.handleSubmit(onSubmit)} 
          className="space-y-6"
          noValidate
        >
          <CardContent className="space-y-6">
            {error && (
              <div className="p-3 text-sm font-medium text-destructive bg-destructive/10 rounded-md">
                {error}
              </div>
            )}
            
            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="fullName"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Full Name
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.fullName && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter full name"
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.fullName?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Email
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        type="email"
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.email && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter email address"
                        autoComplete="email"
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.email?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />
            </div>

            <div className="grid grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="cellNumber"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-1">
                      Cell Number
                      <span className="text-destructive">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input 
                        {...field}
                        className={cn(
                          "transition-colors",
                          form.formState.errors.cellNumber && "border-destructive focus-visible:ring-destructive"
                        )}
                        placeholder="Enter cell number"
                        type="tel"
                        autoComplete="tel"
                      />
                    </FormControl>
                    <FormMessage>
                      {form.formState.errors.cellNumber?.message}
                    </FormMessage>
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="isActive"
                render={({ field }) => (
                  <FormItem className="flex items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel>Active Status</FormLabel>
                      <p className="text-sm text-muted-foreground">
                        Enable or disable user access
                      </p>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </CardContent>
          <CardFooter className="flex justify-end space-x-4">
            <Button
              type="button"
              variant="outline"
              onClick={() => router.back()}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button 
              type="submit" 
              disabled={isLoading}
            >
              {isLoading ? (
                <span className="flex items-center gap-2">
                  <span className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  Updating...
                </span>
              ) : (
                "Update User"
              )}
            </Button>
          </CardFooter>
        </form>
      </Form>
    </Card>
  )
} 