"use client"

import { useQuery } from "@apollo/client"
import { gql } from "@apollo/client"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Users, UserCheck, UserX, Clock, Book } from "lucide-react"
import { Progress } from "@/app/components/ui/progress"

const GET_TRAINING_STATS = gql`
  query GetRegulatoryTrainingAnalysis {
    getRegulatoryTrainingAnalysis {
      stats {
        totalAgents
        compliantAgents
        nonCompliantAgents
        complianceRate
        averageCompletionDays
        moduleCompletionRates {
          moduleId
          moduleName
          completionCount
          completionRate
        }
      }
    }
  }
`

interface ModuleCompletionRate {
  moduleId: number
  moduleName: string
  completionCount: number
  completionRate: number
}

export function TrainingStats() {
  const { data, loading, error } = useQuery(GET_TRAINING_STATS)

  if (loading) return <div>Loading stats...</div>
  if (error) return <div>Error loading stats</div>

  const stats = data.getRegulatoryTrainingAnalysis.stats

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Total Agents</CardTitle>
          <Users className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.totalAgents}</div>
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Compliant Agents</CardTitle>
          <UserCheck className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.compliantAgents}</div>
          <Progress 
            value={stats.complianceRate * 100} 
            className="mt-2"
          />
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Non-Compliant</CardTitle>
          <UserX className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.nonCompliantAgents}</div>
        </CardContent>
      </Card>

      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Avg. Completion Days</CardTitle>
          <Clock className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">{stats.averageCompletionDays}</div>
        </CardContent>
      </Card>

      <Card className="rounded-none md:col-span-2 lg:col-span-4">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="text-sm font-medium">Module Completion Rates</CardTitle>
          <Book className="h-4 w-4 text-muted-foreground" />
        </CardHeader>
        <CardContent>
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
            {stats.moduleCompletionRates.map((module: ModuleCompletionRate) => (
              <div key={module.moduleId} className="space-y-2">
                <div className="text-sm font-medium">{module.moduleName}</div>
                <Progress 
                  value={module.completionRate * 100} 
                  className="h-2"
                />
                <div className="text-xs text-muted-foreground">
                  {Math.round(module.completionRate * 100)}% Complete
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    </div>
  )
} 