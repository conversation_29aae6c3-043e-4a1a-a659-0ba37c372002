"use client"

import { Badge } from "@/app/components/ui/badge"
import { format } from "date-fns"

interface CompletedModule {
  moduleId: number
  moduleName: string
  completed: boolean
  completionDate: string | null
}

interface ModuleDetailsProps {
  modules: CompletedModule[]
}

export function ModuleDetails({ modules }: ModuleDetailsProps) {
  return (
    <div className="space-y-4 p-4 bg-background">
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {modules.map((module) => (
          <div 
            key={module.moduleId}
            className="flex flex-col space-y-2 p-4 border rounded-none bg-card"
          >
            <div className="flex justify-between items-start">
              <span className="font-medium">{module.moduleName}</span>
              <Badge variant={module.completed ? "success" : "secondary"}>
                {module.completed ? "Completed" : "Pending"}
              </Badge>
            </div>
            {module.completionDate && (
              <span className="text-sm text-muted-foreground">
                Completed on: {format(new Date(module.completionDate), "PPP")}
              </span>
            )}
          </div>
        ))}
      </div>
    </div>
  )
} 