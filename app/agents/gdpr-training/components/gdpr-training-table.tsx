"use client"

import { useQuery } from "@apollo/client"
import { gql } from "@apollo/client"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Button } from "@/app/components/ui/button"
import { ChevronDown, ChevronUp } from "lucide-react"
import { useState } from "react"
import { ModuleDetails } from "./module-details"
import { format } from "date-fns"

const GET_REGULATORY_TRAINING = gql`
  query GetRegulatoryTrainingAnalysis {
    getRegulatoryTrainingAnalysis {
      agents {
        agentId
        pin
        firstName
        lastName
        email
        group
        status
        createdDate
        ukRegExamDate
        allModulesCompleted
        lastCompletionDate
        daysToCompletion
        daysOverdue
        completedModules {
          moduleId
          moduleName
          completed
          completionDate
        }
      }
    }
  }
`

interface Agent {
  agentId: string
  pin: string
  firstName: string
  lastName: string
  email: string
  group: string
  status: string
  createdDate: string
  allModulesCompleted: boolean
  completedModules: {
    moduleId: number
    moduleName: string
    completed: boolean
    completionDate: string | null
  }[]
}

export function GdprTrainingTable() {
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set())
  const { data, loading, error } = useQuery(GET_REGULATORY_TRAINING)

  if (loading) return <div>Loading...</div>
  if (error) return <div>Error loading data</div>

  const toggleRow = (agentId: string) => {
    const newExpandedRows = new Set(expandedRows)
    if (expandedRows.has(agentId)) {
      newExpandedRows.delete(agentId)
    } else {
      newExpandedRows.add(agentId)
    }
    setExpandedRows(newExpandedRows)
  }

  return (
    <div className="border bg-card">
      <Table>
        <TableHeader>
          <TableRow className="hover:bg-transparent">
            <TableHead className="w-[50px]"></TableHead>
            <TableHead>Agent ID</TableHead>
            <TableHead>Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead>Group</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Created Date</TableHead>
            <TableHead>Completion Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.getRegulatoryTrainingAnalysis.agents.map((agent: Agent) => (
            <>
              <TableRow key={agent.agentId} className="hover:bg-muted/50">
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleRow(agent.agentId)}
                  >
                    {expandedRows.has(agent.agentId) ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </Button>
                </TableCell>
                <TableCell>{agent.pin}</TableCell>
                <TableCell>{`${agent.firstName} ${agent.lastName}`}</TableCell>
                <TableCell>{agent.email}</TableCell>
                <TableCell>{agent.group}</TableCell>
                <TableCell>{agent.status}</TableCell>
                <TableCell>
                  {format(new Date(agent.createdDate), "PPP")}
                </TableCell>
                <TableCell>
                  {agent.allModulesCompleted ? "Completed" : "In Progress"}
                </TableCell>
              </TableRow>
              {expandedRows.has(agent.agentId) && (
                <TableRow className="hover:bg-transparent">
                  <TableCell colSpan={8} className="p-0 border-b">
                    <ModuleDetails modules={agent.completedModules} />
                  </TableCell>
                </TableRow>
              )}
            </>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 