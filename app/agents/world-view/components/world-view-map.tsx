"use client"

import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import { useEffect, useState } from "react"
import { GoogleMap, useLoadScript, OverlayView, MarkerF } from "@react-google-maps/api"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Label } from "@/app/components/ui/label"
import { Filter, Download, ChevronDown, ChevronUp } from "lucide-react"
import { Button } from "@/app/components/ui/button"
import { DatePicker } from "@/app/components/ui/date-picker"
import { Input } from "@/app/components/ui/input"

interface AgentLocation {
  countryName: string
  agentCount: number
  latitude: number
  longitude: number
  cities: Array<{
    name: string
    lat: number
    lng: number
    count: number
  }>
}

const dummyData: AgentLocation[] = [
  {
    countryName: "United States",
    agentCount: 15000,
    latitude: 39.4419,
    longitude: -97.1419,
    cities: [
      { name: "New York", lat: 40.7128, lng: -74.0060, count: 3500 },
      { name: "Los Angeles", lat: 34.0522, lng: -118.2437, count: 2800 },
      { name: "Chicago", lat: 41.8781, lng: -87.6298, count: 2000 },
      { name: "Miami", lat: 25.7617, lng: -80.1918, count: 1800 }
    ]
  },
  {
    countryName: "United Kingdom",
    agentCount: 5000,
    latitude: 54.7023,
    longitude: -3.2765,
    cities: [
      { name: "London", lat: 51.5074, lng: -0.1278, count: 2000 },
      { name: "Manchester", lat: 53.4808, lng: -2.2426, count: 800 },
      { name: "Birmingham", lat: 52.4862, lng: -1.8904, count: 600 }
    ]
  },
  {
    countryName: "Mexico",
    agentCount: 3000,
    latitude: 23.6345,
    longitude: -102.5528,
    cities: [
      { name: "Mexico City", lat: 19.4326, lng: -99.1332, count: 1200 },
      { name: "Guadalajara", lat: 20.6597, lng: -103.3496, count: 800 },
      { name: "Monterrey", lat: 25.6866, lng: -100.3161, count: 600 }
    ]
  }
]

const mapOptions = {
  styles: [
    {
      featureType: "administrative",
      elementType: "geometry",
      stylers: [{ visibility: "on" }]
    },
    {
      featureType: "water",
      elementType: "geometry",
      stylers: [{ color: "#e9e9e9" }]
    }
  ],
  mapTypeControl: false,
  streetViewControl: false
}

export function WorldViewMap() {
  const { isLoaded, loadError } = useLoadScript({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY!
  })

  if (loadError) {
    return (
      <Card className="p-6 text-center text-destructive">
        Failed to load Google Maps
      </Card>
    )
  }

  if (!isLoaded) {
    return (
      <Card className="rounded-none shadow-none overflow-hidden relative">
        <div className="absolute inset-0 bg-background/50 flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary" />
        </div>
        <div className="w-full h-[768px]" />
      </Card>
    )
  }

  return <Map />
}

function Map() {
  const center = { lat: 39.4419, lng: -97.1419 }
  const [selectedLocation, setSelectedLocation] = useState<string | null>(null)
  const [selectedCountry, setSelectedCountry] = useState<string>("all")
  const [isCollapsed, setIsCollapsed] = useState(false)

  const filteredData = selectedCountry === "all" 
    ? dummyData
    : dummyData.filter(location => {
        switch (selectedCountry) {
          case "us":
            return location.countryName === "United States"
          case "uk":
            return location.countryName === "United Kingdom"
          case "ie":
            return location.countryName === "Ireland"
          case "mx":
            return location.countryName === "Mexico"
          default:
            return true
        }
      })

  return (
    <div className="space-y-4">
      <FilterForm />

      <Card className="rounded-none shadow-none overflow-hidden">
        <GoogleMap
          zoom={3}
          center={center}
          mapContainerClassName="w-full h-[768px]"
          options={mapOptions}
        >
          {filteredData.map((location) => (
            <div key={location.countryName}>
              {/* Country Label */}
              <OverlayView
                key={`label-${location.countryName}`}
                position={{ lat: location.latitude, lng: location.longitude }}
                mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
              >
                <div className="px-3 py-1.5 bg-primary text-white shadow-sm text-sm font-medium rounded-sm whitespace-nowrap cursor-default transform -translate-x-1/2 -translate-y-full">
                  {location.countryName}: {location.agentCount.toLocaleString()}
                </div>
              </OverlayView>

              {/* City Markers */}
              {location.cities.map((city) => (
                <MarkerF
                  key={`marker-${city.name}`}
                  position={{ lat: city.lat, lng: city.lng }}
                  onClick={() => setSelectedLocation(city.name)}
                >
                  {selectedLocation === city.name && (
                    <OverlayView
                      position={{ lat: city.lat, lng: city.lng }}
                      mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
                    >
                      <div className="px-3 py-1.5 bg-white border shadow-md text-sm font-medium rounded-sm whitespace-nowrap cursor-default transform -translate-x-1/2 -translate-y-full">
                        {city.name}: {city.count.toLocaleString()} agents
                      </div>
                    </OverlayView>
                  )}
                </MarkerF>
              ))}
            </div>
          ))}
        </GoogleMap>
      </Card>
    </div>
  )
}

function FilterForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [selectedCountry, setSelectedCountry] = useState<string>("all")
  const [startDate, setStartDate] = useState<Date>()
  const [endDate, setEndDate] = useState<Date>()
  const [email, setEmail] = useState("")

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // TODO: Implement filter logic
  }

  const handleReset = () => {
    setSelectedCountry("all")
    setStartDate(undefined)
    setEndDate(undefined)
    setEmail("")
  }

  return (
    <Card className="rounded-none shadow-none">
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
        <div className="flex items-center space-x-2">
          <Filter className="h-5 w-5" />
          <h2 className="text-lg font-semibold">Filter Options</h2>
        </div>
        <div className="flex items-center gap-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={handleReset}
            className="rounded-none"
          >
            Reset
          </Button>
          <Button
            size="sm"
            onClick={handleSubmit}
            className="rounded-none"
          >
            Apply Filters
          </Button>
        </div>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="p-6">
          <form className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <div className="grid gap-2">
                <Label htmlFor="startDate">From Date</Label>
                <DatePicker
                  date={startDate}
                  setDate={(date: Date | null) => setStartDate(date || undefined)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="endDate">To Date</Label>
                <DatePicker
                  date={endDate}
                  setDate={(date: Date | null) => setEndDate(date || undefined)}
                />
              </div>
              <div className="grid gap-2">
                <Label htmlFor="region">Region</Label>
                <Select
                  value={selectedCountry}
                  onValueChange={setSelectedCountry}
                >
                  <SelectTrigger id="region" className="rounded-none">
                    <SelectValue placeholder="Select region" />
                  </SelectTrigger>
                  <SelectContent className="rounded-none">
                    <SelectItem value="all">All Regions</SelectItem>
                    <SelectItem value="us">United States</SelectItem>
                    <SelectItem value="uk">United Kingdom</SelectItem>
                    <SelectItem value="ie">Ireland</SelectItem>
                    <SelectItem value="mx">Mexico</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="grid gap-2">
                <Label htmlFor="email">Email for CSV</Label>
                <Input
                  id="email"
                  type="email"
                  placeholder="Enter email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="rounded-none"
                />
              </div>
            </div>
          </form>
        </CardContent>
      )}
    </Card>
  )
} 