"use client"

import { useState } from "react"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import { Button } from "@/app/components/ui/button"
import { Mail } from "lucide-react"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"

const EMAIL_RECIPIENTS = [
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "<PERSON>", value: "j<PERSON><EMAIL>" },
  { label: "<PERSON>", value: "<EMAIL>" },
  { label: "Tricia", value: "<EMAIL>" }
]

export function EmailReportDialog() {
  const [selectedEmail, setSelectedEmail] = useState<string>("")

  const handleSendReport = async () => {
    if (!selectedEmail) return
    // API call would go here
    console.log("Sending report to:", selectedEmail)
  }

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline">
          <Mail className="mr-2 h-4 w-4" />
          Email Report
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Send Registration Report</DialogTitle>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <Select value={selectedEmail} onValueChange={setSelectedEmail}>
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select recipient" />
            </SelectTrigger>
            <SelectContent>
              {EMAIL_RECIPIENTS.map((recipient) => (
                <SelectItem key={recipient.value} value={recipient.value}>
                  {recipient.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Button onClick={handleSendReport} disabled={!selectedEmail}>
            Send Report
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  )
} 