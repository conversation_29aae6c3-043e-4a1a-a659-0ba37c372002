"use client"

import {
  Table,
  TableBody,
  Table<PERSON>ell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { UKRegistrationData } from "@/app/lib/api/uk-registration"
import { useState } from "react"

interface UkRegistrationTableProps {
  initialData: UKRegistrationData[]
}

export function UkRegistrationTable({ initialData }: UkRegistrationTableProps) {
  const [data] = useState(initialData)

  return (
    <div className="rounded-md border overflow-x-auto">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="whitespace-nowrap">PIN</TableHead>
            <TableHead className="whitespace-nowrap">Title</TableHead>
            <TableHead className="whitespace-nowrap">First Name</TableHead>
            <TableHead className="whitespace-nowrap">Last Name</TableHead>
            <TableHead className="whitespace-nowrap">Date of Birth</TableHead>
            <TableHead className="whitespace-nowrap">Email</TableHead>
            <TableHead className="whitespace-nowrap">Business Phone</TableHead>
            <TableHead className="whitespace-nowrap">Mobile Phone</TableHead>
            <TableHead className="whitespace-nowrap">Address 1</TableHead>
            <TableHead className="whitespace-nowrap">Address 2</TableHead>
            <TableHead className="whitespace-nowrap">Town</TableHead>
            <TableHead className="whitespace-nowrap">County</TableHead>
            <TableHead className="whitespace-nowrap">Postcode</TableHead>
            <TableHead className="whitespace-nowrap">Website</TableHead>
            <TableHead className="whitespace-nowrap">Brand</TableHead>
            <TableHead className="whitespace-nowrap">Start Date</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((row) => (
            <TableRow key={row.pin}>
              <TableCell className="whitespace-nowrap">{row.pin || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.title || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.firstName || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.lastName || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.dateOfBirth || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.emailAddress || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.businessPhone || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.mobilePhone || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.address1 || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.address2 || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.town || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.county || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.postcode || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.website || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.brand || 'N/A'}</TableCell>
              <TableCell className="whitespace-nowrap">{row.dateOfCommencement || 'N/A'}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 