import { Metada<PERSON> } from "next"
import { UkRegistrationTable } from "./components/uk-registration-table"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Users, Download } from "lucide-react"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { EmailReportDialog } from "./components/email-report-dialog"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { Suspense } from "react"
import { fetchUKRegistrationData } from "@/app/lib/api/uk-registration"

export const metadata: Metadata = {
  title: "UK Agent Registration | Agents",
  description: "View and export UK agent registration data"
}

async function UkRegistrationContent() {
  console.log('Fetching data...')
  const data = await fetchUKRegistrationData()
  console.log('Data received:', data)
  
  return (
    <>
      <Card className="rounded-none">
        <CardHeader className="flex flex-row items-center space-y-0 pb-2">
          <CardTitle className="text-md font-medium flex items-center gap-2">
            <Users className="h-4 w-4" />
            UK Registered Agents
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-2xl font-bold">
            Total Registered: {data.totalAgents}
          </div>
        </CardContent>
      </Card>

      <UkRegistrationTable initialData={data.reportData} />
    </>
  )
}

export default function UkRegistrationPage() {
  return (
    <div className="flex flex-col gap-4 p-6">
      <Breadcrumb />
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-semibold tracking-tight">UK Agent Registration Report</h1>
        <div className="flex gap-2">
          <EmailReportDialog />
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Export Excel
          </Button>
        </div>
      </div>
      
      <Suspense fallback={
        <div className="flex items-center justify-center p-8">
          <div className="animate-pulse text-muted-foreground">Loading data...</div>
        </div>
      }>
        <UkRegistrationContent />
      </Suspense>
    </div>
  )
} 