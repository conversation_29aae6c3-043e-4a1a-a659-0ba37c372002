import type { Metadata } from "next"
import "./globals.css"
import { MainLayout } from './components/layout/main-layout'
import { RelayProvider } from './components/providers/relay-provider'
import { SearchParamsProvider } from './components/providers/search-params-provider'
import { cn } from '@/lib/utils'
import { Toaster } from "@/app/components/ui/toaster"
import { ApolloProviderWrapper } from "./providers/apollo-provider"
import { SidebarProvider } from './contexts/SidebarContext'
import { Toaster as SonnerToaster } from "sonner"

export const metadata: Metadata = {
  title: "Booking Search",
  description: "Search and manage bookings",
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="en" suppressHydrationWarning>
      <body className={cn('min-h-screen bg-background font-sans antialiased')}>
        <ApolloProviderWrapper>
          <RelayProvider>
            <SearchParamsProvider>
              <SidebarProvider>
                <MainLayout>{children}</MainLayout>
              </SidebarProvider>
            </SearchParamsProvider>
          </RelayProvider>
        </ApolloProviderWrapper>
        <Toaster />
        <SonnerToaster />
      </body>
    </html>
  )
}
