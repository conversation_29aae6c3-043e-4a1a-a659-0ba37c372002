import { GraphQLScalarType } from 'graphql'

export const BitFieldScalar = new GraphQLScalarType({
  name: 'BitField',
  description: 'MySQL BIT(1) scalar type',
  
  serialize(value) {
    // Convert Buffer to boolean when sending to client
    if (Buffer.isBuffer(value)) {
      return value[0] === 1
    }
    return <PERSON>olean(value)
  },
  
  parseValue(value) {
    // Convert boolean to Buffer when receiving from client
    return Buffer.from([Boolean(value) ? 1 : 0])
  },
  
  parseLiteral(ast) {
    if (ast.kind === 'BooleanValue') {
      return Buffer.from([ast.value ? 1 : 0])
    }
    return null
  },
}) 