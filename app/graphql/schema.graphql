# ... existing schema types ...

input BookingFiltersInput {
  iBookingID: NumberFilterInput
  vcConfirmationID: StringFilterInput
  dTravelStartDate: DateFilterInput
  nBookingStatusID: NumberFilterInput
}

input NumberFilterInput {
  operator: FilterOperator!
  value: Float!
}

input StringFilterInput {
  operator: StringFilterOperator!
  value: String!
}

input DateFilterInput {
  operator: FilterOperator!
  value: DateTime!
}

enum FilterOperator {
  eq
  gt
  lt
  gte
  lte
}

enum StringFilterOperator {
  eq
  contains
}

scalar DateTime
scalar BitField

type BookingSearchResult {
  iBookingID: Int!
  vcPIN: String!
  vcFreeUserID: String
  nEngineID: Int!
  nBookingTypeID: Int!
  vcConfirmationID: String!
  nBookingStatusID: Int
  vcClientName: String!
  vcLiftCity: String
  vcFinalDestination: String!
  nNumberOfPsgrs: Int
  vcGDS: String
  vcVendor: String!
  vcVendorAddress: String
  vcVendorCityState: String
  vcVendorPostalCode: String
  vcVendorNumber: String
  vcVendorFax: String
  dBooked: DateTime!
  dTravelStartDate: DateTime
  dTravelEndDate: DateTime
  nTotalCharges: Float
  nAdminFee: Float
  nCommissionRate: Float
  nTotalCommission: Float
  bIATANQual: BitField!
  nInteleTravelCommission: Float
  nAgentCommission: Float
  nVendorCheckID: Int
  vcVendorCheckNum: String
  dVendorPaid: DateTime
  bInTheBank: BitField!
  vcITCheckNumber: String
  dAgentPaid: DateTime
  vcCancelConfirmation: String
  txtComments: String
  nCheckRunID: Int
  dCheckRunDate: DateTime
  bActive: BitField
  dModified: DateTime
  dCreated: DateTime
  nUserModifiedID: Int
  nUserCreatedID: Int
  dPending: DateTime
  bNonCommissionable: BitField
  dECS: DateTime
  nOrderID: Int
  nSegmentID: Int
  totalChargesCurrency: String
  nSupplierID: Int
  nDepositAmount: Float
  nProcessingFee: Float
  vcconfirmationidnew: String
  nSalesContactID: Int
  vcVendorID: Int
  vcTripID: String
}

type PaginatedBookingResults {
  items: [BookingSearchResult!]!
  total: Int!
  totalPages: Int!
  page: Int!
  pageSize: Int!
}

type Query {
  searchBookings(
    page: Int!
    pageSize: Int!
    filters: BookingFiltersInput
  ): PaginatedBookingResults!
  # ... other existing queries ...
} 