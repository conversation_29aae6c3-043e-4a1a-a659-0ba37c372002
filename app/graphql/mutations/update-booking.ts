import { gql } from '@apollo/client'

export const UPDATE_BOOKING = gql`
  mutation UpdateBooking($bookingId: Int!, $input: UpdateBookingInput!) {
    updateBooking(bookingId: $bookingId, input: $input) {
      success
      message
      booking {
        iBookingID
        vcClientName
        vcPIN
        vcConfirmationID
        dTravelStartDate
        dTravelEndDate
        vcFinalDestination
        nBookingStatusID
        nBookingTypeID
        nTotalCharges
        totalChargesCurrency
        vcVendor
        vcGDS
        vcVendorAddress
        vcVendorCityState
        vcVendorPostalCode
        vcVendorNumber
        nEngineID
        vcLiftCity
        nNumberOfPsgrs
        dBooked
        vcTripID
        nSalesContactID
        nCommissionRate
        nAdminFee
        nTotalCommission
        nAgentCommission
        bActive
        bNonCommissionable
        txtComments
        vcCancelConfirmation
        vcVendorFax
        nProcessingFee
        __typename
      }
      __typename
    }
  }
`

// Helper function to prepare the mutation variables
export const updateBookingMutation = (bookingId: number, bookingData: any) => ({
  variables: {
    bookingId,
    input: bookingData
  }
}) 