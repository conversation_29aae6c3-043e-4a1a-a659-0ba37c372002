export const typeDefs = `#graphql
  type BookingSearchResult {
    iBookingID: Int!
    vcConfirmationID: String!
    dTravelStartDate: String
    dTravelEndDate: String
    vcClientName: String!
    vcFinalDestination: String!
    vcPIN: String!
    vcTripID: String
    nEngineID: Int!
    nSalesContactID: Int
    nBookingStatusID: Int
  }

  type SearchResponse {
    items: [BookingSearchResult!]!
    total: Int!
    totalPages: Int!
    page: Int!
    pageSize: Int!
  }

  type Query {
    searchBookings(page: Int!, pageSize: Int!, filters: BookingFiltersInput): SearchResponse!
  }

  input BookingFiltersInput {
    iBookingID: FilterInput
    vcConfirmationID: FilterInput
    dTravelStartDate: FilterInput
    nBookingStatusID: FilterInput
  }

  input FilterInput {
    operator: String!
    value: String!
  }
` 