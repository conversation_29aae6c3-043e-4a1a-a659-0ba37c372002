import { gql } from '@apollo/client'

export const GET_BOOKING_BY_ID = gql`
  query GetBookingById($bookingId: Int!) {
    getBookingById(bookingId: $bookingId) {
      success
      message
      booking {
        iBookingID
        vcClientName
        vcPIN
        vcConfirmationID
        dTravelStartDate
        dTravelEndDate
        vcFinalDestination
        nBookingStatusID
        nBookingTypeID
        nTotalCharges
        totalChargesCurrency
        vcVendor
        vcGDS
        vcVendorAddress
        vcVendorCityState
        vcVendorPostalCode
        vcVendorNumber
        nEngineID
        vcLiftCity
        nNumberOfPsgrs
        dBooked
        vcTripID
        nSalesContactID
        nCommissionRate
        nAdminFee
        nTotalCommission
        nAgentCommission
        bActive
        txtComments
      }
    }
  }
`

// Helper function to ensure bookingId is provided
export const getBookingByIdQuery = (bookingId: number) => ({
  query: GET_BOOKING_BY_ID,
  variables: { bookingId },
}) 