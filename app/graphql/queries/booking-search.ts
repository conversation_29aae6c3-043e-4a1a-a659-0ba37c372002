import { gql } from '@apollo/client'

export interface BookingFiltersInput {
  iBookingID?: {
    operator: 'eq' | 'gt' | 'lt' | 'gte' | 'lte'
    value: number
  }
  vcConfirmationID?: {
    operator: 'eq' | 'contains'
    value: string
  }
  dTravelStartDate?: {
    operator: 'eq' | 'gt' | 'lt' | 'gte' | 'lte'
    value: string
  }
  nBookingStatusID?: {
    operator: 'eq'
    value: number
  }
}

export const BOOKING_SEARCH_QUERY = gql`
  query bookingSearchQuery(
    $page: Int!
    $pageSize: Int!
    $filters: BookingFiltersInput
  ) {
    searchBookings(page: $page, pageSize: $pageSize, filters: $filters) {
      items {
        iBookingID
        vcPIN
        vcFreeUserID
        nEngineID
        nBookingTypeID
        vcConfirmationID
        nBookingStatusID
        vcClientName
        vcLiftCity
        vcFinalDestination
        nNumberOfPsgrs
        vcGDS
        vcVendor
        vcVendorAddress
        vcVendorCityState
        vcVendorPostalCode
        vcVendorNumber
        vcVendorFax
        dBooked
        dTravelStartDate
        dTravelEndDate
        nTotalCharges
        nAdminFee
        nCommissionRate
        nTotalCommission
        bIATANQual
        nInteleTravelCommission
        nAgentCommission
        nVendorCheckID
        vcVendorCheckNum
        dVendorPaid
        bInTheBank
        vcITCheckNumber
        dAgentPaid
        vcCancelConfirmation
        txtComments
        nCheckRunID
        dCheckRunDate
        bActive
        dModified
        dCreated
        nUserModifiedID
        nUserCreatedID
        dPending
        bNonCommissionable
        dECS
        nOrderID
        nSegmentID
        totalChargesCurrency
        nSupplierID
        nDepositAmount
        nProcessingFee
        vcconfirmationidnew
        nSalesContactID
        vcVendorID
        vcTripID
      }
      total
      totalPages
      page
      pageSize
    }
  }
`

export interface BookingSearchResult {
  iBookingID: number
  vcPIN: string
  vcFreeUserID?: string
  nEngineID: number
  nBookingTypeID: number
  vcConfirmationID: string
  nBookingStatusID?: number
  vcClientName: string
  vcLiftCity?: string
  vcFinalDestination: string
  nNumberOfPsgrs?: number
  vcGDS?: string
  vcVendor: string
  vcVendorAddress?: string
  vcVendorCityState?: string
  vcVendorPostalCode?: string
  vcVendorNumber?: string
  vcVendorFax?: string
  dBooked: string
  dTravelStartDate?: string
  dTravelEndDate?: string
  nTotalCharges?: number
  nAdminFee?: number
  nCommissionRate?: number
  nTotalCommission?: number
  bIATANQual: boolean
  nInteleTravelCommission?: number
  nAgentCommission?: number
  nVendorCheckID?: number
  vcVendorCheckNum?: string
  dVendorPaid?: string
  bInTheBank: boolean
  vcITCheckNumber?: string
  dAgentPaid?: string
  vcCancelConfirmation?: string
  txtComments?: string
  nCheckRunID?: number
  dCheckRunDate?: string
  bActive?: boolean
  dModified?: string
  dCreated?: string
  nUserModifiedID?: number
  nUserCreatedID?: number
  dPending?: string
  bNonCommissionable?: number
  dECS?: string
  nOrderID?: number
  nSegmentID?: number
  totalChargesCurrency?: string
  nSupplierID?: number
  nDepositAmount?: number
  nProcessingFee?: number
  vcconfirmationidnew?: string
  nSalesContactID?: number
  vcVendorID?: number
  vcTripID?: string
}

export interface BookingSearchResponse {
  searchBookings: {
    items: BookingSearchResult[]
    total: number
    page: number
    pageSize: number
    totalPages: number
  }
} 