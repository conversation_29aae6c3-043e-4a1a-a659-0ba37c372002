"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Input } from "@/components/ui/input"
import { DatePicker } from "@/components/ui/date-picker"
import { Filter, X, ChevronDown, ChevronUp } from "lucide-react"

interface FilterCriteria {
  field: string
  operator: string
  value: string | Date | null
}

const FILTER_FIELDS = [
  { label: "PIN", value: "pin", type: "text" },
  { label: "Booking Type", value: "bookingType", type: "select" },
  { label: "Booked Date", value: "bookedDate", type: "date" },
  { label: "Total Charges", value: "totalCharges", type: "number" },
  { label: "Status", value: "status", type: "select" },
  // Add more fields as needed
]

export function SellersReportFilters() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [filters, setFilters] = useState<FilterCriteria[]>([])

  const addFilter = () => {
    setFilters([...filters, { field: "", operator: "equals", value: "" }])
  }

  const removeFilter = (index: number) => {
    setFilters(filters.filter((_, i) => i !== index))
  }

  const updateFilter = (index: number, updates: Partial<FilterCriteria>) => {
    setFilters(
      filters.map((filter, i) => 
        i === index ? { ...filter, ...updates } : filter
      )
    )
  }

  const resetFilters = () => {
    setFilters([])
  }

  const applyFilters = () => {
    // Implement filter application logic
    console.log("Applying filters:", filters)
  }

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-md font-medium flex items-center gap-2">
          <Filter className="h-4 w-4" />
          Filters
        </CardTitle>
        <div className="flex gap-2">
          <Button
            variant="outline" 
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
          >
            {isCollapsed ? <ChevronDown className="h-4 w-4" /> : <ChevronUp className="h-4 w-4" />}
          </Button>
          <Button
            variant="outline"
            size="sm"
            onClick={resetFilters}
          >
            Reset
          </Button>
          <Button 
            size="sm"
            onClick={applyFilters}
          >
            Apply
          </Button>
        </div>
      </CardHeader>
      {!isCollapsed && (
        <CardContent className="pt-4">
          <div className="space-y-4">
            {filters.map((filter, index) => (
              <div key={index} className="flex gap-2">
                <Select
                  value={filter.field}
                  onValueChange={(value) => updateFilter(index, { field: value })}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select field" />
                  </SelectTrigger>
                  <SelectContent>
                    {FILTER_FIELDS.map((field) => (
                      <SelectItem key={field.value} value={field.value}>
                        {field.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select
                  value={filter.operator}
                  onValueChange={(value) => updateFilter(index, { operator: value })}
                >
                  <SelectTrigger className="w-[200px]">
                    <SelectValue placeholder="Select operator" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="equals">Equals</SelectItem>
                    <SelectItem value="contains">Contains</SelectItem>
                    <SelectItem value="greater">Greater than</SelectItem>
                    <SelectItem value="less">Less than</SelectItem>
                  </SelectContent>
                </Select>

                {FILTER_FIELDS.find(f => f.value === filter.field)?.type === "date" ? (
                  <DatePicker
                    value={filter.value as Date}
                    onChange={(date) => updateFilter(index, { value: date })}
                  />
                ) : (
                  <Input
                    value={filter.value as string}
                    onChange={(e) => updateFilter(index, { value: e.target.value })}
                    placeholder="Enter value"
                  />
                )}

                <Button
                  variant="ghost"
                  size="icon"
                  onClick={() => removeFilter(index)}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            ))}
            <Button variant="outline" onClick={addFilter}>
              Add Filter
            </Button>
          </div>
        </CardContent>
      )}
    </Card>
  )
} 