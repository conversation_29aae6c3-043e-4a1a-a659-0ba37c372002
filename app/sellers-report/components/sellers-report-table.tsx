"use client"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"

// Dummy data for demonstration
const DUMMY_DATA = [
  {
    pin: "12345",
    firstName: "<PERSON>",
    lastName: "Doe",
    email: "<EMAIL>",
    totalCharges: 5000,
    totalCommission: 500,
    agentCommission: 250,
    address: "123 Main St",
    address2: "Apt 4B",
    city: "New York",
    state: "NY",
    zip: "10001",
    phone: "555-0123"
  },
  // Add more dummy records...
]

export function SellersReportTable() {
  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>PIN</TableHead>
            <TableHead>First Name</TableHead>
            <TableHead>Last Name</TableHead>
            <TableHead>Email</TableHead>
            <TableHead className="text-right">Total Charges</TableHead>
            <TableHead className="text-right">Total Commission</TableHead>
            <TableHead className="text-right">Agent Commission</TableHead>
            <TableHead>Address</TableHead>
            <TableHead>Address 2</TableHead>
            <TableHead>City</TableHead>
            <TableHead>State</TableHead>
            <TableHead>ZIP</TableHead>
            <TableHead>Phone</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {DUMMY_DATA.map((row, index) => (
            <TableRow key={index}>
              <TableCell>{row.pin}</TableCell>
              <TableCell>{row.firstName}</TableCell>
              <TableCell>{row.lastName}</TableCell>
              <TableCell>{row.email}</TableCell>
              <TableCell className="text-right">${row.totalCharges.toFixed(2)}</TableCell>
              <TableCell className="text-right">${row.totalCommission.toFixed(2)}</TableCell>
              <TableCell className="text-right">${row.agentCommission.toFixed(2)}</TableCell>
              <TableCell>{row.address}</TableCell>
              <TableCell>{row.address2}</TableCell>
              <TableCell>{row.city}</TableCell>
              <TableCell>{row.state}</TableCell>
              <TableCell>{row.zip}</TableCell>
              <TableCell>{row.phone}</TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 