"use client"

import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/app/components/ui/tabs"
import { useState } from "react"
import { ChevronDown, ChevronUp, Plus, Download } from "lucide-react"
import { Button } from "@/app/components/ui/button"

interface CommissionData {
  pin: string
  commissionAmount: number
  overrideAmount: number
  totalAmount: number
  travelEndDate: string
  bookingStatus: string
}

interface RegionSummary {
  commissions: number
  overrides: number
  details: CommissionData[]
}

const dummyData = {
  uk: {
    commissions: 25000.00,
    overrides: 5000.00,
    details: [
      {
        pin: "UK123",
        commissionAmount: 1200.00,
        overrideAmount: 300.00,
        totalAmount: 1500.00,
        travelEndDate: "2024-03-01",
        bookingStatus: "Active"
      }
    ]
  },
  us: {
    commissions: 35000.00,
    overrides: 7000.00,
    details: [/* similar structure */]
  },
  mx: {
    commissions: 450000.00,
    overrides: 90000.00,
    details: [/* similar structure */]
  }
}

export function CommissionCleanupForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [data] = useState(dummyData)
  const [activeTab, setActiveTab] = useState("uk")

  const handleExportCsv = () => {
    console.log("Exporting CSV...")
  }

  const RegionSummary = ({ data, currency }: { data: RegionSummary, currency: string }) => (
    <div className="grid grid-cols-2 gap-6 mb-6">
      <div className="space-y-2">
        <h3 className="font-semibold">Commissions Total</h3>
        <p className="text-2xl font-medium">
          {currency}{data.commissions.toLocaleString()}
        </p>
      </div>
      <div className="space-y-2">
        <h3 className="font-semibold">Overrides Total</h3>
        <p className="text-2xl font-medium">
          {currency}{data.overrides.toLocaleString()}
        </p>
      </div>
    </div>
  )

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Commission Cleanup Run</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportCsv}
              className="rounded-none w-24"
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <Tabs defaultValue="uk" onValueChange={setActiveTab}>
              <TabsList>
                <TabsTrigger value="uk">United Kingdom</TabsTrigger>
                <TabsTrigger value="us">United States</TabsTrigger>
                <TabsTrigger value="mx" disabled>Mexico</TabsTrigger>
              </TabsList>

              <TabsContent value="uk">
                <RegionSummary data={data.uk} currency="£" />
              </TabsContent>
              <TabsContent value="us">
                <RegionSummary data={data.us} currency="$" />
              </TabsContent>
              <TabsContent value="mx">
                <RegionSummary data={data.mx} currency="MX$" />
              </TabsContent>
            </Tabs>
          </CardContent>
        )}
      </Card>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">Agent PIN</TableHead>
              <TableHead className="font-semibold text-right">Commission</TableHead>
              <TableHead className="font-semibold text-right">Override</TableHead>
              <TableHead className="font-semibold text-right">Total</TableHead>
              <TableHead className="font-semibold">Travel End Date</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {data[activeTab as keyof typeof data].details.map((detail, index) => (
              <TableRow key={index} className="hover:bg-slate-50/50">
                <TableCell>
                  <span className="text-primary hover:underline cursor-pointer">
                    {detail.pin}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  {activeTab === 'uk' ? '£' : activeTab === 'us' ? '$' : 'MX$'}
                  {detail.commissionAmount.toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  {activeTab === 'uk' ? '£' : activeTab === 'us' ? '$' : 'MX$'}
                  {detail.overrideAmount.toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  {activeTab === 'uk' ? '£' : activeTab === 'us' ? '$' : 'MX$'}
                  {detail.totalAmount.toLocaleString()}
                </TableCell>
                <TableCell>{detail.travelEndDate}</TableCell>
                <TableCell>{detail.bookingStatus}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 