"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { But<PERSON> } from "@/app/components/ui/button"
import { Calendar } from "@/app/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/app/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import { Label } from "@/app/components/ui/label"
import { useState } from "react"
import { format } from "date-fns"
import { Calendar as CalendarIcon, ChevronDown, ChevronUp, Download } from "lucide-react"
import { cn } from "@/app/lib/utils"

interface CheckRunResult {
  checkRunId: number
  amount: number
  count: number
}

interface CheckRunSummary {
  totalAmount: number
  totalChecks: number
  details: CheckRunResult[]
}

export function CheckRunAmountsForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [date, setDate] = useState<Date>()
  const [country, setCountry] = useState<string>()
  const [results, setResults] = useState<CheckRunSummary>()
  const [isLoading, setIsLoading] = useState(false)

  const handleSubmit = async () => {
    if (!date || !country) return

    setIsLoading(true)
    // TODO: Implement API call
    // Simulating API call with dummy data
    setTimeout(() => {
      setResults({
        totalAmount: 250000,
        totalChecks: 150,
        details: [
          { checkRunId: 1, amount: 100000, count: 50 },
          { checkRunId: 2, amount: 150000, count: 100 }
        ]
      })
      setIsLoading(false)
    }, 1000)
  }

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <CalendarIcon className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Check Run Details</h2>
          </div>
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={() => setIsCollapsed(!isCollapsed)}
            className="h-8 w-8 p-0"
          >
            {isCollapsed ? (
              <ChevronDown className="h-4 w-4" />
            ) : (
              <ChevronUp className="h-4 w-4" />
            )}
          </Button>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="space-y-8">
              <div className="grid grid-cols-[120px,1fr] items-center gap-4">
                <Label className="text-right">Select Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      variant={"outline"}
                      className={cn(
                        "w-[240px] justify-start text-left font-normal rounded-none",
                        !date && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {date ? format(date, "PPP") : "Pick a date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={date}
                      onSelect={setDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>

              <div className="grid grid-cols-[120px,1fr] items-center gap-4">
                <Label className="text-right">Select Country</Label>
                <RadioGroup
                  onValueChange={setCountry}
                  className="flex space-x-6"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="US" id="us" />
                    <Label htmlFor="us">United States</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="UK" id="uk" />
                    <Label htmlFor="uk">United Kingdom</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="IE" id="ie" />
                    <Label htmlFor="ie">Ireland</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="grid grid-cols-[120px,1fr] items-center gap-4">
                <div className="text-right">
                  <Button 
                    onClick={handleSubmit}
                    disabled={!date || !country || isLoading}
                    className="rounded-none w-[240px]"
                  >
                    Get Check Run Details
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      {results && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <Card className="rounded-none shadow-none bg-slate-50/50">
              <CardContent className="p-8">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Total Commission Payout</h3>
                  <p className="text-3xl font-semibold text-primary">
                    ${results.totalAmount.toLocaleString()}
                  </p>
                </div>
              </CardContent>
            </Card>

            <Card className="rounded-none shadow-none bg-slate-50/50">
              <CardContent className="p-8">
                <div className="space-y-2">
                  <h3 className="text-sm font-medium text-muted-foreground">Total Checks</h3>
                  <p className="text-3xl font-semibold text-primary">
                    {results.totalChecks.toLocaleString()}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>

          <div className="border rounded-none overflow-hidden">
            <Table>
              <TableHeader>
                <TableRow className="bg-slate-50">
                  <TableHead className="font-medium text-sm w-[200px]">Check Run ID</TableHead>
                  <TableHead className="font-medium text-sm text-right">Amount</TableHead>
                  <TableHead className="font-medium text-sm text-right w-[200px]">Check Count</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {results.details.map((result) => (
                  <TableRow 
                    key={result.checkRunId}
                    className="hover:bg-slate-50/50"
                  >
                    <TableCell className="font-medium">{result.checkRunId}</TableCell>
                    <TableCell className="text-right tabular-nums">
                      ${result.amount.toLocaleString()}
                    </TableCell>
                    <TableCell className="text-right tabular-nums">
                      {result.count.toLocaleString()}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </>
      )}
    </div>
  )
} 