"use client"

import { Card, CardContent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { ChevronDown, ChevronUp, Plus, Download } from "lucide-react"
import { Button } from "@/app/components/ui/button"

interface RegionSummary {
  commissions: number
  overrides: number
  currency: string
}

interface CommissionDetail {
  pin: string
  commissionAmount: number
  overrideAmount: number
  totalAmount: number
}

interface RegionData {
  summary: RegionSummary
  details: CommissionDetail[]
}

const dummyData = {
  uk: {
    summary: {
      commissions: 25000.00,
      overrides: 5000.00,
      currency: "GBP"
    },
    details: [
      {
        pin: "UK123",
        commissionAmount: 1200.00,
        overrideAmount: 300.00,
        totalAmount: 1500.00
      }
    ]
  },
  us: {
    summary: {
      commissions: 35000.00,
      overrides: 7000.00,
      currency: "USD"
    },
    details: [
      {
        pin: "US456",
        commissionAmount: 2000.00,
        overrideAmount: 500.00,
        totalAmount: 2500.00
      }
    ]
  },
  mx: {
    summary: {
      commissions: 450000.00,
      overrides: 90000.00,
      currency: "MXN"
    },
    details: [
      {
        pin: "MX789",
        commissionAmount: 25000.00,
        overrideAmount: 5000.00,
        totalAmount: 30000.00
      }
    ]
  }
}

export function CommissionManagementForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [data] = useState(dummyData)

  const handleExportCsv = () => {
    console.log("Exporting CSV...")
  }

  const getCurrencySymbol = (currency: string) => {
    switch (currency) {
      case 'GBP': return '£'
      case 'USD': return '$'
      case 'MXN': return 'MX$'
      default: return ''
    }
  }

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Commission Summary</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportCsv}
              className="rounded-none w-24"
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <h3 className="text-lg font-semibold mb-4">UK Summary</h3>
                <div className="text-sm space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Commissions</span>
                    <span>£{data.uk.summary.commissions.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Overrides</span>
                    <span>£{data.uk.summary.overrides.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 font-medium text-primary">
                    <span>Total</span>
                    <span>£{(data.uk.summary.commissions + data.uk.summary.overrides).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">US Summary</h3>
                <div className="text-sm space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Commissions</span>
                    <span>${data.us.summary.commissions.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Overrides</span>
                    <span>${data.us.summary.overrides.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 font-medium text-primary">
                    <span>Total</span>
                    <span>${(data.us.summary.commissions + data.us.summary.overrides).toLocaleString()}</span>
                  </div>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">MX Summary</h3>
                <div className="text-sm space-y-2">
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Commissions</span>
                    <span>MX${data.mx.summary.commissions.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 border-b">
                    <span className="text-muted-foreground">Overrides</span>
                    <span>MX${data.mx.summary.overrides.toLocaleString()}</span>
                  </div>
                  <div className="flex justify-between py-2 font-medium text-primary">
                    <span>Total</span>
                    <span>MX${(data.mx.summary.commissions + data.mx.summary.overrides).toLocaleString()}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">Region</TableHead>
              <TableHead className="font-semibold">Agent PIN</TableHead>
              <TableHead className="font-semibold text-right">Commission</TableHead>
              <TableHead className="font-semibold text-right">Override</TableHead>
              <TableHead className="font-semibold text-right">Total</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {[
              ...data.uk.details.map(d => ({ ...d, region: 'UK', currency: '£' })),
              ...data.us.details.map(d => ({ ...d, region: 'US', currency: '$' })),
              ...data.mx.details.map(d => ({ ...d, region: 'MX', currency: 'MX$' }))
            ].map((detail, index) => (
              <TableRow 
                key={index} 
                className="hover:bg-slate-50/50"
              >
                <TableCell>{detail.region}</TableCell>
                <TableCell>
                  <span className="text-primary hover:underline cursor-pointer">
                    {detail.pin}
                  </span>
                </TableCell>
                <TableCell className="text-right">
                  {detail.currency}{detail.commissionAmount.toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  {detail.currency}{detail.overrideAmount.toLocaleString()}
                </TableCell>
                <TableCell className="text-right">
                  {detail.currency}{detail.totalAmount.toLocaleString()}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 