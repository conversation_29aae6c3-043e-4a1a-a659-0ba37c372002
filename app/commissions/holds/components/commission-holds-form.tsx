"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { Input } from "@/app/components/ui/input"
import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { useState } from "react"
import { 
  ChevronDown, 
  ChevronUp, 
  Download, 
  Edit2, 
  Eye, 
  Flag, 
  Plus, 
  Unlock 
} from "lucide-react"
import { Badge } from "@/app/components/ui/badge"
import { useToast } from "@/app/components/ui/use-toast"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/app/components/ui/dialog"
import { cn } from "@/app/lib/utils"

interface AgentHold {
  pin: string
  firstName: string
  lastName: string
  email: string
  city: string
  state: string
  status: string
  isFraud: boolean
  holdAmount: number
}

const dummyData: AgentHold[] = [
  {
    pin: "AG123456",
    firstName: "<PERSON>",
    lastName: "Do<PERSON>",
    email: "<EMAIL>",
    city: "Miami",
    state: "FL",
    status: "Active",
    isFraud: true,
    holdAmount: 5000
  },
  // Add more dummy data...
]

export function CommissionHoldsForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [agents] = useState<AgentHold[]>(dummyData)
  const [search, setSearch] = useState("")
  const { toast } = useToast()

  const totalHolds = agents.length
  const totalHoldAmount = agents.reduce((sum, agent) => sum + agent.holdAmount, 0)
  const fraudCases = agents.filter(agent => agent.isFraud).length

  const handleExportCsv = () => {
    console.log("Exporting CSV...")
    toast({
      title: "Export Started",
      description: "Your data export has begun.",
    })
  }

  const handleReleaseHold = (pin: string) => {
    console.log("Releasing hold for:", pin)
    toast({
      title: "Hold Released",
      description: `Hold has been released for PIN ${pin}`,
    })
  }

  const filteredAgents = agents.filter(agent => 
    agent.pin.toLowerCase().includes(search.toLowerCase()) ||
    agent.firstName.toLowerCase().includes(search.toLowerCase()) ||
    agent.lastName.toLowerCase().includes(search.toLowerCase()) ||
    agent.email.toLowerCase().includes(search.toLowerCase())
  )

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Commission Hold Summary</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportCsv}
              className="rounded-none w-24"
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div className="space-y-2">
                <h3 className="font-semibold">Total Holds</h3>
                <p className="text-2xl font-medium">{totalHolds}</p>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold">Total Hold Amount</h3>
                <p className="text-2xl font-medium">${totalHoldAmount.toLocaleString()}</p>
              </div>
              <div className="space-y-2">
                <h3 className="font-semibold">Fraud Cases</h3>
                <p className="text-2xl font-medium text-destructive">{fraudCases}</p>
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="flex justify-between items-center mb-4">
        <Input
          placeholder="Search agents..."
          value={search}
          onChange={(e) => setSearch(e.target.value)}
          className="max-w-sm"
        />
      </div>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">PIN</TableHead>
              <TableHead className="font-semibold">Name</TableHead>
              <TableHead className="font-semibold">Email</TableHead>
              <TableHead className="font-semibold">Location</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold text-right">Hold Amount</TableHead>
              <TableHead className="font-semibold text-center">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredAgents.map((agent) => (
              <TableRow 
                key={agent.pin}
                className={cn(
                  "hover:bg-slate-50/50",
                  agent.isFraud && "bg-red-50/50",
                  !agent.isFraud && agent.holdAmount > 1000 && "bg-yellow-50/50"
                )}
              >
                <TableCell>{agent.pin}</TableCell>
                <TableCell>
                  {agent.firstName} {agent.lastName}
                  {agent.isFraud && (
                    <Badge variant="destructive" className="ml-2">
                      Fraud
                    </Badge>
                  )}
                </TableCell>
                <TableCell>{agent.email}</TableCell>
                <TableCell>{agent.city}, {agent.state}</TableCell>
                <TableCell>{agent.status}</TableCell>
                <TableCell className="text-right font-medium">
                  ${agent.holdAmount.toLocaleString()}
                </TableCell>
                <TableCell>
                  <div className="flex justify-center space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="View details"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="Edit hold amount"
                    >
                      <Edit2 className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 w-8 p-0"
                      aria-label="Release hold"
                      onClick={() => handleReleaseHold(agent.pin)}
                    >
                      <Unlock className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className={cn(
                        "h-8 w-8 p-0",
                        agent.isFraud && "text-destructive"
                      )}
                      aria-label={agent.isFraud ? "Remove fraud flag" : "Flag as fraud"}
                    >
                      <Flag className="h-4 w-4" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 