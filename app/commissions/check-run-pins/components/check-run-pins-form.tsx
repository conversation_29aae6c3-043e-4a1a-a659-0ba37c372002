"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { ChevronDown, ChevronUp, Plus, Trash2 } from "lucide-react"
import { Alert, AlertDescription } from "@/app/components/ui/alert"

interface CheckRunPin {
  pinCheckRunId: number
  pin: string
  userId: number
  userName: string
  createdAt: string
}

interface CheckRunPinFormData {
  pin: string
}

// Dummy data for example
const dummyData: CheckRunPin[] = [
  {
    pinCheckRunId: 1,
    pin: "AG123456",
    userId: 101,
    userName: "John Admin",
    createdAt: "2024-03-15 14:30:00"
  }
]

export function CheckRunPinsForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [results, setResults] = useState<CheckRunPin[]>(dummyData)
  const [error, setError] = useState<string>("")
  const [agentInfo, setAgentInfo] = useState<{ firstName: string; lastName: string } | null>(null)

  const form = useForm<CheckRunPinFormData>({
    defaultValues: {
      pin: "",
    },
  })

  const onSubmit = async (data: CheckRunPinFormData) => {
    // TODO: Implement API call
    console.log("Form submitted:", data)
  }

  const handleDelete = async (id: number) => {
    if (confirm("Are you sure you want to delete?")) {
      // TODO: Implement delete API call
      console.log("Deleting PIN:", id)
    }
  }

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Add PIN to Check Run</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <form onSubmit={form.handleSubmit(onSubmit)}>
              <div className="space-y-6">
                <div className="flex items-center gap-4">
                  <Label>PIN:</Label>
                  <div className="flex gap-4">
                    <Input
                      {...form.register("pin")}
                      className="w-48 rounded-none"
                      placeholder="Enter PIN"
                    />
                    <Button type="submit" className="rounded-none">
                      Add
                    </Button>
                    <Button 
                      type="button" 
                      variant="outline" 
                      onClick={() => form.reset()}
                      className="rounded-none"
                    >
                      Clear
                    </Button>
                  </div>
                </div>

                {error && (
                  <Alert variant="destructive" className="rounded-none">
                    <AlertDescription>{error}</AlertDescription>
                  </Alert>
                )}

                {agentInfo && (
                  <div className="space-y-2">
                    <div><strong>Agent First Name:</strong> {agentInfo.firstName}</div>
                    <div><strong>Agent Last Name:</strong> {agentInfo.lastName}</div>
                  </div>
                )}
              </div>
            </form>
          </CardContent>
        )}
      </Card>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">Agent PIN</TableHead>
              <TableHead className="font-semibold">User ID</TableHead>
              <TableHead className="font-semibold">User Name</TableHead>
              <TableHead className="font-semibold">Created At</TableHead>
              <TableHead className="font-semibold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result) => (
              <TableRow key={result.pinCheckRunId}>
                <TableCell>{result.pin}</TableCell>
                <TableCell>{result.userId}</TableCell>
                <TableCell>{result.userName}</TableCell>
                <TableCell>{result.createdAt}</TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleDelete(result.pinCheckRunId)}
                    className="h-8 w-8 p-0 text-destructive"
                  >
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 