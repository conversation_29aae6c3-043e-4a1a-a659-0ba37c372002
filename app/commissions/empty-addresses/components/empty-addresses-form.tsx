"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { ChevronDown, ChevronUp, Download, Plus } from "lucide-react"

interface AgentAddress {
  pin: string
  address: string
  city: string
  state: string
  zip: string
  email: string
  type: 'commission' | 'override'
}

// Add dummy data
const dummyData: AgentAddress[] = [
  {
    pin: "AG123456",
    address: "",
    city: "Miami",
    state: "",
    zip: "33101",
    email: "<EMAIL>",
    type: 'commission'
  },
  {
    pin: "AG234567",
    address: "123 Main St",
    city: "",
    state: "FL",
    zip: "",
    email: "<EMAIL>",
    type: 'override'
  }
]

export function EmptyAddressesForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const [results] = useState<AgentAddress[]>(dummyData)

  const commissionResults = results.filter(r => r.type === 'commission')
  const overrideResults = results.filter(r => r.type === 'override')

  const handleExportCsv = () => {
    console.log("Exporting CSV...")
  }

  return (
    <div className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Empty Check Addresses Report</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={handleExportCsv}
              className="rounded-none w-24"
            >
              <Download className="mr-2 h-4 w-4" />
              Export
            </Button>
          </div>
        </CardHeader>
      </Card>

      {!isCollapsed && (
        <div className="space-y-8">
          {/* Commission Addresses Table */}
          <div>
            <h3 className="text-md font-semibold mb-4">Commission Payment Addresses</h3>
            <div className="border rounded-none">
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-50">
                    <TableHead className="font-semibold">PIN</TableHead>
                    <TableHead className="font-semibold">Address</TableHead>
                    <TableHead className="font-semibold">City</TableHead>
                    <TableHead className="font-semibold">State</TableHead>
                    <TableHead className="font-semibold">ZIP</TableHead>
                    <TableHead className="font-semibold">Email</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {commissionResults.length > 0 ? (
                    commissionResults.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.pin}</TableCell>
                        <TableCell>{result.address}</TableCell>
                        <TableCell>{result.city}</TableCell>
                        <TableCell>{result.state}</TableCell>
                        <TableCell>{result.zip}</TableCell>
                        <TableCell>{result.email}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        All Commissions have addresses!
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>

          {/* Override Addresses Table */}
          <div>
            <h3 className="text-md font-semibold mb-4">Override Payment Addresses</h3>
            <div className="border rounded-none">
              <Table>
                <TableHeader>
                  <TableRow className="bg-slate-50">
                    <TableHead className="font-semibold">PIN</TableHead>
                    <TableHead className="font-semibold">Address</TableHead>
                    <TableHead className="font-semibold">City</TableHead>
                    <TableHead className="font-semibold">State</TableHead>
                    <TableHead className="font-semibold">ZIP</TableHead>
                    <TableHead className="font-semibold">Email</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {overrideResults.length > 0 ? (
                    overrideResults.map((result, index) => (
                      <TableRow key={index}>
                        <TableCell>{result.pin}</TableCell>
                        <TableCell>{result.address}</TableCell>
                        <TableCell>{result.city}</TableCell>
                        <TableCell>{result.state}</TableCell>
                        <TableCell>{result.zip}</TableCell>
                        <TableCell>{result.email}</TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="text-center py-4 text-muted-foreground">
                        All Overrides have addresses!
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      )}
    </div>
  )
} 