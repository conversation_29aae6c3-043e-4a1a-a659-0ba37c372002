import { Environment, Network, RecordSource, Store } from 'relay-runtime';

async function fetchGraphQL(params: any) {
  console.log('Relay fetchGraphQL called with params:', {
    text: params.text,
    variables: params.variables,
    name: params.name,
  });

  const requestBody = {
    query: params.text,
    operationName: 'BookingSearchContentQuery'
  };

  const apiUrl = process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000/graphql';
  
  console.log('Sending GraphQL Request:', {
    url: apiUrl,
    body: JSON.stringify(requestBody, null, 2),
  });

  try {
    const response = await fetch(apiUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(requestBody),
      cache: 'no-store',
    });

    const json = await response.json();
    console.log('GraphQL Response:', json);

    if (json.errors) {
      const errorMessage = json.errors.map((e: any) => e.message).join('\n');
      const error = new Error(errorMessage);
      error.cause = { response: json, request: requestBody };
      throw error;
    }

    return json;
  } catch (error) {
    console.error('Network or parsing error:', {
      error,
      params,
    });
    throw error;
  }
}

const environment = new Environment({
  network: Network.create(fetchGraphQL),
  store: new Store(new RecordSource()),
});

console.log('Relay environment created:', environment);

export default environment;
