"use client"

import { useState } from "react"
import { SearchWrapper } from "@/app/components/booking-search/search-wrapper"
import { Input } from "@/app/components/ui/input"
import { But<PERSON> } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"
import { useSearchParams, useRouter } from "next/navigation"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
interface FreeUserBookingsPageProps {
  searchParams: { [key: string]: string }
}

export default function FreeUserBookingsPage({ searchParams: initialSearchParams }: FreeUserBookingsPageProps) {
  const [pin, setPin] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!pin.trim()) return

    // Create new URLSearchParams and set the pin
    const params = new URLSearchParams(searchParams.toString())
    params.set("vcFreeUserID", JSON.stringify({
      operator: "equals",
      value: pin
    }))
    params.set("page", "1")
    params.set("pageSize", "10")
    
    // Navigate with the new search params
    router.push(`/bookings/free-user?${params.toString()}`)
  }

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb />
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Free User Bookings</h2>
          <p className="text-muted-foreground">
            Enter a free user PIN to view their bookings
          </p>
        </div>
      </div>

      <Card className="p-6">
        <form onSubmit={handleSubmit} className="flex gap-4 items-end max-w-md">
          <div className="flex-1">
            <label htmlFor="pin" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 block mb-2">
              Free User PIN
            </label>
            <Input
              id="pin"
              type="text"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              placeholder="Enter PIN"
              className="w-full"
            />
          </div>
          <Button type="submit">Search</Button>
        </form>
      </Card>

      {searchParams.get("vcFreeUserID") && (
        <div className="space-y-4">
          <SearchWrapper 
            searchParams={{
              ...initialSearchParams,
              vcFreeUserID: searchParams.get("vcFreeUserID")!
            }} 
          />
        </div>
      )}
    </div>
  )
} 