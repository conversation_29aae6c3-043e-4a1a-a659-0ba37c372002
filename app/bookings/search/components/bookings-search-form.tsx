"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { Search, RotateCcw, ChevronDown, ChevronUp, Download } from "lucide-react"

interface BookingResult {
  confirmationId: string
  pin: string
  supplier: string
  totalCharges: number
  totalCommission: number
  commissionableAmount: number
  inTheBank: boolean
  agentPaid: boolean
  travelStartDate: string
  travelEndDate: string
}

interface BookingsSearchFormData {
  bookingId: string
  pin: string
  inTheBank: string
}

// Add dummy data
const dummyResults: BookingResult[] = [
  {
    confirmationId: "CONF123",
    pin: "PIN456",
    supplier: "Supplier A",
    totalCharges: 1500.00,
    totalCommission: 150.00,
    commissionableAmount: 1350.00,
    inTheBank: true,
    agentPaid: false,
    travelStartDate: "2024-04-01",
    travelEndDate: "2024-04-07"
  },
  // Add more dummy data as needed
]

export function BookingsSearchForm() {
  const [results, setResults] = useState<BookingResult[]>(dummyResults)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const form = useForm<BookingsSearchFormData>({
    defaultValues: {
      bookingId: "",
      pin: "",
      inTheBank: "",
    },
  })

  const onSubmit = async (data: BookingsSearchFormData) => {
    // TODO: Implement API call
    console.log("Form submitted:", data)
  }

  const handleExportCsv = () => {
    // TODO: Implement CSV export
    console.log("Exporting CSV...")
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-8">
      <Card className="rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <h2 className="text-lg font-semibold">Search Claim Booking</h2>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => form.reset()}
              className="rounded-none w-24"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button
              type="submit"
              size="sm"
              className="rounded-none w-24"
            >
              <Search className="mr-2 h-4 w-4" />
              Search
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="grid grid-cols-3 gap-6">
              <div className="flex items-center gap-4">
                <Label className="w-32">Booking ID:</Label>
                <Input
                  {...form.register("bookingId")}
                  className="w-48 rounded-none"
                  placeholder="Enter booking ID"
                />
              </div>
              <div className="flex items-center gap-4">
                <Label className="w-32">PIN:</Label>
                <Input
                  {...form.register("pin")}
                  className="w-48 rounded-none"
                  placeholder="Enter PIN"
                />
              </div>
              <div className="flex items-center gap-4">
                <Label className="w-32">In The Bank:</Label>
                <Input
                  {...form.register("inTheBank")}
                  className="w-48 rounded-none"
                  placeholder="Enter value"
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="flex justify-end mb-4">
        <Button
          type="button"
          variant="outline"
          size="sm"
          onClick={handleExportCsv}
          className="rounded-none"
        >
          <Download className="mr-2 h-4 w-4" />
          Export as CSV
        </Button>
      </div>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">Confirmation ID</TableHead>
              <TableHead className="font-semibold">PIN</TableHead>
              <TableHead className="font-semibold">Supplier</TableHead>
              <TableHead className="font-semibold text-right">Total Charges</TableHead>
              <TableHead className="font-semibold text-right">Commission</TableHead>
              <TableHead className="font-semibold text-right">Commissionable Amount</TableHead>
              <TableHead className="font-semibold">In the Bank</TableHead>
              <TableHead className="font-semibold">Agent Paid</TableHead>
              <TableHead className="font-semibold">Travel Start</TableHead>
              <TableHead className="font-semibold">Travel End</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {results.map((result, index) => (
              <TableRow key={index}>
                <TableCell>{result.confirmationId}</TableCell>
                <TableCell>{result.pin}</TableCell>
                <TableCell>{result.supplier}</TableCell>
                <TableCell className="text-right">${result.totalCharges.toFixed(2)}</TableCell>
                <TableCell className="text-right">${result.totalCommission.toFixed(2)}</TableCell>
                <TableCell className="text-right">${result.commissionableAmount.toFixed(2)}</TableCell>
                <TableCell>{result.inTheBank ? "Yes" : "No"}</TableCell>
                <TableCell>{result.agentPaid ? "Yes" : "No"}</TableCell>
                <TableCell>{result.travelStartDate}</TableCell>
                <TableCell>{result.travelEndDate}</TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </form>
  )
} 