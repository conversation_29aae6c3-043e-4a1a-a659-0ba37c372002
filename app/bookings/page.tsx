import { ClientSearch } from "@/app/components/client-search"
import { SearchWrapper } from "@/app/components/booking-search/search-wrapper"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"

interface BookingsPageProps {
  searchParams: { [key: string]: string }
}

export default function BookingsPage({ searchParams }: BookingsPageProps) {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb data-testid="breadcrumb" />
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Booking Search</h2>
          <p className="text-muted-foreground">
            Search and manage your bookings here
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <ClientSearch />
        <SearchWrapper searchParams={searchParams} />
      </div>
    </div>
  )
} 