"use client"

import { useState } from "react"
import { useSearchParams, useRouter } from "next/navigation"
import { SearchWrapper } from "@/app/components/booking-search/search-wrapper"
import { Input } from "@/app/components/ui/input"
import { Button } from "@/app/components/ui/button"
import { Card } from "@/app/components/ui/card"

interface AgentBookingsClientProps {
  initialSearchParams: { [key: string]: string }
}

export function AgentBookingsClient({ initialSearchParams }: AgentBookingsClientProps) {
  const [pin, setPin] = useState("")
  const router = useRouter()
  const searchParams = useSearchParams()

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    if (!pin.trim()) return

    const params = new URLSearchParams(searchParams.toString())
    params.set("vcPIN", JSON.stringify({
      operator: "equals",
      value: pin
    }))
    params.set("page", "1")
    params.set("pageSize", "10")
    
    router.push(`/bookings/agent?${params.toString()}`)
  }

  return (
    <>
      <Card className="p-6">
        <form onSubmit={handleSubmit} className="flex gap-4 items-end max-w-md">
          <div className="flex-1">
            <label htmlFor="pin" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 block mb-2">
              Agent PIN
            </label>
            <Input
              id="pin"
              type="text"
              value={pin}
              onChange={(e) => setPin(e.target.value)}
              placeholder="Enter Agent PIN"
              className="w-full"
            />
          </div>
          <Button type="submit">Search</Button>
        </form>
      </Card>

      {searchParams.get("vcPIN") && (
        <div className="space-y-4">
          <SearchWrapper 
            searchParams={{
              ...initialSearchParams,
              vcPIN: searchParams.get("vcPIN")!
            }} 
          />
        </div>
      )}
    </>
  )
} 