import { Suspense } from "react"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { AgentBookingsClient } from "./agent-bookings-client"

export default function AgentBookingsPage({
  searchParams,
}: {
  searchParams: { [key: string]: string }
}) {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb />
      <div className="flex items-center justify-between space-y-2">
        <h2 className="text-2xl font-bold tracking-tight">Agent Bookings</h2>
        <p className="text-muted-foreground">
          Enter an agent PIN to view their bookings
        </p>
      </div>

      <Suspense fallback={<div>Loading...</div>}>
        <AgentBookingsClient initialSearchParams={searchParams} />
      </Suspense>
    </div>
  )
} 