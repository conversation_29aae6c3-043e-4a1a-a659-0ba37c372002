import { EcsUploadForm } from "@/app/components/ecs/ecs-upload-form"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
export default function EcsUploadPage() {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb />
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">ECS Check Upload</h2>
          <p className="text-muted-foreground">
            Upload and manage ECS check scans
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <EcsUploadForm />
      </div>
    </div>
  )
} 