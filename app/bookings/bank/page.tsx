"use client"

import { BankBookingsTable } from "@/app/components/bank-bookings/bank-bookings-table"
import { CheckInfoForm } from "@/app/components/bank-bookings/check-info-form"
import { BookingTypeNav } from "@/app/components/bank-bookings/booking-type-nav"
import { Card, CardHeader, CardTitle, CardContent } from "@/app/components/ui/card"
import { Button } from "@/app/components/ui/button"
import { ChevronDown, RotateCcw, Search } from "lucide-react"
import { cn } from "@/lib/utils"
import { useState } from "react"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
interface BankBookingsPageProps {
  searchParams: { [key: string]: string }
}

export default function BankBookingsPage({ searchParams }: BankBookingsPageProps) {
  const [isCollapsed, setIsCollapsed] = useState(false)

  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb />
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Bank Bookings</h2>
          <p className="text-muted-foreground">
            Manage and track bookings in the bank system
          </p>
        </div>
      </div>

      <BookingTypeNav />

      <div className="space-y-4">
        <Card className="rounded-none shadow-none border">
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
            <CardTitle>Check Information</CardTitle>
            <div className="flex items-center gap-2">
              <Button 
                variant="ghost" 
                className="rounded-none flex gap-2"
                onClick={() => setIsCollapsed(!isCollapsed)}
              >
                <ChevronDown className={cn(
                  "h-4 w-4 transition-transform",
                  isCollapsed && "rotate-180"
                )} />
                {isCollapsed ? "Show" : "Hide"}
              </Button>
              <Button variant="ghost" size="icon" className="rounded-none">
                <RotateCcw className="h-4 w-4" />
              </Button>
              <Button 
                className={cn(
                  "rounded-none flex gap-2",
                  "bg-[#3C36A9] hover:bg-[#3C36A9]/90 text-white"
                )}
              >
                <Search className="h-4 w-4" />
                Search
              </Button>
            </div>
          </CardHeader>
          {!isCollapsed && (
            <CardContent className="pt-4">
              <CheckInfoForm />
            </CardContent>
          )}
        </Card>
        
        <BankBookingsTable searchParams={searchParams} />
      </div>
    </div>
  )
} 