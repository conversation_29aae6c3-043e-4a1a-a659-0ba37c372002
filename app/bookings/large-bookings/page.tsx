import { Breadcrumb } from "@/app/components/ui/breadcrumb";
import { LargeBookingsContainer } from "./components/large-bookings-container";
import { Suspense } from "react";
import { LargeBookingsSkeleton } from "./components/large-bookings-skeleton";
import { Toaster } from "@/app/components/ui/toaster";

export default function LargeBookingsPage() {
  return (
    <div className="w-full p-6">
      <Breadcrumb />
      <div className="flex items-center mt-4 justify-between pb-6">
        <h1 className="text-2xl font-semibold">
          Large Bookings
        </h1>
      </div>
      <Suspense fallback={<LargeBookingsSkeleton />}>
        <LargeBookingsContainer />
      </Suspense>
      <Toaster />
    </div>
  );
} 