"use client";

import { useState } from "react";
import { <PERSON><PERSON> } from "@/app/components/ui/button";
import { 
  DropdownMenu, 
  DropdownMenuContent, 
  DropdownMenuItem, 
  DropdownMenuLabel, 
  DropdownMenuSeparator, 
  DropdownMenuTrigger 
} from "@/app/components/ui/dropdown-menu";
import { Mail, Send, Check, Loader2 } from "lucide-react";
import { useToast } from "@/app/components/ui/use-toast";
import { LargeBooking, LargeBookingFilterInput } from "@/app/lib/types/bookings";

interface SendReportDropdownProps {
  bookings: LargeBooking[];
  filters: LargeBookingFilterInput;
}

const STAFF_EMAILS = [
  { email: "<EMAIL>", name: "S. Petrillo" },
  { email: "<EMAIL>", name: "D. Jordan" },
  { email: "<EMAIL>", name: "<PERSON><PERSON> <PERSON>" },
  { email: "cpel<PERSON><PERSON>@inteletravel.com", name: "<PERSON><PERSON>" },
  { email: "<EMAIL>", name: "<PERSON><PERSON>" },
  { email: "<EMAIL>", name: "H. Narcisse" },
  { email: "<EMAIL>", name: "M. Martinez" },
];

export function SendReportDropdown({ bookings, filters }: SendReportDropdownProps) {
  const { toast } = useToast();
  const [isSending, setIsSending] = useState(false);
  const [lastSentTo, setLastSentTo] = useState<string | null>(null);

  const handleSendReport = (email: string, name: string) => {
    if (bookings.length === 0) {
      toast({
        title: "No data to send",
        description: "There are no bookings matching your current filters.",
        variant: "destructive",
      });
      return;
    }

    // Just show a toast for UI demonstration
    setLastSentTo(email);
    
    toast({
      title: "Report would be sent",
      description: `In a real implementation, the report would be sent to ${name} (${email}).`,
    });
  };

  const handleSendToAll = () => {
    if (bookings.length === 0) {
      toast({
        title: "No data to send",
        description: "There are no bookings matching your current filters.",
        variant: "destructive",
      });
      return;
    }
    
    toast({
      title: "Report would be sent to all",
      description: "In a real implementation, the report would be sent to all staff members.",
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" disabled={isSending}>
          {isSending ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Sending...
            </>
          ) : (
            <>
              <Mail className="mr-2 h-4 w-4" />
              Send Report
            </>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>Send Report To</DropdownMenuLabel>
        <DropdownMenuSeparator />
        {STAFF_EMAILS.map((staff) => (
          <DropdownMenuItem
            key={staff.email}
            onClick={() => handleSendReport(staff.email, staff.name)}
            disabled={isSending}
            className="flex items-center justify-between"
          >
            <span>{staff.name}</span>
            {lastSentTo === staff.email && <Check className="h-4 w-4 text-green-500" />}
          </DropdownMenuItem>
        ))}
        <DropdownMenuSeparator />
        <DropdownMenuItem 
          onClick={handleSendToAll}
          disabled={isSending}
          className="flex items-center"
        >
          <Send className="mr-2 h-4 w-4" />
          <span>Send to All Staff</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
} 