"use client";

import { useCallback, useEffect, useState } from "react";
import { LargeBookingsTable } from "./large-bookings-table";
import { LargeBookingsFilters } from "./large-bookings-filters";
import { SendReportDropdown } from "./send-report-dropdown";
import { graphqlClient } from "@/app/lib/graphql/client";
import { LARGE_BOOKINGS_QUERY } from "@/app/lib/graphql/queries";
import { 
  LargeBooking, 
  LargeBookingFilterInput, 
  LargeBookingsResponse
} from "@/app/lib/types/bookings";
import { useSearchParams, useRouter, usePathname } from "next/navigation";
import { createQueryString } from "@/app/lib/utils";

export function LargeBookingsContainer() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const pathname = usePathname();
  
  const [bookings, setBookings] = useState<LargeBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Get filters from URL params
  const vendorName = searchParams.get("vendorName") || "";
  const minTotalCharges = searchParams.get("minTotalCharges") ? 
    parseInt(searchParams.get("minTotalCharges") as string) : undefined;
  const startDate = searchParams.get("startDate") || "";
  const endDate = searchParams.get("endDate") || "";

  // Current active filters
  const currentFilters: LargeBookingFilterInput = {};
  if (vendorName) currentFilters.vendorName = vendorName;
  if (minTotalCharges) currentFilters.minTotalCharges = minTotalCharges;
  if (startDate) currentFilters.startDate = startDate;
  if (endDate) currentFilters.endDate = endDate;

  const fetchBookings = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    
    try {
      const filters: LargeBookingFilterInput = {};
      if (vendorName) filters.vendorName = vendorName;
      if (minTotalCharges) filters.minTotalCharges = minTotalCharges;
      if (startDate) filters.startDate = startDate;
      if (endDate) filters.endDate = endDate;
      
      const data = await graphqlClient.request<LargeBookingsResponse>(
        LARGE_BOOKINGS_QUERY,
        { filters }
      );
      
      setBookings(data.largeBookings);
    } catch (err) {
      console.error("Error fetching large bookings:", err);
      setError("Failed to load bookings. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }, [vendorName, minTotalCharges, startDate, endDate]);

  useEffect(() => {
    fetchBookings();
  }, [fetchBookings]);

  const handleFilterChange = (filters: LargeBookingFilterInput) => {
    // Update URL with new filters
    const newParams = createQueryString(
      searchParams,
      {
        vendorName: filters.vendorName || null,
        minTotalCharges: filters.minTotalCharges?.toString() || null,
        startDate: filters.startDate || null,
        endDate: filters.endDate || null
      }
    );
    
    router.push(`${pathname}?${newParams}`);
  };

  return (
    <div className="space-y-6">
      <LargeBookingsFilters 
        initialFilters={{
          vendorName,
          minTotalCharges,
          startDate,
          endDate
        }}
        onFilterChange={handleFilterChange}
      />
      
      <div className="flex justify-between items-center">
        <h2 className="text-lg font-medium">
          {isLoading ? "Loading bookings..." : `${bookings.length} bookings found`}
        </h2>
        <SendReportDropdown bookings={bookings} filters={currentFilters} />
      </div>
      
      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded">
          {error}
        </div>
      )}
      
      <LargeBookingsTable 
        bookings={bookings} 
        isLoading={isLoading} 
      />
    </div>
  );
} 