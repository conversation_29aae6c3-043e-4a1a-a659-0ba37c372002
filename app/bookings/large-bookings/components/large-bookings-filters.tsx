"use client";

import { useState, useEffect } from "react";
import { Button } from "@/app/components/ui/button";
import { Input } from "@/app/components/ui/input";
import { Label } from "@/app/components/ui/label";
import { Calendar } from "@/app/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/app/components/ui/popover";
import { CalendarIcon, ChevronDown, ChevronUp } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/app/lib/utils";
import { LargeBookingFilterInput } from "@/app/lib/types/bookings";
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card";

interface LargeBookingsFiltersProps {
  initialFilters: {
    vendorName: string;
    minTotalCharges?: number;
    startDate: string;
    endDate: string;
  };
  onFilterChange: (filters: LargeBookingFilterInput) => void;
}

export function LargeBookingsFilters({ initialFilters, onFilterChange }: LargeBookingsFiltersProps) {
  const [vendorName, setVendorName] = useState(initialFilters.vendorName || "");
  const [minTotalCharges, setMinTotalCharges] = useState(initialFilters.minTotalCharges?.toString() || "");
  const [startDate, setStartDate] = useState<Date | undefined>(
    initialFilters.startDate ? new Date(initialFilters.startDate) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    initialFilters.endDate ? new Date(initialFilters.endDate) : undefined
  );
  const [isExpanded, setIsExpanded] = useState(true);

  // Update local state when initialFilters change (e.g., from URL)
  useEffect(() => {
    setVendorName(initialFilters.vendorName || "");
    setMinTotalCharges(initialFilters.minTotalCharges?.toString() || "");
    setStartDate(initialFilters.startDate ? new Date(initialFilters.startDate) : undefined);
    setEndDate(initialFilters.endDate ? new Date(initialFilters.endDate) : undefined);
  }, [initialFilters]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    const filters: LargeBookingFilterInput = {};
    if (vendorName) filters.vendorName = vendorName;
    if (minTotalCharges) filters.minTotalCharges = parseInt(minTotalCharges);
    if (startDate) filters.startDate = format(startDate, "yyyy-MM-dd");
    if (endDate) filters.endDate = format(endDate, "yyyy-MM-dd");
    
    onFilterChange(filters);
  };

  const handleReset = () => {
    setVendorName("");
    setMinTotalCharges("");
    setStartDate(undefined);
    setEndDate(undefined);
    
    onFilterChange({});
  };

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <Card className="rounded-none">
      <CardHeader className="px-6 py-4 border-b">
        <div className="flex items-center justify-between">
          <CardTitle className="text-lg font-medium">
            Filter Bookings
          </CardTitle>
          <div className="flex space-x-2">
            <Button type="button" variant="outline" size="sm" onClick={handleReset}>
              Reset
            </Button>
            <Button type="submit" size="sm" form="large-bookings-filter-form">
              Apply Filters
            </Button>
            <Button 
              variant="ghost" 
              size="icon" 
              className="ml-2 h-8 w-8" 
              onClick={toggleExpanded}
            >
              {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
            </Button>
          </div>
        </div>
      </CardHeader>
      
      {isExpanded && (
        <CardContent className="px-6 py-6">
          <form id="large-bookings-filter-form" onSubmit={handleSubmit}>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
              <div className="space-y-2">
                <Label htmlFor="vendorName">Vendor Name</Label>
                <Input
                  id="vendorName"
                  placeholder="e.g. Marriott"
                  value={vendorName}
                  onChange={(e) => setVendorName(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="minTotalCharges">Min Total Charges</Label>
                <Input
                  id="minTotalCharges"
                  type="number"
                  placeholder="e.g. 15000"
                  value={minTotalCharges}
                  onChange={(e) => setMinTotalCharges(e.target.value)}
                />
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="startDate">Start Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="startDate"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !startDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {startDate ? format(startDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={startDate}
                      onSelect={setStartDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
              
              <div className="space-y-2">
                <Label htmlFor="endDate">End Date</Label>
                <Popover>
                  <PopoverTrigger asChild>
                    <Button
                      id="endDate"
                      variant="outline"
                      className={cn(
                        "w-full justify-start text-left font-normal",
                        !endDate && "text-muted-foreground"
                      )}
                    >
                      <CalendarIcon className="mr-2 h-4 w-4" />
                      {endDate ? format(endDate, "PPP") : "Select date"}
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0">
                    <Calendar
                      mode="single"
                      selected={endDate}
                      onSelect={setEndDate}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>
          </form>
        </CardContent>
      )}
    </Card>
  );
} 