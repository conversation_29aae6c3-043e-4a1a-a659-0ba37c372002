"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, CardHeader } from "@/app/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { ChevronDown, ChevronUp, Download, RotateCcw } from "lucide-react"

interface LargeBooking {
  pin: string
  bookingType: string
  confirmationId: string
  clientName: string
  vendor: string
  booked: string
  travelStartDate: string
  travelEndDate: string
  totalCharges: number
  totalCommission: number
}

export function LargeBookingsForm() {
  const [isCollapsed, setIsCollapsed] = useState(false)
  const form = useForm()

  const onSubmit = (data: any) => {
    // Handle form submission - generate and send report
    form.reset()
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
      <Card className="w-full bg-white rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Download className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Generate Large Bookings Report</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => form.reset()}
              className="rounded-none w-24"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button
              type="submit"
              size="sm"
              className="rounded-none w-24"
            >
              <Download className="mr-2 h-4 w-4" />
              Generate
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <p className="text-sm text-gray-500">
              This report includes active bookings created since Jan. 1, 2020 that have not been paid and over $10,000 in total charges for US and UK.
            </p>
          </CardContent>
        )}
      </Card>

      <div className="w-full">
        <div className="border rounded-none">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50">
                <TableHead className="font-semibold">PIN</TableHead>
                <TableHead className="font-semibold">Booking Type</TableHead>
                <TableHead className="font-semibold">Confirmation ID</TableHead>
                <TableHead className="font-semibold">Client Name</TableHead>
                <TableHead className="font-semibold">Vendor</TableHead>
                <TableHead className="font-semibold">Booked Date</TableHead>
                <TableHead className="font-semibold">Travel Start</TableHead>
                <TableHead className="font-semibold">Travel End</TableHead>
                <TableHead className="font-semibold text-right">Total Charges</TableHead>
                <TableHead className="font-semibold text-right">Commission</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {/* Table content will be populated with actual data */}
            </TableBody>
          </Table>
        </div>
      </div>
    </form>
  )
} 