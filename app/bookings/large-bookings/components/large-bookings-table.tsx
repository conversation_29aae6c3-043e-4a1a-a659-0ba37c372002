"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table";
import { LargeBooking } from "@/app/lib/types/bookings";
import { format } from "date-fns";

interface LargeBookingsTableProps {
  bookings: LargeBooking[];
  isLoading: boolean;
}

export function LargeBookingsTable({ bookings, isLoading }: LargeBookingsTableProps) {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2,
    }).format(amount); // Remove the division by 100
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), "MMM d, yyyy");
    } catch (e) {
      return dateString;
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
      </div>
    );
  }

  if (bookings.length === 0) {
    return (
      <div className="bg-white rounded-lg border shadow-sm p-8 text-center">
        <p className="text-muted-foreground">No bookings found. Try adjusting your filters.</p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border shadow-sm overflow-hidden">
      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Confirmation ID</TableHead>
              <TableHead>Client Name</TableHead>
              <TableHead>Vendor</TableHead>
              <TableHead>Booking Date</TableHead>
              <TableHead>Travel Dates</TableHead>
              <TableHead className="text-right">Total Charges</TableHead>
              <TableHead className="text-right">Commission</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {bookings.map((booking) => (
              <TableRow key={booking.vcConfirmationID}>
                <TableCell className="font-medium">{booking.vcConfirmationID}</TableCell>
                <TableCell>{booking.vcClientName}</TableCell>
                <TableCell>{booking.vcVendor}</TableCell>
                <TableCell>{formatDate(booking.booked)}</TableCell>
                <TableCell>
                  {formatDate(booking.travelStartDate)} - {formatDate(booking.travelEndDate)}
                </TableCell>
                <TableCell className="text-right">{formatCurrency(booking.nTotalCharges)}</TableCell>
                <TableCell className="text-right">
                  {typeof booking.nTotalCommission === 'number' 
                    ? booking.nTotalCommission > 100 
                      ? formatCurrency(booking.nTotalCommission) // If over 100, treat as a dollar amount
                      : `${booking.nTotalCommission}%` // If under 100, treat as a percentage
                    : booking.nTotalCommission}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 