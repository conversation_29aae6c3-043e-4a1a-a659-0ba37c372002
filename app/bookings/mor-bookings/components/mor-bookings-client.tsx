"use client"

import { useState, useEffect } from "react"
import { Loader2 } from "lucide-react"
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, CardHeader, CardTitle } from "@/components/ui/card"
import { MorBookingsTable } from "./mor-bookings-table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { Button } from "@/components/ui/button"

type Booking = {
  bookingId: number
  pin: string
  confirmationId: string
  clientName: string
  bookingType: string
  bookingStatus: string
  totalCharges: number
  commission: number
  vendor: string
  region: string
  bookedDate: string
  travelStartDate: string
  travelEndDate: string
  isInTheBank: boolean
}

type MORBookingsResponse = {
  totalBookings: number
  totalCharges: number
  totalCommission: number
  bookings: Booking[]
}

export function MorBookingsClient() {
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [data, setData] = useState<MORBookingsResponse | null>(null)

  const fetchMORBookings = async () => {
    setIsLoading(true)
    setError(null)

    try {
      const query = `
        query GetMORBookings {
          getMORBookings {
            totalBookings
            totalCharges
            totalCommission
            bookings {
              bookingId
              pin
              confirmationId
              clientName
              bookingType
              bookingStatus
              totalCharges
              commission
              vendor
              region
              bookedDate
              travelStartDate
              travelEndDate
              isInTheBank
            }
          }
        }
      `

      const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || "", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      })

      const result = await response.json()
      
      if (result.errors) {
        throw new Error(result.errors[0]?.message || "Error fetching MOR bookings")
      }

      if (result.data?.getMORBookings) {
        setData(result.data.getMORBookings)
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : "Failed to fetch MOR bookings")
      console.error("Error fetching MOR bookings:", err)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchMORBookings()
  }, [])

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h3 className="text-xl font-medium">MOR Bookings Report</h3>
          <p className="text-sm text-muted-foreground">
            View all MOR bookings and their details
          </p>
        </div>
        <Button 
          onClick={fetchMORBookings} 
          disabled={isLoading}
        >
          {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
          Refresh Data
        </Button>
      </div>

      {error && (
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {isLoading && !data && (
        <div className="flex justify-center items-center py-8">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
        </div>
      )}

      {data && (
        <>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Bookings
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">{data.totalBookings}</div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Charges
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {data.totalCharges.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD'
                  })}
                </div>
              </CardContent>
            </Card>
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium text-muted-foreground">
                  Total Commission
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {data.totalCommission.toLocaleString('en-US', {
                    style: 'currency',
                    currency: 'USD'
                  })}
                </div>
              </CardContent>
            </Card>
          </div>

          <MorBookingsTable bookings={data.bookings} />
        </>
      )}
    </div>
  )
} 