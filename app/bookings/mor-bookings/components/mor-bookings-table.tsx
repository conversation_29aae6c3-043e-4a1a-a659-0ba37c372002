"use client"

import { useState } from "react"
import { ArrowUpDown, Check, X } from "lucide-react"
import { format, parseISO, isValid } from "date-fns"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"

interface Booking {
  bookingId: number
  pin: string
  confirmationId: string
  clientName: string
  bookingType: string
  bookingStatus: string
  totalCharges: number
  commission: number
  vendor: string
  region: string
  bookedDate: string
  travelStartDate: string
  travelEndDate: string
  isInTheBank: boolean
}

interface MorBookingsTableProps {
  bookings: Booking[]
}

export function MorBookingsTable({ bookings }: MorBookingsTableProps) {
  const [sortColumn, setSortColumn] = useState<keyof Booking | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(0)
  const [clientNameFilter, setClientNameFilter] = useState("")
  const pageSize = 10

  const formatDate = (dateString: string) => {
    try {
      return format(parseISO(dateString), "MMM d, yyyy")
    } catch (e) {
      return dateString
    }
  }

  const toggleSort = (column: keyof Booking) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const filteredBookings = bookings.filter(booking => 
    booking.clientName.toLowerCase().includes(clientNameFilter.toLowerCase())
  )

  const sortedBookings = [...filteredBookings].sort((a, b) => {
    if (!sortColumn) return 0

    const aValue = a[sortColumn]
    const bValue = b[sortColumn]

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      // Check if this is a date field
      if (sortColumn === 'bookedDate' || sortColumn === 'travelStartDate' || sortColumn === 'travelEndDate') {
        try {
          const aDate = parseISO(aValue)
          const bDate = parseISO(bValue)
          
          if (isValid(aDate) && isValid(bDate)) {
            return sortDirection === 'asc' 
              ? aDate.getTime() - bDate.getTime() 
              : bDate.getTime() - aDate.getTime()
          }
        } catch (e) {
          // Fall back to string comparison if date parsing fails
        }
      }
      
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue)
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    }

    if (typeof aValue === 'boolean' && typeof bValue === 'boolean') {
      return sortDirection === 'asc' 
        ? (aValue ? 1 : 0) - (bValue ? 1 : 0)
        : (bValue ? 1 : 0) - (aValue ? 1 : 0)
    }

    return 0
  })

  const paginatedBookings = sortedBookings.slice(
    currentPage * pageSize,
    (currentPage + 1) * pageSize
  )

  const totalPages = Math.ceil(sortedBookings.length / pageSize)

  const SortButton = ({ column }: { column: keyof Booking }) => (
    <Button
      variant="ghost"
      onClick={() => toggleSort(column)}
      className="h-8 px-2"
    >
      <ArrowUpDown className="ml-2 h-4 w-4" />
    </Button>
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>MOR Bookings</CardTitle>
        <div className="flex items-center py-4">
          <Input
            placeholder="Filter by client name..."
            value={clientNameFilter}
            onChange={(event) => {
              setClientNameFilter(event.target.value)
              setCurrentPage(0) // Reset to first page when filtering
            }}
            className="max-w-sm"
          />
        </div>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Booking ID</TableHead>
                <TableHead>PIN</TableHead>
                <TableHead>Confirmation ID</TableHead>
                <TableHead>
                  Client Name
                  <SortButton column="clientName" />
                </TableHead>
                <TableHead>Booking Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>
                  Total Charges
                  <SortButton column="totalCharges" />
                </TableHead>
                <TableHead>
                  Commission
                  <SortButton column="commission" />
                </TableHead>
                <TableHead>Vendor</TableHead>
                <TableHead>Region</TableHead>
                <TableHead>
                  Booked Date
                  <SortButton column="bookedDate" />
                </TableHead>
                <TableHead>Travel Start</TableHead>
                <TableHead>Travel End</TableHead>
                <TableHead>In The Bank</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBookings.length > 0 ? (
                paginatedBookings.map((booking) => (
                  <TableRow key={booking.bookingId}>
                    <TableCell>{booking.bookingId}</TableCell>
                    <TableCell>{booking.pin}</TableCell>
                    <TableCell>{booking.confirmationId}</TableCell>
                    <TableCell>{booking.clientName}</TableCell>
                    <TableCell>
                      <Badge variant="outline">{booking.bookingType}</Badge>
                    </TableCell>
                    <TableCell>
                      <Badge>{booking.bookingStatus}</Badge>
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {booking.totalCharges.toLocaleString('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                      })}
                    </TableCell>
                    <TableCell className="text-right font-medium">
                      {booking.commission.toLocaleString('en-US', {
                        style: 'currency',
                        currency: 'USD',
                        minimumFractionDigits: 2,
                      })}
                    </TableCell>
                    <TableCell>{booking.vendor}</TableCell>
                    <TableCell>{booking.region}</TableCell>
                    <TableCell>{formatDate(booking.bookedDate)}</TableCell>
                    <TableCell>{formatDate(booking.travelStartDate)}</TableCell>
                    <TableCell>{formatDate(booking.travelEndDate)}</TableCell>
                    <TableCell>
                      {booking.isInTheBank ? 
                        <Check className="h-4 w-4 text-green-500 mx-auto" /> : 
                        <X className="h-4 w-4 text-red-500 mx-auto" />}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={14} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
            disabled={currentPage === 0}
          >
            Previous
          </Button>
          <div className="text-sm text-muted-foreground">
            Page {currentPage + 1} of {Math.max(1, totalPages)}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
            disabled={currentPage >= totalPages - 1}
          >
            Next
          </Button>
        </div>
      </CardContent>
    </Card>
  )
} 