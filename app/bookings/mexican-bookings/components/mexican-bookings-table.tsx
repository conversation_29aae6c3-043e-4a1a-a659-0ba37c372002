"use client"

import { useState } from "react"
import { ArrowUpDown } from "lucide-react"

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

interface Booking {
  orderId: string
  serviceType: string
  createdDate: string
  salesCurrency: string
  totalCharges: number
  supplier: string
  bookingType: string
  totalBookings: number
}

interface MexicanBookingsTableProps {
  bookings: Booking[]
}

export function MexicanBookingsTable({ bookings }: MexicanBookingsTableProps) {
  const [sortColumn, setSortColumn] = useState<keyof Booking | null>(null)
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc')
  const [currentPage, setCurrentPage] = useState(0)
  const pageSize = 10

  const toggleSort = (column: keyof Booking) => {
    if (sortColumn === column) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(column)
      setSortDirection('asc')
    }
  }

  const sortedBookings = [...bookings].sort((a, b) => {
    if (!sortColumn) return 0

    const aValue = a[sortColumn]
    const bValue = b[sortColumn]

    if (typeof aValue === 'string' && typeof bValue === 'string') {
      return sortDirection === 'asc' 
        ? aValue.localeCompare(bValue) 
        : bValue.localeCompare(aValue)
    }

    if (typeof aValue === 'number' && typeof bValue === 'number') {
      return sortDirection === 'asc' ? aValue - bValue : bValue - aValue
    }

    return 0
  })

  const paginatedBookings = sortedBookings.slice(
    currentPage * pageSize,
    (currentPage + 1) * pageSize
  )

  const totalPages = Math.ceil(bookings.length / pageSize)

  const SortButton = ({ column }: { column: keyof Booking }) => (
    <Button
      variant="ghost"
      onClick={() => toggleSort(column)}
      className="h-8 px-2"
    >
      <ArrowUpDown className="ml-2 h-4 w-4" />
    </Button>
  )

  return (
    <Card>
      <CardHeader>
        <CardTitle>Mexican Bookings</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Order ID</TableHead>
                <TableHead>
                  Service Type
                  <SortButton column="serviceType" />
                </TableHead>
                <TableHead>
                  Created Date
                  <SortButton column="createdDate" />
                </TableHead>
                <TableHead>Currency</TableHead>
                <TableHead>
                  Total Charges
                  <SortButton column="totalCharges" />
                </TableHead>
                <TableHead>
                  Supplier
                  <SortButton column="supplier" />
                </TableHead>
                <TableHead>Booking Type</TableHead>
                <TableHead>Total Bookings</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedBookings.length > 0 ? (
                paginatedBookings.map((booking) => (
                  <TableRow key={booking.orderId}>
                    <TableCell>{booking.orderId}</TableCell>
                    <TableCell>{booking.serviceType}</TableCell>
                    <TableCell>
                      {new Date(booking.createdDate).toLocaleString()}
                    </TableCell>
                    <TableCell>{booking.salesCurrency}</TableCell>
                    <TableCell className="text-right font-medium">
                      {booking.totalCharges.toLocaleString(undefined, {
                        style: 'currency',
                        currency: booking.salesCurrency || 'MXN',
                        minimumFractionDigits: 2,
                      })}
                    </TableCell>
                    <TableCell>{booking.supplier}</TableCell>
                    <TableCell>{booking.bookingType}</TableCell>
                    <TableCell>{booking.totalBookings}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={8} className="h-24 text-center">
                    No results.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-end space-x-2 py-4">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.max(0, prev - 1))}
            disabled={currentPage === 0}
          >
            Previous
          </Button>
          <div className="text-sm">
            Page {currentPage + 1} of {Math.max(1, totalPages)}
          </div>
          <Button
            variant="outline"
            size="sm"
            onClick={() => setCurrentPage(prev => Math.min(totalPages - 1, prev + 1))}
            disabled={currentPage >= totalPages - 1}
          >
            Next
          </Button>
        </div>
      </CardContent>
    </Card>
  )
} 