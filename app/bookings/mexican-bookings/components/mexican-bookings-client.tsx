"use client"

import { useState } from "react"
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { format } from "date-fns"
import { CalendarIcon, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"

import { But<PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Calendar } from "@/components/ui/calendar"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { Card } from "@/components/ui/card"
import { MexicanBookingsTable } from "./mexican-bookings-table"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { CheckCircle, AlertCircle } from "lucide-react"

const formSchema = z.object({
  reportType: z.enum(["WEEKLY", "MONTHLY"]),
  sendReportTo: z.string().email({ message: "Please enter a valid email address" }),
  endTravelDate: z.date(),
  endVendorDate: z.date(),
})

type FormValues = z.infer<typeof formSchema>

type Booking = {
  orderId: string
  serviceType: string
  createdDate: string
  salesCurrency: string
  totalCharges: number
  supplier: string
  bookingType: string
  totalBookings: number
}

type ReportStatus = {
  sent: boolean
  message: string
  recipient: string
}

export function MexicanBookingsClient() {
  const [isLoading, setIsLoading] = useState(false)
  const [bookings, setBookings] = useState<Booking[]>([])
  const [reportStatus, setReportStatus] = useState<ReportStatus | null>(null)

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      reportType: "WEEKLY",
      sendReportTo: "",
      endTravelDate: new Date(),
      endVendorDate: new Date(),
    },
  })

  async function onSubmit(data: FormValues) {
    setIsLoading(true)
    setBookings([])
    setReportStatus(null)

    try {
      const query = `
        query WeeklyMexicanBookings {
          weeklyMexicanBookings(params: {
            reportType: "${data.reportType}",
            sendReportTo: "${data.sendReportTo}",
            endTravelDate: "${format(data.endTravelDate, "yyyy-MM-dd")}",
            endVendorDate: "${format(data.endVendorDate, "yyyy-MM-dd")}"
          }) {
            status {
              sent
              message
              recipient
            }
            bookings {
              orderId
              serviceType
              createdDate
              salesCurrency
              totalCharges
              supplier
              bookingType
              totalBookings
            }
          }
        }
      `

      const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || "", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({ query }),
      })

      const result = await response.json()
      
      if (result.data?.weeklyMexicanBookings) {
        setBookings(result.data.weeklyMexicanBookings.bookings || [])
        setReportStatus(result.data.weeklyMexicanBookings.status)
      }
    } catch (error) {
      console.error("Error fetching Mexican bookings:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <Card className="p-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <div className="grid grid-cols-1 gap-6 md:grid-cols-2">
              <FormField
                control={form.control}
                name="reportType"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Report Type</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select report type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="WEEKLY">Weekly</SelectItem>
                        <SelectItem value="MONTHLY">Monthly</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="sendReportTo"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Send Report To</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} />
                    </FormControl>
                    <FormDescription>
                      Email address to receive the report
                    </FormDescription>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endTravelDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Travel Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="endVendorDate"
                render={({ field }) => (
                  <FormItem className="flex flex-col">
                    <FormLabel>End Vendor Date</FormLabel>
                    <Popover>
                      <PopoverTrigger asChild>
                        <FormControl>
                          <Button
                            variant={"outline"}
                            className={cn(
                              "w-full pl-3 text-left font-normal",
                              !field.value && "text-muted-foreground"
                            )}
                          >
                            {field.value ? (
                              format(field.value, "PPP")
                            ) : (
                              <span>Pick a date</span>
                            )}
                            <CalendarIcon className="ml-auto h-4 w-4 opacity-50" />
                          </Button>
                        </FormControl>
                      </PopoverTrigger>
                      <PopoverContent className="w-auto p-0" align="start">
                        <Calendar
                          mode="single"
                          selected={field.value}
                          onSelect={field.onChange}
                          initialFocus
                        />
                      </PopoverContent>
                    </Popover>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <Button type="submit" disabled={isLoading}>
              {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
              Generate Report
            </Button>
          </form>
        </Form>
      </Card>

      {reportStatus && (
        <Alert variant={reportStatus.sent ? "default" : "destructive"}>
          {reportStatus.sent ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertTitle>
            {reportStatus.sent ? "Report Sent" : "Error Sending Report"}
          </AlertTitle>
          <AlertDescription>
            {reportStatus.message}
            {reportStatus.sent && reportStatus.recipient && (
              <span className="block mt-1">
                Sent to: <strong>{reportStatus.recipient}</strong>
              </span>
            )}
          </AlertDescription>
        </Alert>
      )}

      {bookings.length > 0 && (
        <MexicanBookingsTable bookings={bookings} />
      )}
    </div>
  )
} 