"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { Label } from "@/app/components/ui/label"

const emailOptions = [
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "j<PERSON><PERSON><EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
  "<EMAIL>",
]

export function MexicanBookingsForm() {
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // Handle form submission
  }

  return (
    <div className="p-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold">Mexican Bookings Report</h1>
      </div>

      <Card className="w-full bg-white rounded-none">
        <CardHeader className="border-b py-3">
          <h2 className="text-lg font-medium">Mexican Bookings to be Paid</h2>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label>Select the email address to receive the report:</Label>
              <Select name="SendReportTo" required>
                <SelectTrigger className="rounded-none">
                  <SelectValue placeholder="Select email address" />
                </SelectTrigger>
                <SelectContent>
                  {emailOptions.map((email) => (
                    <SelectItem key={email} value={email}>
                      {email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex justify-end pt-4">
              <Button type="submit" className="rounded-none">
                Generate Report
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 