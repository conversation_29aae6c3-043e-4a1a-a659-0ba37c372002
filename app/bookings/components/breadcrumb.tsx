"use client"

import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { cn } from "@/lib/utils"

type BreadcrumbProps = {
  segments: {
    title: string
    href?: string
  }[]
}

export function Breadcrumb({ segments }: BreadcrumbProps) {
  return (
    <nav aria-label="Breadcrumb" className="flex items-center text-sm">
      <ol className="flex items-center gap-2">
        {segments.map((segment, index) => {
          const isLast = index === segments.length - 1
          return (
            <li key={segment.title} className="flex items-center gap-2">
              {segment.href && !isLast ? (
                <Link href={segment.href} className="text-muted-foreground hover:text-foreground transition-colors">
                  {segment.title}
                </Link>
              ) : (
                <span className={cn(isLast ? "font-medium text-foreground" : "text-muted-foreground")}>
                  {segment.title}
                </span>
              )}
              {!isLast && <ChevronRight className="h-4 w-4 text-muted-foreground" />}
            </li>
          )
        })}
      </ol>
    </nav>
  )
} 