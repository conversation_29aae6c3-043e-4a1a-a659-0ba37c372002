import { CheckReconciliationTable } from "@/app/components/check-reconciliation/check-reconciliation-table"
import { CheckReconciliationHeader } from "@/app/components/check-reconciliation/check-reconciliation-header"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"
export default function CheckReconciliationPage() {
  return (
    <div className="h-full flex-1 flex-col space-y-8 p-8 md:flex">
      <Breadcrumb />
      <div className="flex items-center justify-between space-y-2">
        <div>
          <h2 className="text-2xl font-bold tracking-tight">Check Reconciliation</h2>
          <p className="text-muted-foreground">
            View and manage CTS vendor check reconciliation
          </p>
        </div>
      </div>

      <div className="space-y-4">
        <CheckReconciliationHeader />
        <CheckReconciliationTable />
      </div>
    </div>
  )
} 