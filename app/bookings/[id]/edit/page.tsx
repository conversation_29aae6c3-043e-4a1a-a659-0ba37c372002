import { Metadata } from "next"
import { BookingEditForm } from "@/app/components/booking-edit/booking-edit-form"
import { Breadcrumb } from "@/app/components/ui/breadcrumb"

export const metadata: Metadata = {
  title: "Edit Booking",
  description: "Edit booking details",
}

interface BookingEditPageProps {
  params: {
    id: string
  }
}

export default async function BookingEditPage({ params }: BookingEditPageProps) {
  const bookingId = parseInt(params.id)

  return (
    <div className=" mx-auto p-6 space-y-6">
      <div className="flex flex-col gap-2">
        <Breadcrumb
         
        />
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold tracking-tight">Edit Booking #{bookingId}</h1>
        </div>
      </div>

      <BookingEditForm bookingId={bookingId} />
    </div>
  )
} 