"use client"

import { <PERSON><PERSON> } from "@/app/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/app/components/ui/card"
import { Input } from "@/app/components/ui/input"
import { Label } from "@/app/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/app/components/ui/radio-group"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { useState } from "react"
import { useForm } from "react-hook-form"
import { Plus, RotateCcw, MinusCircle, ChevronDown, ChevronUp } from "lucide-react"

interface Supplier {
  id: number
  name: string
  fullPayment: boolean
  daysAcceptedPayment: number
}

const SAMPLE_DATA: Supplier[] = [
  { id: 1, name: "Global Travel Corp", fullPayment: true, daysAcceptedPayment: 30 },
  { id: 2, name: "Travel Solutions Ltd", fullPayment: false, daysAcceptedPayment: 45 },
  { id: 3, name: "World Tours Inc", fullPayment: true, daysAcceptedPayment: 15 },
]

export function MorSuppliersForm() {
  const [suppliers, setSuppliers] = useState<Supplier[]>(SAMPLE_DATA)
  const [isCollapsed, setIsCollapsed] = useState(false)
  const form = useForm()

  const onSubmit = (data: any) => {
    const newSupplier: Supplier = {
      id: suppliers.length + 1,
      name: data.supplier,
      fullPayment: data.fullPayment === "yes",
      daysAcceptedPayment: Number(data.daysAcceptedPayment),
    }
    setSuppliers([...suppliers, newSupplier])
    form.reset()
  }

  const handleDelete = (id: number) => {
    if (confirm("Are you sure you want to delete?")) {
      setSuppliers(suppliers.filter((supplier) => supplier.id !== id))
    }
  }

  return (
    <form onSubmit={form.handleSubmit(onSubmit)} className="w-full space-y-8">
      <Card className="w-full bg-white rounded-none shadow-none">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-4 border-b">
          <div className="flex items-center space-x-2">
            <Plus className="h-5 w-5" />
            <h2 className="text-lg font-semibold">Add New Supplier</h2>
          </div>
          <div className="flex items-center space-x-2">
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => setIsCollapsed(!isCollapsed)}
              className="h-8 w-8 p-0"
            >
              {isCollapsed ? (
                <ChevronDown className="h-4 w-4" />
              ) : (
                <ChevronUp className="h-4 w-4" />
              )}
              <span className="sr-only">
                {isCollapsed ? "Expand" : "Collapse"}
              </span>
            </Button>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={() => form.reset()}
              className="rounded-none w-24"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Reset
            </Button>
            <Button
              type="submit"
              size="sm"
              className="rounded-none w-24"
            >
              <Plus className="mr-2 h-4 w-4" />
              Submit
            </Button>
          </div>
        </CardHeader>
        {!isCollapsed && (
          <CardContent className="p-6">
            <div className="grid w-full gap-4">
              <div className="flex items-center gap-4">
                <Label className="w-48 text-[#3C36A9] font-bold">Supplier:</Label>
                <Input
                  name="supplier"
                  required
                  className="flex-1 rounded-none max-w-xl"
                />
              </div>

              <div className="flex items-center gap-4">
                <Label className="w-48 text-[#3C36A9] font-bold">
                  Full Payment:
                </Label>
                <RadioGroup name="fullPayment" className="flex gap-4" required>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="yes" id="yes" />
                    <Label htmlFor="yes">Yes</Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="no" id="no" />
                    <Label htmlFor="no">No</Label>
                  </div>
                </RadioGroup>
              </div>

              <div className="flex items-center gap-4">
                <Label className="w-48 text-[#3C36A9] font-bold">
                  Days Accepted Payment:
                </Label>
                <Input
                  type="number"
                  name="daysAcceptedPayment"
                  required
                  className="flex-1 rounded-none max-w-xl"
                />
              </div>
            </div>
          </CardContent>
        )}
      </Card>

      <div className="w-full">
        <div className="border rounded-none">
          <Table>
            <TableHeader>
              <TableRow className="bg-slate-50">
                <TableHead className="font-semibold">Sr.No</TableHead>
                <TableHead className="font-semibold">Supplier</TableHead>
                <TableHead className="font-semibold">Full Payment</TableHead>
                <TableHead className="font-semibold">Days Accepted Payment</TableHead>
                <TableHead className="font-semibold">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {suppliers.map((supplier) => (
                <TableRow key={supplier.id}>
                  <TableCell className="font-medium">{supplier.id}</TableCell>
                  <TableCell>{supplier.name}</TableCell>
                  <TableCell>{supplier.fullPayment ? "Yes" : "No"}</TableCell>
                  <TableCell>{supplier.daysAcceptedPayment}</TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      className="text-red-600 hover:text-red-800 p-0"
                      onClick={() => handleDelete(supplier.id)}
                    >
                      Delete
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </form>
  )
} 