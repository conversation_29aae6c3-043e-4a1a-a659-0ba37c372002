import { Breadcrumb } from "@/app/components/ui/breadcrumb"
import { Button } from "@/app/components/ui/button"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import Link from "next/link"

interface Supplier {
  id: number
  name: string
  type: string
  status: string
  lastUpdated: string
  createdBy: string
}

const SAMPLE_SUPPLIERS: Supplier[] = [
  { 
    id: 1, 
    name: "Global Travel Corp", 
    type: "Tour Operator", 
    status: "Active",
    lastUpdated: "2024-03-20",
    createdBy: "<EMAIL>"
  },
  { 
    id: 2, 
    name: "Travel Solutions Ltd", 
    type: "Hotel Chain", 
    status: "Active",
    lastUpdated: "2024-03-19",
    createdBy: "<EMAIL>"
  },
  { 
    id: 3, 
    name: "World Tours Inc", 
    type: "MOR", 
    status: "Active",
    lastUpdated: "2024-03-18",
    createdBy: "<EMAIL>"
  },
  { 
    id: 4, 
    name: "Cruise Lines International", 
    type: "Cruise", 
    status: "Active",
    lastUpdated: "2024-03-17",
    createdBy: "<EMAIL>"
  },
]

export default function SuppliersPage() {
  return (
    <div className="p-8 space-y-8">   
      <Breadcrumb />                    
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold text-[#000080]">
          InteleTravel Suppliers
        </h1>
        <Link href="/suppliers/new">
          <Button className="rounded-none bg-[#3C36A9]">
            Add New Supplier
          </Button>
        </Link>
      </div>

      <div className="border rounded-none">
        <Table>
          <TableHeader>
            <TableRow className="bg-slate-50">
              <TableHead className="font-semibold">ID</TableHead>
              <TableHead className="font-semibold">Supplier Name</TableHead>
              <TableHead className="font-semibold">Type</TableHead>
              <TableHead className="font-semibold">Status</TableHead>
              <TableHead className="font-semibold">Last Updated</TableHead>
              <TableHead className="font-semibold">Created By</TableHead>
              <TableHead className="font-semibold">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {SAMPLE_SUPPLIERS.map((supplier) => (
              <TableRow key={supplier.id} className="hover:bg-slate-50">
                <TableCell className="font-medium">{supplier.id}</TableCell>
                <TableCell>{supplier.name}</TableCell>
                <TableCell>{supplier.type}</TableCell>
                <TableCell>
                  <span className="inline-flex items-center rounded-full px-2 py-1 text-xs font-medium bg-green-50 text-green-700 ring-1 ring-inset ring-green-600/20">
                    {supplier.status}
                  </span>
                </TableCell>
                <TableCell>{supplier.lastUpdated}</TableCell>
                <TableCell>{supplier.createdBy}</TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 lg:px-3 text-blue-600 hover:text-blue-700"
                    >
                      Edit
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-8 px-2 lg:px-3 text-red-600 hover:text-red-700"
                    >
                      Delete
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  )
} 