"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { useRouter } from "next/navigation"

const supplierTypes = [
  "Tour Operator",
  "Hotel Chain",
  "MOR",
  "Cruise",
  "Airlines",
  "Car Rental",
]

export function NewSupplierForm() {
  const router = useRouter()

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    // Handle form submission
    router.push("/suppliers")
  }

  return (
    <div className="p-8 space-y-8">
      <div className="flex items-center justify-between mb-8">
        <h1 className="text-2xl font-semibold text-[#000080]">
          Add New Supplier
        </h1>
      </div>

      <Card className="w-full bg-white rounded-none">
        <CardHeader className="border-b py-3">
          <h2 className="text-lg font-medium">Supplier Details</h2>
        </CardHeader>
        <CardContent className="p-6">
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="grid gap-2">
                  <Label>Supplier Name</Label>
                  <Input
                    name="name"
                    required
                    className="rounded-none"
                    placeholder="Enter supplier name"
                  />
                </div>

                <div className="grid gap-2">
                  <Label>Supplier Type</Label>
                  <Select name="type" required>
                    <SelectTrigger className="rounded-none">
                      <SelectValue placeholder="Select supplier type" />
                    </SelectTrigger>
                    <SelectContent>
                      {supplierTypes.map((type) => (
                        <SelectItem key={type} value={type}>
                          {type}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="grid gap-2">
                  <Label>Contact Email</Label>
                  <Input
                    type="email"
                    name="email"
                    required
                    className="rounded-none"
                    placeholder="Enter contact email"
                  />
                </div>

                <div className="grid gap-2">
                  <Label>Contact Phone</Label>
                  <Input
                    type="tel"
                    name="phone"
                    className="rounded-none"
                    placeholder="Enter contact phone"
                  />
                </div>
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Button
                type="button"
                variant="outline"
                className="rounded-none"
                onClick={() => router.push("/suppliers")}
              >
                Cancel
              </Button>
              <Button type="submit" className="rounded-none bg-[#3C36A9]">
                Add Supplier
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </div>
  )
} 