import { Metadata } from "next"
import { Card } from "@/app/components/ui/card"
import { PageHeader } from "@/app/components/ui/page-header"
import { DashboardStats } from "@/app/components/dashboard/dashboard-stats"
import { BookingStats } from "@/app/components/dashboard/revenue-chart"
import { RecentActivities } from "@/app/components/dashboard/recent-activities"
import { UserAnalytics } from "@/app/components/dashboard/user-analytics"
import { AgentCommission } from "@/app/components/dashboard/agent-commission"

export const metadata: Metadata = {
  title: "Dashboard",
  description: "Main dashboard with overview statistics and charts",
}

interface HomePageProps {
  searchParams: { [key: string]: string }
}

export default async function HomePage({ searchParams }: HomePageProps) {
  return (
    <div className="flex-1 space-y-4 p-4 md:p-8 pt-6">
      <PageHeader 
        title="Dashboard" 
        description="Overview of your business metrics and performance"
      />
      
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <DashboardStats />
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Card className="col-span-4">
          <BookingStats />
        </Card>
        
        <Card className="col-span-3">
          <div className="p-6">
            <RecentActivities />
          </div>
        </Card>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <UserAnalytics />
        <Card className="col-span-4">
          <div className="p-6">
            <RecentActivities />
          </div>
        </Card>
      </div>

      <div className="grid gap-4">
        <AgentCommission />
      </div>
    </div>
  )
}
