import { backendAPI } from '@/lib/axios-instance';

export async function getProfile() {
  try {
    const response = await backendAPI.get('/profile');
    return response.data;
  } catch (error) {
    console.error('Error fetching profile:', error);
    throw error;
  }
}

export async function updateProfile(data: unknown) {
  try {
    const response = await backendAPI.put('/profile', data);
    return response.data;
  } catch (error) {
    console.error('Error updating profile:', error);
    throw error;
  }
}