import { Environment, Network, RecordSource, Store } from 'relay-runtime'

async function fetchGraphQL(params: any, variables: any) {
  const response = await fetch('/api/graphql', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      query: params.text,
      variables,
    }),
  })

  return response.json()
}

function createRelayEnvironment() {
  return new Environment({
    network: Network.create(fetchGraphQL),
    store: new Store(new RecordSource()),
  })
}

let relayEnvironment: Environment | undefined

export function getEnvironment() {
  if (typeof window === 'undefined') {
    return createRelayEnvironment()
  }

  if (!relayEnvironment) {
    relayEnvironment = createRelayEnvironment()
  }

  return relayEnvironment
} 