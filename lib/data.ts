import { Transaction } from '../types/transaction'

export const initialTransactions: Transaction[] = [
  {
    id: "1",
    date: '2025-01-17',
    pin: 'UK44502846',
    name: '<PERSON>',
    amount: 143.00,
    type: 'Temporary Payment',
    paymentMethod: 'Checkout',
    result: 'Success',
    user: 'by the agent',
    comment: 'Customer Booking Payment'
  },
  {
    id: "2",
    date: '2025-01-18',
    pin: 'US55603957',
    name: '<PERSON>',
    amount: 250.50,
    type: 'Booking Payment',
    paymentMethod: 'Credit Card',
    result: 'Success',
    user: 'online',
    comment: 'Flight Booking'
  },
  {
    id: "3",
    date: '2025-01-19',
    pin: 'CA78901234',
    name: '<PERSON>',
    amount: 500.00,
    type: 'Temporary Payment',
    paymentMethod: 'PayPal',
    result: 'Failure',
    user: 'by the agent',
    comment: 'Hotel Reservation - Payment Failed'
  },
  {
    id: "4",
    date: '2025-01-20',
    pin: 'AU12345678',
    name: '<PERSON>',
    amount: 1200.75,
    type: 'Booking Payment',
    paymentMethod: 'Bank Transfer',
    result: 'Success',
    user: 'online',
    comment: 'Cruise Package Booking'
  },
] 