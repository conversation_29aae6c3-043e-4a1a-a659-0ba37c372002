import { Environment, Network, RecordSource, Store } from 'relay-runtime'

let relayEnvironment: Environment | undefined

async function fetchRelay(params: any, variables: any) {
  try {
    const response = await fetch(process.env.NEXT_PUBLIC_GRAPHQL_URL || 'http://localhost:4000/graphql', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        // Add any auth headers if needed
        // 'Authorization': `Bearer ${token}`,
      },
      body: JSON.stringify({
        query: params.text,
        variables,
      }),
    })

    if (!response.ok) {
      const errorText = await response.text()
      throw new Error(`Network error: ${response.status} - ${errorText}`)
    }

    const contentType = response.headers.get('content-type')
    if (!contentType?.includes('application/json')) {
      const text = await response.text()
      throw new Error(`Expected JSON response but got ${contentType}: ${text}`)
    }

    return response.json()
  } catch (error) {
    console.error('Relay fetch error:', error)
    throw error
  }
}

export function getEnvironment() {
  if (relayEnvironment) return relayEnvironment

  relayEnvironment = new Environment({
    network: Network.create(fetchRelay),
    store: new Store(new RecordSource()),
  })

  return relayEnvironment
}

// Export the environment directly for components that need it
export const RelayEnvironment = getEnvironment() 