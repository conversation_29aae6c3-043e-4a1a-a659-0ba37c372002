import { 
  Home, 
  Users,
  Search,
  CircleDollarSign,
  FileText,
  ClipboardList,
  Building2,
  GraduationCap
} from "lucide-react"

export const sidebarConfig = [
  {
    title: "Dashboard",
    href: "/dashboard",
    icon: Home
  },
  {
    title: "Agents",
    href: "/agents",
    icon: Users,
    items: [
      {
        title: "All Agents",
        href: "/agents",
        icon: Users
      },
      {
        title: "Agent Report",
        href: "/agents/report",
        icon: FileText
      },
      {
        title: "Coach Report",
        href: "/agents/coach-report",
        icon: FileText
      },
      {
        title: "Sellers Report",
        href: "/agents/sellers-report",
        icon: FileText
      },
      {
        title: "Bonus Commission",
        href: "/agents/bonus-commission",
        icon: CircleDollarSign
      },
      {
        title: "Trial Report",
        href: "/agents/trial-report",
        icon: ClipboardList
      }
    ]
  },
  {
    title: "Bookings",
    href: "/bookings",
    icon: Building2
  },
  {
    title: "Leads",
    href: "/leads",
    icon: Search
  },
  {
    title: "Commissions",
    href: "/commissions",
    icon: CircleDollarSign
  },
  {
    title: "Transactions",
    href: "/transactions",
    icon: ClipboardList
  }
] 