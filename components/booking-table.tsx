"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Checkbox } from "@/components/ui/checkbox"
import { Button } from "@/components/ui/button"
import { MoreHorizontal } from "lucide-react"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface Booking {
  iBookingID: number
  vcPIN: string
  vcConfirmationID: string
  vcClientName: string
  dTravelStartDate: string
  dTravelEndDate: string
  vcFinalDestination: string
}

interface BookingTableProps {
  data: Booking[]
}

export function BookingTable({ data }: BookingTableProps) {
  const [selectedRows, setSelectedRows] = useState<Record<number, boolean>>({})
  const [selectAll, setSelectAll] = useState(false)

  const toggleSelectAll = () => {
    setSelectAll(!selectAll)
    const newSelection: Record<number, boolean> = {}
    data.forEach(booking => {
      newSelection[booking.iBookingID] = !selectAll
    })
    setSelectedRows(newSelection)
  }

  const toggleRow = (id: number) => {
    setSelectedRows(prev => ({
      ...prev,
      [id]: !prev[id]
    }))
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>
              <Checkbox 
                checked={selectAll}
                onCheckedChange={toggleSelectAll}
                aria-label="Select all"
              />
            </TableHead>
            <TableHead>PIN</TableHead>
            <TableHead>Confirmation</TableHead>
            <TableHead>Client Name</TableHead>
            <TableHead>Start Date</TableHead>
            <TableHead>End Date</TableHead>
            <TableHead>Destination</TableHead>
            <TableHead>Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {data.map((booking) => (
            <TableRow 
              key={booking.iBookingID}
              data-state={selectedRows[booking.iBookingID] && "selected"}
            >
              <TableCell>
                <Checkbox
                  checked={selectedRows[booking.iBookingID]}
                  onCheckedChange={() => toggleRow(booking.iBookingID)}
                  aria-label="Select row"
                />
              </TableCell>
              <TableCell>{booking.vcPIN}</TableCell>
              <TableCell>{booking.vcConfirmationID}</TableCell>
              <TableCell>{booking.vcClientName}</TableCell>
              <TableCell>{booking.dTravelStartDate}</TableCell>
              <TableCell>{booking.dTravelEndDate}</TableCell>
              <TableCell>{booking.vcFinalDestination}</TableCell>
              <TableCell>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" className="h-8 w-8 p-0">
                      <span className="sr-only">Open menu</span>
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end">
                    <DropdownMenuItem onClick={() => navigator.clipboard.writeText(booking.vcPIN)}>
                      Copy PIN
                    </DropdownMenuItem>
                    <DropdownMenuItem>View Details</DropdownMenuItem>
                    <DropdownMenuItem>Edit Booking</DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 