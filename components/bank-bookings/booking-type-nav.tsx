"use client"

import { But<PERSON> } from "@/components/ui/button"
import { useRouter } from "next/navigation"

export function BookingTypeNav() {
  const router = useRouter()

  return (
    <div className="flex flex-wrap gap-2">
      <Button variant="outline" onClick={() => router.push("/agents/bookings/new")}>
        Enter New Booking
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/air")}>
        Air Bookings
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/car")}>
        Car Bookings
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/hotel")}>
        Hotel Bookings
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/cruise")}>
        Cruise Bookings
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/vacation")}>
        Vacation Bookings
      </Button>
      <Button variant="outline" onClick={() => router.push("/agents/bookings/insurance")}>
        Insurance Bookings
      </Button>
    </div>
  )
} 