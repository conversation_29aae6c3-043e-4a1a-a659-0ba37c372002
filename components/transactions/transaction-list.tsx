"use client"

import { useState } from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { TransactionStatus } from "./transaction-status"
import { formatCurrency, formatDate } from "@/app/lib/utils"
import { useTransactionSearch } from "@/app/hooks/use-transaction-search"

interface Transaction {
  id: string
  date: string
  description: string
  amount: number
  status: "completed" | "pending" | "failed"
  type: "credit" | "debit"
}

export function TransactionList() {
  const { transactions, isLoading } = useTransactionSearch()

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Date</TableHead>
            <TableHead>Description</TableHead>
            <TableHead>Amount</TableHead>
            <TableHead>Status</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {transactions?.map((transaction) => (
            <TableRow key={transaction.id}>
              <TableCell>{formatDate(transaction.date)}</TableCell>
              <TableCell>{transaction.description}</TableCell>
              <TableCell className={transaction.type === "credit" ? "text-green-600" : "text-red-600"}>
                {formatCurrency(transaction.amount)}
              </TableCell>
              <TableCell>
                <TransactionStatus status={transaction.status} />
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  )
} 