"use client"

import { useSearchParams } from "next/navigation"
import { useTransactionFilters } from "@/app/hooks/use-transaction-filters"
import { Card, CardContent, CardHeader, CardTitle } from "@/app/components/ui/card"
import { Label } from "@/app/components/ui/label"
import { Input } from "@/app/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/app/components/ui/select"
import { DateRangePicker } from "@/app/components/date-range-picker"

export function TransactionFilters() {
  const { filters, updateFilters } = useTransactionFilters()

  return (
    <Card>
      <CardHeader>
        <CardTitle>Filters</CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label>Date Range</Label>
          <DateRangePicker
            value={filters.dateRange}
            onChange={(range) => updateFilters({ dateRange: range })}
          />
        </div>

        <div className="space-y-2">
          <Label>Status</Label>
          <Select
            value={filters.status}
            onValueChange={(value) => updateFilters({ status: value })}
          >
            <SelectTrigger>
              <SelectValue placeholder="Select status" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All</SelectItem>
              <SelectItem value="completed">Completed</SelectItem>
              <SelectItem value="pending">Pending</SelectItem>
              <SelectItem value="failed">Failed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <Label>Amount Range</Label>
          <div className="flex gap-2">
            <Input
              type="number"
              placeholder="Min"
              value={filters.minAmount}
              onChange={(e) => updateFilters({ minAmount: e.target.value })}
            />
            <Input
              type="number"
              placeholder="Max"
              value={filters.maxAmount}
              onChange={(e) => updateFilters({ maxAmount: e.target.value })}
            />
          </div>
        </div>
      </CardContent>
    </Card>
  )
} 