"use client"

import * as React from "react"
import { cn } from "@/lib/utils"

interface SelectionProps extends React.HTMLAttributes<HTMLDivElement> {
  selected?: boolean
}

export function Selection({ selected, className, ...props }: SelectionProps) {
  return (
    <div
      className={cn(
        "rounded-md border p-2",
        selected && "border-blue-500 bg-blue-50",
        className
      )}
      {...props}
    />
  )
}