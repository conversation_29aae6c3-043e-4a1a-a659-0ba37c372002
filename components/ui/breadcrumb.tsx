"use client"

import { ChevronRight, Home } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

interface BreadcrumbProps {
  homeElement?: boolean
  containerClasses?: string
}

export function Breadcrumb({ homeElement = true, containerClasses }: BreadcrumbProps) {
  const paths = usePathname()
  const pathElements = paths.split('/').filter((path) => path)

  return (
    <div className={`flex items-center space-x-1 text-sm text-muted-foreground ${containerClasses}`}>
      {homeElement && (
        <Link
          href="/"
          className="flex items-center hover:text-foreground transition-colors"
        >
          <Home className="h-4 w-4" />
        </Link>
      )}
      
      {pathElements.map((path, index) => {
        const href = `/${pathElements.slice(0, index + 1).join('/')}`
        const isLast = index === pathElements.length - 1
        const title = path.charAt(0).toUpperCase() + path.slice(1).replace(/-/g, ' ')

        return (
          <div key={path} className="flex items-center">
            <ChevronRight className="h-4 w-4 mx-1" />
            {isLast ? (
              <span className="font-medium text-foreground">{title}</span>
            ) : (
              <Link
                href={href}
                className="hover:text-foreground transition-colors"
              >
                {title}
              </Link>
            )}
          </div>
        )
      })}
    </div>
  )
} 