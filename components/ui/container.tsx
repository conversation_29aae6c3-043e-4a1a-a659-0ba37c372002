import * as React from "react"
import { cn } from "@/lib/utils"

// Replace the empty interface with a type alias
export type ContainerProps = React.HTMLAttributes<HTMLDivElement>

const Container = React.forwardRef<HTMLDivElement, ContainerProps>(
  ({ className, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("container mx-auto px-4 text-[#333]", className)}
        {...props}
      />
    )
  }
)
Container.displayName = "Container"

export { Container }