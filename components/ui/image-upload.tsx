"use client"

import { useState } from "react"
import { Input } from "./input"
import Image from "next/image"

interface ImageUploadProps {
  onUpload: (url: string) => void
  currentImage?: string
}

export function ImageUpload({ onUpload, currentImage }: ImageUploadProps) {
  const [preview, setPreview] = useState(currentImage)

  function handleFileChange(e: React.ChangeEvent<HTMLInputElement>) {
    const file = e.target.files?.[0]
    if (file) {
      // In production, implement actual file upload logic here
      const reader = new FileReader()
      reader.onloadend = () => {
        setPreview(reader.result as string)
        onUpload(reader.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  return (
    <div className="space-y-2">
      <Input type="file" accept="image/*" onChange={handleFileChange} />
      {preview && (
        <div className="relative w-[100px] h-[100px]">
          <Image
            src={preview}
            alt="Preview"
            fill
            className="object-contain"
            sizes="100px"
          />
        </div>
      )}
    </div>
  )
} 