"use client"

import { useEffect, useState } from "react"
import { useToast, toast as toastFn } from "./use-toast"

export function Toaster() {
  const { toasts, dismissToast } = useToast()
  const [mounted, setMounted] = useState(false)

  useEffect(() => {
    setMounted(true)

    const handleToast = (event: CustomEvent<{ title: string; description?: string; variant?: "default" | "destructive" }>) => {
      toastFn(event.detail)
    }

    window.addEventListener("toast", handleToast as EventListener)

    return () => {
      window.removeEventListener("toast", handleToast as EventListener)
    }
  }, [])

  if (!mounted) {
    return null
  }

  return (
    <div className="fixed bottom-0 right-0 z-50 p-4 space-y-4">
      {toasts.map((toast, index) => (
        <div
          key={index}
          className={`p-4 rounded-md shadow-md ${
            toast.variant === "destructive" ? "bg-red-500 text-white" : "bg-white text-black"
          }`}
        >
          <h3 className="font-bold">{toast.title}</h3>
          {toast.description && <p>{toast.description}</p>}
          <button
            onClick={() => dismissToast(index)}
            className="mt-2 text-sm underline"
          >
            Dismiss
          </button>
        </div>
      ))}
    </div>
  )
}