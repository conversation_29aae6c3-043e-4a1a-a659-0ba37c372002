"use client"

import React from "react"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/app/components/ui/table"
import { But<PERSON> } from "@/app/components/ui/button"
import { Input } from "@/app/components/ui/input"
import { Checkbox } from "@/app/components/ui/checkbox"
import { ChevronDown, ChevronUp, Download } from "lucide-react"
import { cn } from "@/app/lib/utils"

interface BaseTableProps<T> {
  data: T[]
  mainColumns: ColumnDef<T>[]
  detailColumns: ColumnDef<T>[]
  filterPlaceholder?: string
  topContent?: React.ReactNode
  onFilter?: (value: string) => void
}

interface ColumnDef<T> {
  id: string
  header: string | React.ReactNode
  cell: (row: T) => React.ReactNode
  className?: string
}

export function BaseTable<T extends { id: string }>({
  data,
  mainColumns,
  detailColumns,
  filterPlaceholder = "Filter...",
  topContent,
  onFilter,
}: BaseTableProps<T>) {
  const [expandedRows, setExpandedRows] = React.useState<Set<string>>(new Set())
  const [selectedRows, setSelectedRows] = React.useState<Set<string>>(new Set())
  const [filter, setFilter] = React.useState("")

  const toggleRow = (id: string) => {
    setExpandedRows(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }

  const handleSelectAll = () => {
    if (selectedRows.size === data.length) {
      setSelectedRows(new Set())
    } else {
      setSelectedRows(new Set(data.map(row => row.id)))
    }
  }

  const handleSelectRow = (id: string) => {
    setSelectedRows(prev => {
      const next = new Set(prev)
      if (next.has(id)) {
        next.delete(id)
      } else {
        next.add(id)
      }
      return next
    })
  }

  // Calculate equal width for content columns
  const utilityColumnsWidth = 120 // 40px * 3 for select, expand, and actions
  const contentColumnWidth = `${(100 - (utilityColumnsWidth / 10))/mainColumns.length}%`

  const columns = [
    {
      id: "select",
      header: (
        <Checkbox
          checked={selectedRows.size === data.length}
          onCheckedChange={handleSelectAll}
          aria-label="Select all"
        />
      ),
      cell: (row: T) => (
        <Checkbox
          checked={selectedRows.has(row.id)}
          onCheckedChange={() => handleSelectRow(row.id)}
          aria-label={`Select row ${row.id}`}
        />
      ),
      className: "w-[40px] sticky left-0 bg-background",
    },
    {
      id: "expand",
      header: "",
      cell: (row: T) => (
        <Button
          variant="ghost"
          size="icon"
          className="h-8 w-8"
          onClick={(e) => {
            e.stopPropagation()
            toggleRow(row.id)
          }}
        >
          <ChevronDown
            className={`h-4 w-4 transition-transform ${
              expandedRows.has(row.id) ? "rotate-180" : ""
            }`}
          />
        </Button>
      ),
      className: "w-[40px] sticky left-[40px] bg-background",
    },
    ...mainColumns.map(column => ({
      ...column,
      className: cn(column.className, `w-[${contentColumnWidth}]`),
    })),
    {
      id: "actions",
      header: "",
      cell: (row: T) => (
        <Button variant="ghost" size="icon" className="h-8 w-8">
          <Download className="h-4 w-4" />
          <span className="sr-only">Download</span>
        </Button>
      ),
      className: "w-[40px] sticky right-0 bg-background",
    },
  ]

  const handleFilter = (value: string) => {
    setFilter(value)
    onFilter?.(value)
  }

  return (
    <div className="space-y-4">
      {topContent}
      <div className="flex items-center justify-between">
        <Input
          placeholder={filterPlaceholder}
          className="max-w-sm"
          value={filter}
          onChange={(e) => handleFilter(e.target.value)}
        />
        {selectedRows.size > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">
              {selectedRows.size} selected
            </span>
            <Button variant="outline" size="sm">
              <Download className="h-4 w-4 mr-2" />
              Download Selected
            </Button>
          </div>
        )}
      </div>
      <div className="rounded-md border">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow>
                {columns.map(column => (
                  <TableHead 
                    key={column.id} 
                    className={column.className}
                  >
                    {column.header}
                  </TableHead>
                ))}
              </TableRow>
            </TableHeader>
            <TableBody>
              {data.map((row) => (
                <React.Fragment key={row.id}>
                  <TableRow>
                    {columns.map(column => (
                      <TableCell 
                        key={column.id} 
                        className={column.className}
                      >
                        {column.cell(row)}
                      </TableCell>
                    ))}
                  </TableRow>
                  {expandedRows.has(row.id) && (
                    <TableRow className="bg-muted/50">
                      <TableCell colSpan={columns.length}>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4 p-4">
                          {detailColumns.map(column => (
                            <div key={column.id} className="space-y-1">
                              <div className="text-sm text-muted-foreground">
                                {column.header}
                              </div>
                              {column.cell(row)}
                            </div>
                          ))}
                        </div>
                      </TableCell>
                    </TableRow>
                  )}
                </React.Fragment>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
    </div>
  )
} 