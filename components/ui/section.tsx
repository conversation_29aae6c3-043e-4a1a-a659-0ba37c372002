import * as React from "react"
import { cn } from "@/lib/utils"

// Removed the empty SectionProps interface

const Section = React.forwardRef<HTMLElement, React.HTMLAttributes<HTMLElement>>(
  ({ className, ...props }, ref) => {
    return (
      <section
        ref={ref}
        className={cn("mb-8", className)}
        {...props}
      />
    )
  }
)
Section.displayName = "Section"

export { Section }