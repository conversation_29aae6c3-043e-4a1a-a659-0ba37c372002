import React from 'react';

export const GradientText: React.FC<{ children: React.ReactNode }> = ({ children }) => (
  <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 to-indigo-600">
    {children}
  </span>
);

export const TextGenerateEffect: React.FC<{ words: string; className?: string }> = ({ words, className }) => (
  <p className={`transition-opacity duration-1000 ease-in-out ${className}`}>
    {words}
  </p>
);