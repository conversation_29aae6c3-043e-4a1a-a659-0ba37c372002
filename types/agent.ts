export interface Agent {
  iAgentID: string
  vcPIN: string
  vcFName: string
  vcLName: string
  vcEmail: string
  status: {
    vcStatus: string
  }
  group?: string
  referredBy?: string
  offer?: string
  landingPage?: string
  keyword?: string
}

export interface AgentTableProps {
  initialData: {
    agents: Agent[]
    total: number
    pageInfo: {
      currentPage: number
      totalPages: number
      hasNextPage: boolean
      hasPreviousPage: boolean
    }
  }
}

export interface AgentResponse {
  agents: Agent[]
  total: number
  pageInfo: {
    currentPage: number
    totalPages: number
    hasNextPage: boolean
    hasPreviousPage: boolean
  }
} 