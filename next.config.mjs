const path = require('path');

/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: true,
  compiler: {
    relay: {
      src: path.resolve(__dirname, 'app'),
      artifactDirectory: path.resolve(__dirname, 'app/__generated__'),
      language: 'typescript',
      eagerEsModules: true,
    },
  },
  webpack: (config, { isServer }) => {
    config.module.rules.push({
      test: /\.(js|jsx|ts|tsx)$/,
      exclude: /node_modules/,
      use: [
        {
          loader: 'babel-loader',
          options: {
            presets: ['next/babel'],
            plugins: ['relay']
          }
        }
      ]
    });
    return config;
  },
};

export default nextConfig;
