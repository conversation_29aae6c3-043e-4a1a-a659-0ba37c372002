{"name": "admin-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start -p 3002", "lint": "next lint", "update-schema": "get-graphql-schema http://localhost:4000/graphql > schema.graphql", "relay": "relay-compiler", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"@apollo/client": "^3.13.4", "@auth/core": "^0.34.2", "@clerk/nextjs": "^6.0.2", "@googlemaps/js-api-loader": "^1.16.8", "@graphql-tools/schema": "^10.0.16", "@heroicons/react": "^2.1.5", "@hookform/resolvers": "^3.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-avatar": "^1.1.1", "@radix-ui/react-checkbox": "^1.1.2", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.2", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.2", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.0.2", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.5", "@react-google-maps/api": "^2.20.5", "@tanstack/react-query": "^5.66.0", "@tanstack/react-table": "^8.9.3", "@tremor/react": "^3.18.7", "@types/uuid": "^9.0.8", "axios": "^1.7.7", "class-variance-authority": "^0.7.1", "clsx": "^2.0.0", "date-fns": "^3.6.0", "framer-motion": "^11.18.2", "graphql": "^16.10.0", "graphql-request": "^7.1.2", "lodash": "^4.17.21", "lucide-react": "^0.344.0", "next": "14.0.3", "next-auth": "^4.24.11", "next-themes": "^0.3.0", "react": "^18.2.0", "react-day-picker": "^9.5.1", "react-dom": "^18.2.0", "react-dropzone": "^14.3.5", "react-error-boundary": "^4.1.2", "react-hook-form": "^7.53.0", "react-hot-toast": "^2.4.1", "react-markdown": "^9.0.1", "react-relay": "^18.1.0", "react-select": "^5.8.1", "recharts": "^2.15.1", "relay-runtime": "^18.1.0", "sonner": "^2.0.1", "tailwind-merge": "^2.0.0", "tailwindcss-animate": "^1.0.7", "uuid": "^9.0.1", "zod": "^3.23.8"}, "devDependencies": {"@playwright/test": "^1.51.1", "@shadcn/ui": "^0.0.4", "@supabase/supabase-js": "^2.49.1", "@swc/core": "^1.11.4", "@swc/jest": "^0.2.37", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^14.3.1", "@types/google.maps": "^3.58.1", "@types/jest": "^29.5.14", "@types/node": "^20.0.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@types/react-relay": "^16.0.6", "@types/relay-runtime": "^18.1.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.12.0", "@typescript-eslint/parser": "^8.12.0", "autoprefixer": "^10.0.1", "babel-plugin-relay": "^15.0.0", "eslint": "^8", "eslint-config-next": "^14.2.12", "get-graphql-schema": "^2.1.2", "graphql-tag": "^2.12.6", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "postcss": "^8", "relay-compiler": "^15.0.0", "tailwindcss": "^3.3.0", "ts-node": "^10.9.2", "typescript": "^5.0.0"}, "eslintConfig": {"rules": {"no-unused-vars": 0, "@typescript-eslint/no-unused-vars": 0}}, "relay": {"src": "./app", "schema": "./schema.graphql", "language": "typescript", "artifactDirectory": "./app/__generated__"}}