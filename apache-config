<VirtualHost *:80>
    ServerName yourdomain.com
    
    DocumentRoot /var/admin-dashboard
    
    ProxyPreserveHost On
    
    # Important: Handle _next directory properly
    ProxyPass /_next http://localhost:3001/_next
    ProxyPassReverse /_next http://localhost:3001/_next
    
    # Handle API routes
    ProxyPass /api http://localhost:3001/api
    ProxyPassReverse /api http://localhost:3001/api
    
    # Handle all other routes
    ProxyPass / http://localhost:3001/
    ProxyPassReverse / http://localhost:3001/
    
    # Disable caching for development
    <Location />
        Header set Cache-Control "no-cache, no-store, must-revalidate"
        Header set Pragma "no-cache"
        Header set Expires 0
    </Location>
    
    ErrorLog ${APACHE_LOG_DIR}/concierge-error.log
    CustomLog ${APACHE_LOG_DIR}/concierge-access.log combined
</VirtualHost> 